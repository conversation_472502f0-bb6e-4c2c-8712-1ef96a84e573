import { Entity, Column, PrimaryGeneratedColumn, OneToMany } from 'typeorm';
import { LIMS_ZYWS_SAMPLE } from './LIMS_ZYWS_SAMPLE.entity';
import { GlobalModule } from '../../../global.module';

const schema = (
  GlobalModule?.config['database_lab_oracle']?.database || 'SYSTEM'
).toUpperCase();

@Entity({
  name: 'LIMS_ZYWS_MREGIST',
  schema,
  comment: '职业卫生实验室-福州检测项目表',
})
export class LIMS_ZYWS_MREGIST {
  @PrimaryGeneratedColumn({ type: 'number', comment: '登记序号' })
  REGGUID: number;

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 100,
    comment: '项目号+批次号',
  })
  REGNO: string;

  @Column({ type: 'varchar2', length: 100, comment: '任务编号' })
  WTSNO: string;

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 100,
    comment: '被采样单位代码(受检单位)',
  })
  ORGCODE: string;

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 100,
    comment: '被采样单位名称(受检单位)',
  })
  ORGNAME: string;

  @Column({
    type: 'date',
    nullable: true,
    comment: '采样时间',
  })
  SENDTIME: Date;

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 200,
    comment: '检品来源',
  })
  SAMPORIG: string;

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 200,
    comment: '详细地址',
  })
  ADDRESS: string;

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 200,
    comment: '联系人',
  })
  FLINKMAN: string;

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 200,
    comment: '联系电话',
  })
  FLINKTEL: string;

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 80,
    comment: '采收样人',
  })
  DEPTMAN: string;

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 50,
    comment: '操作员',
  })
  OPERATOR: string;

  @Column({
    type: 'date',
    nullable: true,
    comment: '操作时间',
  })
  OPERTIME: Date; //数据修改时间

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 200,
    comment: '备注',
  })
  REMARK: string;

  @OneToMany(() => LIMS_ZYWS_SAMPLE, (item) => item.REGGUID)
  LIMS_ZYWS_SAMPLE: LIMS_ZYWS_SAMPLE[];

  @Column({
    type: 'blob',
    nullable: true,
    comment: '检测报告',
  })
  ORIGNAL: Buffer;
}
