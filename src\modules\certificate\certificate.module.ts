import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Certificate } from './mysql/certificate.entity';
import { CertificateService } from './certificate.service';
import { CertificateController } from './certificate.controller';
// 资质证书
@Module({
  imports: [TypeOrmModule.forFeature([Certificate], 'mysqlConnectionIservice')],
  controllers: [CertificateController],
  providers: [CertificateService],
  exports: [CertificateService],
})
export class CertificateModule {}
