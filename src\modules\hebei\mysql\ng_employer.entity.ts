import { Entity, PrimaryGeneratedColumn, Column, OneToOne, JoinColumn, OneToMany, Index } from 'typeorm';
import { ngDeclaration } from './ng_declaration.entity';

@Entity('ng_employer')
export class NgEmployer {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
    comment: 'employer_id',
  })
  id: number;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: false,
    name: 'employer_name',
    comment: '用人单位名称',
  })
  employerName: string;

  @Index({ unique: true })  // 📌
  @Column({
    type: 'varchar',
    length: 18,
    nullable: false,
    name: 'unified_code',
    comment: '统一社会信用代码',
  })
  unifiedCode: string;

  @Column({
    type: 'bigint',
    nullable: true,
    name: 'district_id',
    comment: '所属地区id',
  })
  districtId: number;

  @Column({
    type: 'int',
    unsigned: true,
    nullable: true,
    name: 'economic_id',
    comment: '经济类型id',
  })
  economicId?: number;

  @Column({
    type: 'varchar',
    length: 12,
    nullable: true,
    name: 'industry_id',
    comment: '行业分类id',
  })
  industryId?: string;

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    name: 'registered_address',
    comment: '注册地址',
  })
  registeredAddress?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    name: 'legal_person_name',
    comment: '法人姓名',
  })
  legalPersonName?: string;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    name: 'legal_person_phone',
    comment: '法人电话',
  })
  legalPersonPhone?: string;

  @Column({
    type: 'tinyint',
    nullable: true,
    name: 'employer_scale',
    comment: '企业规模(1大2中3小4微)',
  })
  employerScale?: number;

  @Column({
    type: 'tinyint',
    nullable: true,
    name: 'employer_status',
    comment: '企业生产状态(0注销,1停产,2正常)',
  })
  employerStatus?: number;

  @Column({
    type: 'int',
    nullable: true,
    name: 'last_declared_id',
    comment: '最后申报成功id',
  })
  lastDeclaredId?: number;

  @OneToMany(() => ngDeclaration, (ngDeclaration) => ngDeclaration.employer)
  declarations: ngDeclaration[];

  @OneToOne(() => ngDeclaration, (ngDeclaration) => ngDeclaration.employer)
  @JoinColumn({ name: 'last_declared_id', referencedColumnName: 'id' })
  lastDeclaration: ngDeclaration;
}
