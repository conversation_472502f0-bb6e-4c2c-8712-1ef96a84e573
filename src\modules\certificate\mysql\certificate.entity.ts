import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

// 资质证书
@Entity('certificate')
export class Certificate {
  @PrimaryGeneratedColumn({ comment: '自增ID' })
  id: number;

  @Column({ comment: '所属用户ID', type: 'int' })
  user_id: number;

  @Column({ comment: '证书名称', type: 'varchar', length: 64 })
  name: string;

  @Column({ comment: '证书类型', type: 'varchar', length: 64, nullable: true })
  type?: string;

  @Column({ comment: '专业类别', type: 'varchar', length: 64, nullable: true })
  professional?: string;

  @Column({ comment: '证书编号', type: 'varchar', length: 64 })
  number: string;

  @Column({ comment: '发证机构', type: 'varchar', length: 255 })
  issuing_agency?: string;

  @Column({ comment: '获取的证书的时间', type: 'timestamp', nullable: true })
  get_time?: Date;

  @Column({ comment: '有效期至', type: 'timestamp', nullable: true })
  valid_time?: Date;

  @Column({
    comment: '附件',
    type: 'varchar',
    length: 255,
    nullable: true,
    default: '',
  })
  annex?: string;

  @CreateDateColumn({ comment: '创建时间', type: 'datetime' })
  created_at: Date;

  @UpdateDateColumn({ comment: '更新时间', type: 'datetime' })
  updated_at: Date;
}
