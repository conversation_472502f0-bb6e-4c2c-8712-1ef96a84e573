import { Entity, Column } from 'typeorm';

@Entity('jgjg_project')
export class PrJgjgProject {
  @Column({ type: 'varchar', length: 10, name: 'ID', primary: true })
  ID: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'ADDRESS' })
  ADDRESS: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'AREA' })
  AREA: string | null;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    name: 'CONTRACT_DATE',
  })
  CONTRACT_DATE: string | null;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    name: 'CONTRACT_NUMBER',
  })
  CONTRACT_NUMBER: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'INDUSTRY' })
  INDUSTRY: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'LINKMAN' })
  LINKMAN: string | null;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    name: 'NAME',
    comment: '用人单位名称',
  })
  NAME: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'PHONE' })
  PHONE: string | null;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    name: 'SERVICE_TYPE',
  })
  SERVICE_TYPE: string | null;

  @Column({ type: 'varchar', length: 19, nullable: true, name: 'CREATE_TIME' })
  CREATE_TIME: string | null;

  @Column({ type: 'varchar', length: 19, nullable: true, name: 'OPER_TIME' })
  OPER_TIME: string | null;

  @Column({ type: 'varchar', length: 1, nullable: true, name: 'IS_DELETE' })
  IS_DELETE: string | null;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    name: 'SERVICE_OPERNAME',
  })
  SERVICE_OPERNAME: string | null;

  @Column({
    type: 'varchar',
    length: 2,
    nullable: true,
    name: 'SERVICE_PROTYPE',
  })
  SERVICE_PROTYPE: string | null;

  @Column({
    type: 'varchar',
    length: 2,
    nullable: true,
    name: 'SERVICE_STATUS',
    comment: '1已签约2上报编辑中3已完成',
  })
  SERVICE_STATUS: string | null;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    name: 'SERVICE_SUBTIME',
  })
  SERVICE_SUBTIME: string | null;

  @Column({ type: 'longtext', nullable: true, name: 'CYYQ_NAME' })
  CYYQ_NAME: string | null;

  @Column({ type: 'longtext', nullable: true, name: 'JCJG' })
  JCJG: string | null;

  @Column({ type: 'longtext', nullable: true, name: 'JCJG_EXCEED' })
  JCJG_EXCEED: string | null;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    name: 'PROJECT_PERSON',
  })
  PROJECT_PERSON: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'REPORT_DATE' })
  REPORT_DATE: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'REPORT_EDIT' })
  REPORT_EDIT: string | null;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    name: 'REPORT_NUMBER',
  })
  REPORT_NUMBER: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'WTJC_NAME' })
  WTJC_NAME: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'WTJC_BL' })
  WTJC_BL: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'WTJC_YPBL' })
  WTJC_YPBL: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'WTJC_YPS' })
  WTJC_YPS: string | null;

  @Column({ type: 'longtext', nullable: true, name: 'YQ_ENDTIME' })
  YQ_ENDTIME: string | null;

  @Column({ type: 'longtext', nullable: true, name: 'YQ_STARTTIME' })
  YQ_STARTTIME: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'ZYBWHFL' })
  ZYBWHFL: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'FXJL_PD' })
  FXJL_PD: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'FXJL_SM' })
  FXJL_SM: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'FXJL_XM' })
  FXJL_XM: string | null;

  @Column({ type: 'longtext', nullable: true, name: 'PJJL' })
  PJJL: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'INPUT_AREA' })
  INPUT_AREA: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'INPUT_CITY' })
  INPUT_CITY: string | null;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    name: 'INPUT_PROVINCE',
  })
  INPUT_PROVINCE: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'OPERORGANID' })
  OPERORGANID: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'IS_JCJGCB' })
  IS_JCJGCB: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'IS_WTJCBL' })
  IS_WTJCBL: string | null;

  @Column({ type: 'varchar', length: 20, nullable: true, name: 'BG_FZYSPD' })
  BG_FZYSPD: string | null;

  @Column({ type: 'longtext', nullable: true, name: 'BG_FZYSWT' })
  BG_FZYSWT: string | null;

  @Column({ type: 'varchar', length: 20, nullable: true, name: 'BG_GRFHYPPD' })
  BG_GRFHYPPD: string | null;

  @Column({ type: 'longtext', nullable: true, name: 'BG_GRFHYPWT' })
  BG_GRFHYPWT: string | null;

  @Column({ type: 'varchar', length: 20, nullable: true, name: 'BG_JWZYPD' })
  BG_JWZYPD: string | null;

  @Column({ type: 'longtext', nullable: true, name: 'BG_JWZYWT' })
  BG_JWZYWT: string | null;

  @Column({ type: 'varchar', length: 20, nullable: true, name: 'BG_JZWSXPD' })
  BG_JZWSXPD: string | null;

  @Column({ type: 'longtext', nullable: true, name: 'BG_JZWSXWT' })
  BG_JZWSXWT: string | null;

  @Column({ type: 'varchar', length: 20, nullable: true, name: 'BG_SBBJPD' })
  BG_SBBJPD: string | null;

  @Column({ type: 'longtext', nullable: true, name: 'BG_SBBJWT' })
  BG_SBBJWT: string | null;

  @Column({ type: 'varchar', length: 20, nullable: true, name: 'BG_YJJYSSPD' })
  BG_YJJYSSPD: string | null;

  @Column({ type: 'longtext', nullable: true, name: 'BG_YJJYSSWT' })
  BG_YJJYSSWT: string | null;

  @Column({ type: 'varchar', length: 20, nullable: true, name: 'BG_ZTBJPD' })
  BG_ZTBJPD: string | null;

  @Column({ type: 'longtext', nullable: true, name: 'BG_ZTBJWT' })
  BG_ZTBJWT: string | null;

  @Column({ type: 'varchar', length: 20, nullable: true, name: 'BG_ZYBFHCSPD' })
  BG_ZYBFHCSPD: string | null;

  @Column({ type: 'longtext', nullable: true, name: 'BG_ZYBFHCSWT' })
  BG_ZYBFHCSWT: string | null;

  @Column({ type: 'varchar', length: 20, nullable: true, name: 'BG_ZYBWHGZPD' })
  BG_ZYBWHGZPD: string | null;

  @Column({ type: 'longtext', nullable: true, name: 'BG_ZYBWHGZWT' })
  BG_ZYBWHGZWT: string | null;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    name: 'BG_ZYBWHXMSBPD',
  })
  BG_ZYBWHXMSBPD: string | null;

  @Column({ type: 'longtext', nullable: true, name: 'BG_ZYBWHXMSBWT' })
  BG_ZYBWHXMSBWT: string | null;

  @Column({ type: 'varchar', length: 20, nullable: true, name: 'BG_ZYBWHYSPD' })
  BG_ZYBWHYSPD: string | null;

  @Column({ type: 'longtext', nullable: true, name: 'BG_ZYBWHYSWT' })
  BG_ZYBWHYSWT: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'XCCYPT' })
  XCCYPT: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'XCDCPT' })
  XCDCPT: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'IS_RYCF' })
  IS_RYCF: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'IS_SBCF' })
  IS_SBCF: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'ADDID' })
  ADDID: string | null;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    name: 'INPUT_AREAADDRESS',
  })
  INPUT_AREAADDRESS: string | null;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    name: 'INPUT_AREAAREA',
  })
  INPUT_AREAAREA: string | null;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    name: 'INPUT_CITYADDRESS',
  })
  INPUT_CITYADDRESS: string | null;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    name: 'INPUT_CITYAREA',
  })
  INPUT_CITYAREA: string | null;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    name: 'INPUT_PROVINCEADDRESS',
  })
  INPUT_PROVINCEADDRESS: string | null;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    name: 'INPUT_PROVINCEAREA',
  })
  INPUT_PROVINCEAREA: string | null;

  @Column({ type: 'longtext', nullable: true, name: 'JCJGWHYSTYPE' })
  JCJGWHYSTYPE: string | null;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    name: 'OVERWEIGHTINP',
  })
  OVERWEIGHTINP: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'QYGM' })
  QYGM: string | null;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    name: 'REALITYADDRESS',
  })
  REALITYADDRESS: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'REPORTID' })
  REPORTID: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'REPORTNO' })
  REPORTNO: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'REPORTYEAR' })
  REPORTYEAR: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'SBSSMC' })
  SBSSMC: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'SBSSMCISHG' })
  SBSSMCISHG: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'YPMC' })
  YPMC: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'YPMCISHG' })
  YPMCISHG: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'ZZYWFW' })
  ZZZYWFW: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'CREDITCODE' })
  CREDITCODE: string | null;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    name: 'PROJECTPHONE',
  })
  PROJECTPHONE: string | null;

  @Column({ type: 'longtext', nullable: true, name: 'JCJG_JCD' })
  JCJG_JCD: string | null;

  @Column({ type: 'longtext', nullable: true, name: 'JCJG_WHYS' })
  JCJG_WHYS: string | null;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    name: 'PROJECT_LEADER',
  })
  PROJECT_LEADER: string | null;
}
