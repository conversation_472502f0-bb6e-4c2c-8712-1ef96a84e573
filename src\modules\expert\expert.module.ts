import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Expert } from './mysql/expert.entity';
import { ExpertService } from './expert.service';
import { ExpertController } from './expert.controller';
import { CertificateModule } from '../certificate/certificate.module';
import { ExpertStatisticsService } from './expert-statistics.service';
import { ExpertReviewService } from './expert-review.service';
import { DictionaryModule } from '../dictionary/dictionary.module';
import { ExpertReview } from './mysql/expert_review.entity';
import { ExpertExtractionRecords } from './mysql/expert_extraction_records.entity';
import { ExpertExtractionRecordsService } from './expert-extraction-records.service';
import { ExpertWork } from './mysql/expert_work.entity';
import { ExpertWorkService } from './expert_work.service';

// 专家库
@Module({
  imports: [
    TypeOrmModule.forFeature(
      [Expert, ExpertReview, ExpertExtractionRecords, ExpertWork],
      'mysqlConnectionIservice',
    ),
    CertificateModule,
    DictionaryModule,
  ],
  controllers: [ExpertController],
  providers: [
    ExpertService,
    ExpertStatisticsService,
    ExpertReviewService,
    ExpertExtractionRecordsService,
    ExpertWorkService,
  ],
  exports: [ExpertService],
})
export class ExpertModule {}
