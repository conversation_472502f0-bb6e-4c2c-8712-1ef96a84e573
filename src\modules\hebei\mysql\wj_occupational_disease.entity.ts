import { Entity, Column, PrimaryColumn } from 'typeorm';

@Entity('wj_occupational_disease')
export class wjOccupationalDisease {
  @PrimaryColumn({ type: 'varchar', length: 64, comment: 'id' })
  id: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '报告卡编码',
  })
  report_card_code: string;

  @Column({
    type: 'varchar',
    length: 64,
    nullable: true,
    comment: '审核意见关联ID',
  })
  review_id: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '用人单位所在省',
  })
  employer_province: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '用人单位所在市',
  })
  employer_city: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '用人单位所在区县',
  })
  employer_county: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '用人单位所在地',
  })
  employer_location: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '用人单位所在地区编码',
  })
  employer_area_code: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '用人单位名称',
  })
  employer_name: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '统一社会信用代码',
  })
  employer_unified_code: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '二级代码',
  })
  secondary_code: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '企业类型',
  })
  enterprise_type: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '行业类别',
  })
  industry_category: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '行业类别编码',
  })
  industry_category_code: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '企业规模',
  })
  enterprise_scale: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '用人单位详细地址',
  })
  employer_address: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '用人单位地址邮编',
  })
  employer_postcode: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '用人单位联系人',
  })
  employer_contact: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '用人单位联系人电话',
  })
  employer_contact_phone: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '患者姓名',
  })
  patient_name: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '证件类型',
  })
  certificate_type: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '证件号码',
  })
  certificate_number: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '性别',
  })
  gender: string;

  @Column({
    type: 'date',
    nullable: true,
    comment: '出生日期',
  })
  date_of_birth: Date;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '劳动者联系方式',
  })
  worker_contact_info: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '职业病种类',
  })
  disease_type: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '其他职业病具体名称',
  })
  other_disease_name: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '其他危害因素具体名称',
  })
  other_hazardous_name: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '职业性化学中毒分类',
  })
  chemical_poisoning_category: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '职业病名称',
  })
  disease_name: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '病例类型',
  })
  case_type: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '接触的职业性有害因素',
  })
  exposed_hazardous_factors: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '接触的职业性有害因素编码',
  })
  exposed_hazardous_factors_code: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '统计工种',
  })
  statistical_job_type: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '统计工种编码',
  })
  statistical_job_type_code: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '其他工种',
  })
  other_job_type: string;

  @Column({
    type: 'date',
    nullable: true,
    comment: '死亡日期',
  })
  death_date: Date;

  @Column({
    type: 'varchar',
    nullable: true,
    length: 255,
    comment: '死亡原因',
  })
  death_cause: Date;

  @Column({
    type: 'date',
    nullable: true,
    comment: '诊断登记日期',
  })
  diagnosis_registration_date: Date;

  @Column({
    type: 'date',
    nullable: true,
    comment: '开始接害日期',
  })
  start_exposure_date: Date;

  @Column({
    type: 'int',
    nullable: true,
    comment: '专业工龄 - 年',
  })
  professional_service_years: number;

  @Column({
    type: 'int',
    nullable: true,
    comment: '专业工龄 - 月',
  })
  professional_service_months: number;

  @Column({
    type: 'int',
    nullable: true,
    comment: '专业工龄 - 日',
  })
  professional_service_days: number;

  @Column({
    type: 'int',
    nullable: true,
    comment: '专业工龄 - 时',
  })
  professional_service_hours: number;

  @Column({
    type: 'int',
    nullable: true,
    comment: '专业工龄 - 分',
  })
  professional_service_minutes: number;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '合并症 - 肺结核',
  })
  complication_tuberculosis: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '合并症 - 肺及支气管感染',
  })
  complication_pulmonary_and_bronchial_infection: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  complication_spontaneous_pneumothorax: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '合并症 - 肺心病',
  })
  complication_cor_pulmonale: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '合并症 - 肺癌',
  })
  complication_lung_cancer: string;

  @Column({
    type: 'date',
    nullable: true,
    comment: '诊断日期',
  })
  diagnosis_date: Date;

  @Column({
    type: 'date',
    nullable: true,
    comment: '尘肺病一期日期',
  })
  pneumoconiosis_phase1_date: Date;

  @Column({
    type: 'date',
    nullable: true,
    comment: '尘肺病二期日期',
  })
  pneumoconiosis_phase2_date: Date;

  @Column({
    type: 'date',
    nullable: true,
    comment: '尘肺病三期日期',
  })
  pneumoconiosis_phase3_date: Date;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '诊断单位',
  })
  diagnosis_unit: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '诊断单位负责人',
  })
  diagnosis_unit_responsible_person: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '诊断医师',
  })
  diagnosis_doctor: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '诊断医师所在单位',
  })
  diagnosis_doctor_unit: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '鉴定信息 - 报告卡编码',
  })
  appraisal_report_card_code: string;

  @Column({
    type: 'date',
    nullable: true,
    comment: '鉴定日期',
  })
  appraisal_date: Date;

  @Column({
    type: 'date',
    nullable: true,
    comment: '鉴定报告日期',
  })
  appraisal_report_date: Date;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '鉴定信息 - 职业病名称',
  })
  appraisal_disease_name: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '鉴定信息 - 职业病种类',
  })
  appraisal_disease_type: string;

  @Column({
    type: 'date',
    nullable: true,
    comment: '鉴定信息 - 尘肺一期日期',
  })
  appraisal_pneumoconiosis_phase1_date: Date;

  @Column({
    type: 'date',
    nullable: true,
    comment: '鉴定信息 - 尘肺二期日期',
  })
  appraisal_pneumoconiosis_phase2_date: Date;

  @Column({
    type: 'date',
    nullable: true,
    comment: '鉴定信息 - 尘肺三期日期',
  })
  appraisal_pneumoconiosis_phase3_date: Date;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '鉴定信息 - 尘肺病例类型',
  })
  appraisal_pneumoconiosis_case_type: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '鉴定信息 - 化学中毒分类',
  })
  appraisal_chemical_poisoning_category: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '填表人',
  })
  form_filler: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '填表人联系电话',
  })
  form_filler_phone: string;

  @Column({
    type: 'date',
    nullable: true,
    comment: '填表日期',
  })
  form_filling_date: Date;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '报告人',
  })
  reporter: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '报告人联系电话',
  })
  reporter_phone: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '报告单位',
  })
  reporting_unit: string;

  @Column({
    type: 'date',
    nullable: true,
    comment: '报告日期',
  })
  reporting_date: Date;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '审核状态',
  })
  review_status: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '审核意见',
  })
  review_opinion: string;

  @Column({
    type: 'varchar',
    length: 512,
    nullable: true,
    comment: '备注',
  })
  remarks: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '用工单位名称',
  })
  labor_using_unit_name: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '用工单位统一社会信用代码',
  })
  labor_unified_code: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '用工单位二级代码',
  })
  labor_secondary_code: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '用工单位所在地区编码',
  })
  labor_area_code: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '企业类型（用工单位）',
  })
  labor_type: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '企业规模（用工单位）',
  })
  labor_enterprise_scale: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '用工单位所在省',
  })
  labor_province: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '用工单位所在市',
  })
  labor_city: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '用工单位所在区县',
  })
  labor_county: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '用工单位所在地区',
  })
  labor_area: string;

  @Column({
    type: 'varchar',
    length: 32,
    comment: '行业类别（用工单位）',
  })
  labor_industry_name: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '行业类别编码（用工单位）',
  })
  labor_industry_code: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '暂停/退出诊断',
  })
  suspend_or_quit_diagnosis: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '是否有明确的诊断结论',
  })
  has_conclusion: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '拟诊断职业病种类',
  })
  proposed_disease_type: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '拟诊断职业病名称',
  })
  proposed_disease_name: string;

  @Column({
    type: 'date',
    comment: '创建日期',
  })
  creation_date: string;
}
