import {
  Entity,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ObjectIdColumn,
} from 'typeorm';
import { ObjectId } from 'mongodb';

@Entity('MedicalExamPlans')
export class MedicalExamPlan {
  @ObjectIdColumn()
  _id: ObjectId;

  @Column({ type: 'varchar', nullable: true, comment: '姓名' })
  name?: string;

  @Column({ type: 'varchar', nullable: true, comment: '联系方式' })
  contactPhone?: string;

  @Column({ type: 'varchar', nullable: true, comment: '身份证号' })
  idNumber?: string;

  @Column({
    type: 'enum',
    enum: [0, 1, 2],
    nullable: true,
    comment: '体检类别 0离岗 1岗前 2在岗'
  })
  examType?: number;

  @Column({
    type: 'enum',
    enum: [0, 1, 2, 3],
    nullable: true,
    comment: '预约状态 0未预约 1已预约(待审核) 2审核通过 3已拒绝'
  })
  reservationStatu?: number;

  @Column({ type: 'varchar', nullable: true, comment: '合同ID' })
  contractId?: string;

  @Column({ type: 'varchar', nullable: true, comment: '企业ID' })
  enterpriseId?: string;

  @Column({ type: 'varchar', nullable: true, comment: '员工ID' })
  employeeId?: string;

  @Column({ type: 'varchar', default: '', comment: '拒绝原因' })
  refuseReason: string;

  @Column({ type: 'timestamp', nullable: true, comment: '预约时间' })
  reservationDate?: Date;

  @Column({ type: 'varchar', nullable: true, comment: '体检登记ID' })
  healthCheckRegisterId?: string;

  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date;
}