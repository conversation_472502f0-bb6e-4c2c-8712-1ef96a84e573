import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { Repository } from 'typeorm';
// import { OnEvent } from '@nestjs/event-emitter';
// import { UNIFIED_ACCOUNT_CREATED } from '../unified-account/events/constants';
// import { AdminOrgsOrg } from '../unified-account/account-service/admin-orgs/admin-orgs-org.account.entity';
// import { AccountCreatedEvent } from '../unified-account/events/account-created-event';
import { UnifiedAccountService } from '../unified-account/unified-account.service';
// import { END_POINT } from 'src/constants/endPoint';
import { ngDeclaration } from './mysql/ng_declaration.entity';
import { DimDistrict } from './mysql/dim_district.entity'; // 区域字典
import { dimEconomic } from './mysql/dim_economic.entity';
import { dimIndustry } from './mysql/dim_industry.entity';
import { ngAudit } from './mysql/ng_audit.entity';
import { ngProduct } from './mysql/ng_product.entity';
import { ngRoster } from './mysql/ng_roster.entity';
import { ngHazardExposed } from './mysql/ng_hazard_exposed.entity';
import { ngHazardDetect } from './mysql/ng_hazard_detect.entity';
import { ngHazardExam } from './mysql/ng_hazard_exam.entity';
import { dimEmployerHazard } from './mysql/dim_employer_hazard.entity';
import { tjHealthCompany } from './mysql/tj_health_company.entity';
import { tjTechnicalqualityCompany } from './mysql/tj_technicalquality_company.entity';
// import { QueryDimEmployerHazard } from './dto/queryDimEmployerHazard';
import { NgUser } from './mysql/ng_user.entity';
import { ngSupervisor } from './mysql/ng_supervisor.entity';
import { ngMonitor } from './mysql/ng_monitor.entity';
import { DimEmployerIndustry } from './mysql/dim_employer_industry.entity';
import { dimHazard } from './mysql/dim_hazard.entity';
import { NgEmployer } from './mysql/ng_employer.entity';
import { PrJgjgProject } from './mysql/pr_jgjg_project.entity';
import { groupBy, sortBy, take } from 'lodash';
// import { ngSupervisor } from './mysql/ng_supervisor.entity';
import * as moment from 'moment';

@Injectable()
export class QyOnlineDeclarationService {
  constructor(
    @InjectRepository(ngDeclaration, 'mysqlConnectionHebei')
    private readonly ngDeclarationRepository: Repository<ngDeclaration>,
    @InjectRepository(DimDistrict, 'mysqlConnectionHebei')
    private readonly districtDictRepository: Repository<DimDistrict>,
    @InjectRepository(dimEconomic, 'mysqlConnectionHebei')
    private readonly dimEconomicRepository: Repository<dimEconomic>,
    @InjectRepository(dimIndustry, 'mysqlConnectionHebei')
    private readonly dimIndustryRepository: Repository<dimIndustry>,
    @InjectRepository(ngAudit, 'mysqlConnectionHebei')
    private readonly ngAuditRepository: Repository<ngAudit>,
    @InjectRepository(ngProduct, 'mysqlConnectionHebei')
    private readonly ngProductRepository: Repository<ngProduct>,
    @InjectRepository(ngRoster, 'mysqlConnectionHebei')
    private readonly ngRosterRepository: Repository<ngRoster>,
    @InjectRepository(ngHazardExposed, 'mysqlConnectionHebei')
    private readonly ngHazardExposedRepository: Repository<ngHazardExposed>,
    @InjectRepository(ngHazardDetect, 'mysqlConnectionHebei')
    private readonly ngHazardDetectRepository: Repository<ngHazardDetect>,
    @InjectRepository(ngHazardExam, 'mysqlConnectionHebei')
    private readonly ngHazardExamRepository: Repository<ngHazardExam>,
    @InjectRepository(dimEmployerHazard, 'mysqlConnectionHebei')
    private readonly dimEmployerHazardRepository: Repository<dimEmployerHazard>,
    @InjectRepository(DimEmployerIndustry, 'mysqlConnectionHebei')
    private readonly DimEmployerIndustryRepository: Repository<DimEmployerIndustry>,
    @InjectRepository(dimHazard, 'mysqlConnectionHebei')
    private readonly dimHazardRepository: Repository<dimHazard>,
    @InjectRepository(DimDistrict, 'mysqlConnectionHebei')
    private readonly DimDistrictRepository: Repository<DimDistrict>,
    @InjectRepository(NgEmployer, 'mysqlConnectionHebei')
    private readonly NgEmployerRepository: Repository<NgEmployer>,
    @InjectRepository(PrJgjgProject, 'mysqlConnectionHebeiPr')
    private readonly PrJgjgProjectRepository: Repository<PrJgjgProject>,
    @InjectRepository(tjHealthCompany, 'mysqlConnectionHebeiTy')
    private readonly tjHealthCompanyRepository: Repository<tjHealthCompany>,
    @InjectRepository(tjTechnicalqualityCompany, 'mysqlConnectionHebeiTy')
    private readonly tjTechnicalqualityCompanyRepository: Repository<tjTechnicalqualityCompany>,
    @InjectRepository(ngSupervisor, 'mysqlConnectionHebei')
    private readonly ngSupervisorRepository: Repository<ngSupervisor>,
    private readonly configService: ConfigService,
    private readonly unifiedAccountService: UnifiedAccountService,
    @InjectRepository(NgUser, 'mysqlConnectionHebei')
    private readonly ngUserRepository: Repository<NgUser>,
    @InjectRepository(ngMonitor, 'mysqlConnectionHebei')
    private readonly ngMonitorRepository: Repository<ngMonitor>,
  ) {}
  private readonly logger = new Logger(QyOnlineDeclarationService.name);

  async qyOnlineDeclarationList(data) {
    const { matchQuery = {}, pagination = {} } = data;

    const currentPage = pagination.currentPage ? pagination.currentPage : 1;
    const pageSize = pagination.pageSize ? pagination.pageSize : 99999;

    const whereData: any = {
      del_flag: 0,
    };
    if (matchQuery.unified_code) {
      whereData.unified_code = matchQuery.unified_code;
    }
    if (matchQuery.employer_name) {
      whereData.employer_name = matchQuery.employer_name;
    }
    if (matchQuery.declaration_type) {
      whereData.declaration_type = matchQuery.declaration_type;
    }

    let whereReport: any = {}; // 后置筛选
    if (matchQuery.isReport) {
      whereReport = 'ng_audit.id IS NOT NULL';
      whereReport = 'ng_audit.id IS NOT NULL';
    }

    // 获取数据
    const docs = await this.ngDeclarationRepository
      .createQueryBuilder('ng_declaration')
      .where(whereData)
      .select([
        'ng_declaration.id',
        'ng_declaration.declaration_type',
        'ng_declaration.declaration_year',
        'ng_declaration.declaration_date',
        'ng_declaration.employer_name',
        'ng_declaration.unified_code',
        'ng_declaration.filler',
        'ng_declaration.contactor',
        'ng_declaration.contactor_phone',
        'ng_declaration.sync_status',
      ])
      .andWhere(whereReport)
      .leftJoinAndSelect(
        ngAudit,
        'ng_audit',
        'ng_audit.id = ng_declaration.last_audit_id',
      )
      .orderBy('last_update_time', 'DESC')
      // .orderBy('declaration_date', 'DESC')
      .limit(pageSize) // 每页返回的条数
      .offset((currentPage - 1) * pageSize) // 每页返回的条数
      .getRawMany();

    // 获取总数
    const count = await this.ngDeclarationRepository
      .createQueryBuilder('ng_declaration')
      .where(whereData)
      .getCount();

    return {
      docs,
      count,
    };
  }

  async getOnlineDeclaration(data) {
    // 获取数据
    const docs = await this.ngDeclarationRepository.findOne({
      where: { id: data.id },
    });
    return docs;
  }

  async saveOnlineDeclaration(data) {
    const newUser = this.ngDeclarationRepository.create(data);
    const res = await this.ngDeclarationRepository.save(newUser);
    return res;
  }

  async updateOnlineDeclaration(data) {
    const { formData, id } = data;

    await this.ngDeclarationRepository
      .createQueryBuilder('ng_declaration')
      .update(ngDeclaration) // 指定要更新的实体
      .set(formData) // 设置要更新的字段
      .where('id = :id', { id }) // 添加更新条件
      .execute(); // 执行更新
  }

  async delByIdOnlineDeclaration(id) {
    const res = await this.ngDeclarationRepository.delete({ id });
    return res;
  }

  async getQyEconomicTypes() {
    const docs = await this.dimEconomicRepository
      .createQueryBuilder('dim_economic')
      .where('dim_economic.id IS NOT NULL')
      .getMany();
    return docs;
  }

  async getQyIndustryCategorys() {
    const docs = await this.DimEmployerIndustryRepository.find({});
    return docs;
  }

  async updateQyProduct(data) {
    const { formData } = data;
    await this.ngProductRepository.save(formData);
    return;
  }

  async getQyProduct(query) {
    const { id } = query;
    const res = await this.ngProductRepository
      .createQueryBuilder('ng_product')
      .where({ declaration_id: id })
      .getMany();

    return res;
  }

  async deleteQyProduct(query) {
    const { id, declaration_id } = query;
    const delparams: any = {};
    if (id) {
      delparams.id = id;
    }
    if (declaration_id) {
      delparams.declaration_id = declaration_id;
    }
    const res = await this.ngProductRepository.delete(delparams);
    if (res.affected) {
      return {
        code: 200,
        type: 'success',
        message: '删除成功',
      };
    }
    return {
      code: 500,
      type: 'warning',
      message: '删除失败',
    };
  }

  async updateQyNgRoster(body) {
    const { personData, id } = body;

    const newDate = personData.map((e) => {
      return {
        declaration_id: Number(id),
        emp_name: e.name,
        dept: e.dingtree,
        id_card_no: e.IDNum,
        train_time: e.trainTime ? new Date(e.trainTime) : null,
        train_type: e.trainType,
        train_method: e.trainFun,
        comm_train_inst: e.trainOrg,
        category: e.personType,
      };
    });

    // 先批量删除，然后批量新增
    await this.ngRosterRepository.delete({ declaration_id: id });

    await this.ngRosterRepository
      .createQueryBuilder('ng_roster')
      .insert()
      .into(ngRoster)
      .values(newDate)
      .execute();

    // 更新危害申报表人数
    // 处理人员类别
    const trainPeopleMap = {
      主要负责人: 0,
      职业卫生管理人员: 0,
      接害劳动者: 0,
    };
    // 接害人数只统计接害劳动者人数
    let exposed_num = 0;

    for (const trainData of personData) {
      if (
        trainPeopleMap.hasOwnProperty(trainData.personType) &&
        trainData.trainTime
      ) {
        trainPeopleMap[trainData.personType]++;
      }

      if (trainData.personType === '接害劳动者') {
        exposed_num++;
      }
    }

    const updateParmas = {
      has_leader_training: trainPeopleMap['主要负责人'] ? 1 : 0,
      has_manager_training: trainPeopleMap['职业卫生管理人员'] ? 1 : 0,
      employee_training_num: trainPeopleMap['接害劳动者'],
      exposed_num,
    };

    const updateRes = await this.ngDeclarationRepository
      .createQueryBuilder('ng_declaration')
      .update(ngDeclaration) // 指定要更新的实体
      .set(updateParmas) // 设置要更新的字段
      .where('id = :id', { id }) // 添加更新条件
      .execute(); // 执行更新

    if (updateRes.affected) {
      return updateParmas;
    }
    return;
  }

  async delQyNgRoster(query) {
    const { id, declaration_id } = query;
    const delparams: any = {};
    if (id) {
      delparams.id = id;
    }
    if (declaration_id) {
      delparams.declaration_id = declaration_id;
    }
    const res = await this.ngRosterRepository.delete(delparams);
    if (res.affected) {
      return {
        code: 200,
        type: 'success',
        message: '删除成功',
      };
    }
    return {
      code: 500,
      type: 'warning',
      message: '删除失败',
    };
  }

  async getQyNgRosterList(body) {
    const { id, pageInfo } = body;
    // 分页
    const res = await this.ngRosterRepository
      .createQueryBuilder('ng_roster')
      .where({ declaration_id: id })
      .orderBy('ng_roster.id', 'ASC')
      .skip((pageInfo.currentPage - 1) * pageInfo.pageSize) // 设置偏移量
      .take(pageInfo.pageSize) // 每页返回条数
      .getManyAndCount();

    const [docs, total] = res;

    return {
      docs,
      total,
    };
  }

  async updateHazardExposed(body) {
    const { formData } = body;
    const result = [];

    for (const item of formData) {
      const existingData = await this.ngHazardExposedRepository.findOne({
        where: {
          declaration_id: Number(item.declaration_id),
          hazard_factor: item.hazard_factor,
        },
      });

      if (existingData) {
        // 如果数据存在则更新
        existingData.total_exposed = item.total_exposed;
        result.push(await this.ngHazardExposedRepository.save(existingData));
      } else {
        // 如果数据不存在则新增
        const newData = this.ngHazardExposedRepository.create(item);
        result.push(await this.ngHazardExposedRepository.save(newData));
      }
    }
    return result;
  }

  async getHazardExposed(query) {
    const { id } = query;

    const data = await this.ngHazardExposedRepository.find({
      where: {
        declaration_id: Number(id),
      },
    });

    return data;
  }

  async deleteHazardExposedByIds(ids) {
    const res = await this.ngHazardExposedRepository.delete(ids);
    return res;
  }

  async updateHazardDetect(body) {
    const { formData } = body;
    const result = [];

    for (const item of formData) {
      const existingData = await this.ngHazardDetectRepository.findOne({
        where: {
          declaration_id: Number(item.declaration_id),
          hazard_factor: item.hazard_factor,
        },
      });

      if (existingData) {
        // 如果数据存在则更新
        existingData.detection_points = item.detection_points;
        existingData.overlimit_points = item.overlimit_points;
        result.push(await this.ngHazardDetectRepository.save(existingData));
      } else {
        // 如果数据不存在则新增
        const newData = await this.ngHazardDetectRepository.create(item);
        result.push(await this.ngHazardDetectRepository.save(newData));
      }
    }
    return result;
  }

  async deleteHazardDetectByIds(ids) {
    const res = await this.ngHazardDetectRepository.delete(ids);
    return res;
  }

  async getHazardDetect(query) {
    const { id } = query;

    const data = await this.ngHazardDetectRepository.find({
      where: {
        declaration_id: Number(id),
      },
    });

    return data;
  }

  async updateHazardExam(body) {
    const { formData } = body;
    const result = [];

    for (const item of formData) {
      const existingData = await this.ngHazardExamRepository.findOne({
        where: {
          declaration_id: Number(item.declaration_id),
          hazard_factor: item.hazard_factor,
        },
      });

      if (existingData) {
        // 如果数据存在则更新
        existingData.total_exam = item.total_exam;
        result.push(await this.ngHazardExamRepository.save(existingData));
      } else {
        // 如果数据不存在则新增
        const newData = await this.ngHazardExamRepository.create(item);
        result.push(await this.ngHazardExamRepository.save(newData));
      }
    }
    return result;
  }

  async deleteHazardExamByIds(ids) {
    const res = await this.ngHazardExamRepository.delete(ids);
    return res;
  }

  async getHazardExam(query) {
    const { id } = query;

    const data = await this.ngHazardExamRepository.find({
      where: {
        declaration_id: Number(id),
      },
    });

    return data;
  }

  async createNgAudit(body) {
    const { id } = body;
    const form = {
      declaration_id: Number(id),
      audit_status: 0,
      audit_time: null,
    };

    const newForm = this.ngAuditRepository.create(form);
    const res = await this.ngAuditRepository.save(newForm);

    if (res && res.id) {
      // 将审核表关联上申报表
      const updateData: any = { last_audit_id: res.id };
      await this.ngDeclarationRepository
        .createQueryBuilder('ng_declaration')
        .update(ngDeclaration) // 指定要更新的实体
        .set(updateData) // 设置要更新的字段
        .where('id = :id', { id }) // 添加更新条件
        .execute(); // 执行更新
    }
  }

  async getByIdNgAudit(id) {
    const whereQuery = {
      id,
    };

    const docs = await this.ngAuditRepository
      .createQueryBuilder('ng_audit')
      .where(whereQuery)
      .getOne();

    return docs;
  }

  async getHarmFactors(query) {
    const matchQuery: any = {};
    if (query.hazard_type) {
      matchQuery.hazard_type = query.hazard_type;
    }
    if (query.hazard_dec_type) {
      matchQuery.hazard_dec_type = query.hazard_dec_type;
    }

    const docs = await this.dimHazardRepository.find({
      where: matchQuery,
    });

    return docs;
  }

  async calculateQyknewtjfx(searchQuery) {
    const today = new Date().toLocaleDateString('zh-CN');
    const year = today.split('/')[0];
    let month = today.split('/')[1];
    month = month.padStart(2, '0');
    const currentYear = year;
    const lastYear = (Number(year) - 1).toString();
    const startDate = lastYear + '-' + month + '-01'; // 13个月前的第一天
    //(这边不知道为什么new date自动-8h,会取到每月倒数第二天，直接加个1天加回去)
    const endDate = new Date(Number(currentYear), Number(month), 1)
      .toISOString()
      .split('T')[0]; // 当前月的最后一天
    // 申报总数（今年的申报数量）
    const countCurrentYear = await this.ngDeclarationRepository
      .createQueryBuilder('entity')
      .select('COUNT(DISTINCT entity.unified_code)', 'count')
      .where('Year(entity.declaration_date) = :currentYear', { currentYear })
      .andWhere('entity.district_id LIKE :userDistrict', {
        userDistrict: `${searchQuery.userDistrict}%`,
      })
      .andWhere('entity.del_flag = 0')
      .getRawOne();

    // 申报增长率新
    const newData = await this.ngDeclarationRepository
      .createQueryBuilder('entity')
      .select('COUNT(DISTINCT entity.unified_code)', 'count')
      .where('Year(entity.declaration_date) = :currentYear', { currentYear })
      .andWhere('entity.district_id LIKE :userDistrict', {
        userDistrict: `${searchQuery.userDistrict}%`,
      })
      .andWhere('entity.declaration_type = 1')
      .andWhere('entity.del_flag = 0')
      .getRawOne();

    // 申报增长率旧
    const oldData = await this.ngDeclarationRepository
      .createQueryBuilder('entity')
      .leftJoinAndSelect(
        ngAudit,
        'ng_audit',
        'ng_audit.declaration_id = entity.last_audit_id',
      )
      .select('COUNT(DISTINCT entity.unified_code)', 'count')
      .where('Year(entity.declaration_date) < :currentYear', { currentYear })
      .andWhere('entity.district_id LIKE :userDistrict', {
        userDistrict: `${searchQuery.userDistrict}%`,
      })
      .andWhere('ng_audit.audit_status = 1')
      .andWhere('entity.del_flag = 0')
      .getRawOne();

    // 最近13个月的申报数量
    const result = await this.ngDeclarationRepository
      .createQueryBuilder('entity')
      .select([
        "DATE_FORMAT(entity.declaration_date, '%Y-%m') AS month", // 按月分组
        'COUNT(DISTINCT entity.unified_code) AS count', // 统计数量
      ])
      .where('entity.declaration_date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere('entity.district_id LIKE :userDistrict', {
        userDistrict: `${searchQuery.userDistrict}%`,
      })
      .andWhere('entity.del_flag = 0')
      .groupBy("DATE_FORMAT(entity.declaration_date, '%Y-%m')") // 按月分组
      .orderBy('month', 'ASC') // 按月份排序
      .getRawMany();

    // 补全没有数据的月份
    const months = [];
    const start = new Date(startDate); // 这里又不会给我自动转成零时区了
    const end = new Date(endDate);
    while (start <= end) {
      const year = start.getFullYear();
      const month = String(start.getMonth() + 1).padStart(2, '0'); // 补零
      months.push(`${year}-${month}`);
      start.setMonth(start.getMonth() + 1); // 增加一个月
    }

    const completeResult = months.map((month) => {
      const existing = result.find((item) => item.month === month);
      return {
        month,
        count: existing ? existing.count : 0, // 如果没有匹配，返回 0
      };
    });

    return {
      countCurrentYear,
      newData,
      oldData,
      completeResult,
    };
  }

  xxcxWhereSearch(queryBuilder, searchQuery) {
    if (searchQuery.sbnf) {
      queryBuilder.andWhere('entity.declaration_year = :sbnf', {
        sbnf: searchQuery.sbnf,
      });
    }

    if (searchQuery.sbrqStart && searchQuery.sbrqEnd) {
      queryBuilder.andWhere(
        'entity.declaration_date BETWEEN :sbrqStart AND :sbrqEnd',
        {
          sbrqStart: searchQuery.sbrqStart,
          sbrqEnd: searchQuery.sbrqEnd,
        },
      );
    }

    if (searchQuery.sblx) {
      queryBuilder.andWhere('entity.declaration_type = :sblx', {
        sblx: searchQuery.sblx,
      });
    }

    if (searchQuery.name) {
      queryBuilder.andWhere('entity.employer_name LIKE :name', {
        name: `%${searchQuery.name}%`,
      });
    }

    if (searchQuery.unifiedCode) {
      queryBuilder.andWhere('entity.unified_code LIKE :unifiedCode', {
        unifiedCode: `%${searchQuery.unifiedCode}%`,
      });
    }

    if (searchQuery.zgzrsCountCondition && searchQuery.zgzrseCount) {
      if (searchQuery.zgzrsCountCondition === 'lt') {
        queryBuilder.andWhere('entity.employee_num < :employeeNum', {
          employeeNum: searchQuery.zgzrseCount,
        });
      } else if (searchQuery.zgzrsCountCondition === 'gt') {
        queryBuilder.andWhere('entity.employee_num > :employeeNum', {
          employeeNum: searchQuery.zgzrseCount,
        });
      } else if (searchQuery.zgzrsCountCondition === 'range') {
        queryBuilder.andWhere(
          'entity.employee_num BETWEEN :zgzrsCountMin AND :zgzrsCountMax',
          {
            zgzrsCountMin: searchQuery.zgzrsCountMin || 0,
            zgzrsCountMax: searchQuery.zgzrsCountMax || 1000000000000,
          },
        );
      }
    }

    if (searchQuery.fr) {
      queryBuilder.andWhere('entity.legal_rep LIKE :fr', {
        fr: `%${searchQuery.fr}%`,
      });
    }

    if (searchQuery.hylb) {
      queryBuilder.leftJoinAndSelect(
        DimEmployerIndustry,
        'dim_industry',
        'dim_industry.code = entity.industry_id',
      );
      queryBuilder.andWhere('dim_industry.level LIKE :hylb', {
        hylb: `%${searchQuery.hylb}%`,
      });
    } else {
      queryBuilder.leftJoinAndSelect(
        DimEmployerIndustry,
        'dim_industry',
        'dim_industry.id = entity.gbz_industry_id',
      );
    }

    if (searchQuery.jjlx) {
      queryBuilder
        .leftJoinAndSelect(
          dimEconomic,
          'dim_economic',
          'dim_economic.id = entity.economic_id',
        )
        .andWhere('dim_economic.id = :economicType', {
          economicType: searchQuery.jjlx,
        });
    } else {
      queryBuilder.leftJoinAndSelect(
        dimEconomic,
        'dim_economic',
        'dim_economic.id = entity.economic_id',
      );
    }

    if (searchQuery.qygm) {
      queryBuilder.andWhere('entity.employer_scale = :qygm', {
        qygm: searchQuery.qygm,
      });
    }

    if (searchQuery.leaderTraining) {
      queryBuilder.andWhere('entity.has_leader_training = :leaderTraining', {
        leaderTraining: searchQuery.leaderTraining,
      });
    }

    if (searchQuery.managerTraining) {
      queryBuilder.andWhere('entity.has_manager_training = :managerTraining', {
        managerTraining: searchQuery.managerTraining,
      });
    }

    if (searchQuery.harmFactorStatus) {
      if (searchQuery.harmFactorStatus === '粉尘') {
        queryBuilder.andWhere('entity.has_dust_hazard = 1');
      } else if (searchQuery.harmFactorStatus === '化学因素') {
        queryBuilder.andWhere('entity.has_chem_hazard = 1');
      } else if (searchQuery.harmFactorStatus === '物理因素') {
        queryBuilder.andWhere('entity.has_phys_hazard = 1');
      } else if (searchQuery.harmFactorStatus === '放射性因素') {
        queryBuilder.andWhere('entity.has_radio_hazard = 1');
      } else if (searchQuery.harmFactorStatus === '生物因素') {
        queryBuilder.andWhere('entity.has_bio_hazard = 1');
      } else if (searchQuery.harmFactorStatus === '其他') {
        queryBuilder.andWhere('entity.has_other_hazard = 1');
      }
    }

    if (searchQuery.healthSupervision) {
      queryBuilder.andWhere('entity.exam_status = :healthSupervision', {
        healthSupervision: searchQuery.healthSupervision,
      });
    }

    if (searchQuery.harmFactorRegularDetection) {
      queryBuilder.andWhere(
        'entity.detect_type = :harmFactorRegularDetection',
        { harmFactorRegularDetection: searchQuery.harmFactorRegularDetection },
      );
    }

    if (searchQuery.statusRange === '1') {
      const subQuery = queryBuilder
        .subQuery()
        .select('MAX(sub.declaration_date)', 'maxDate')
        .addSelect('sub.unified_code', 'unified_code')
        .from('ng_declaration', 'sub')
        .groupBy('sub.unified_code')
        .getQuery();

      queryBuilder.innerJoin(
        `(${subQuery})`,
        'subQuery',
        'entity.declaration_date = subQuery.maxDate AND entity.unified_code = subQuery.unified_code',
      );
    }

    queryBuilder.andWhere('entity.del_flag = 0');

    queryBuilder
      .leftJoinAndSelect(
        ngAudit,
        'ng_audit',
        // 'ng_audit.declaration_id = entity.id'
        'ng_audit.id = entity.last_audit_id',
      )
      .andWhere('ng_audit.audit_status = 1');
    return queryBuilder;
  }

  async qyknewtjfx(searchQuery) {
    const { page = 1, size = 10 } = searchQuery;
    let queryBuilder =
      await this.ngDeclarationRepository.createQueryBuilder('entity');

    queryBuilder.andWhere('entity.district_id LIKE :userDistrict', {
      userDistrict: `${searchQuery.userDistrict}%`,
    });

    queryBuilder = this.xxcxWhereSearch(queryBuilder, searchQuery);

    if (searchQuery.ssdqmc) {
      queryBuilder.andWhere('entity.district_id LIKE :ssdqmc', {
        ssdqmc: `${searchQuery.ssdqmc.replace(/0{3}$|0{6}$|0{8}$|0{10}$/, '')}%`,
      });
    }

    if (searchQuery.economicStatus) {
      queryBuilder.leftJoinAndSelect(
        NgEmployer,
        'ng_employer',
        'ng_employer.unified_code = entity.unified_code',
      );
      queryBuilder.andWhere('ng_employer.employer_status = :economicStatus', {
        economicStatus: searchQuery.economicStatus,
      });
    }

    const selectFields = [
      'entity.id',
      'entity.employer_name',
      'entity.unified_code',
      'entity.employer_address',
      'dim_industry.name',
      'entity.declaration_type',
      'entity.declaration_year',
      'entity.declaration_date',
      'dim_economic.economic_type',
      'entity.legal_rep',
      'entity.legal_rep_phone',
    ];

    const result = await queryBuilder
      .clone()
      .select(selectFields)
      .orderBy('entity.declaration_date', 'DESC')
      .limit(size) // 每页返回的条数
      .offset((page - 1) * size) // 每页返回的条数
      .getRawMany();

    // 电话号码脱敏
    const maskedResult = result.map((item) => {
      if (item.entity_legal_rep_phone) {
        item.entity_legal_rep_phone =
          item.entity_legal_rep_phone.slice(0, 3) +
          '****' +
          item.entity_legal_rep_phone.slice(-4);
      }
      return item;
    });

    const totalRes = await queryBuilder
      .select('COUNT(*)', 'count')
      .getRawMany();
    const total = totalRes && totalRes[0] ? Number(totalRes[0].count) : 0;
    return {
      list: maskedResult,
      pagination: {
        page: page,
        size: size,
        total: total,
      },
    };
  }
  async getHealthTraintList(query) {
    const { page = 1, size = 10 } = query;
    const result = await this.ngRosterRepository
      .createQueryBuilder('ngRoster')
      .innerJoin(
        ngDeclaration,
        'ng_declaration',
        'ng_declaration.id = ngRoster.declaration_id',
      )
      .select('YEAR(ngRoster.train_time)', 'year')
      .addSelect(
        `SUM(CASE WHEN ngRoster.category = '主要负责人' THEN 1 ELSE 0 END)`,
        'ResponsibleNum',
      )
      .addSelect(
        `SUM(CASE WHEN ngRoster.category = '接害劳动者' THEN 1 ELSE 0 END)`,
        'EmployeeNum',
      )
      .addSelect(
        `SUM(CASE WHEN ngRoster.category = '职业卫生管理人员' THEN 1 ELSE 0 END)`,
        'AdminNum',
      )
      .andWhere('ng_declaration.unified_code = :unifiedCode', {
        unifiedCode: query.unifiedCode,
      })
      .andWhere('ng_declaration.del_flag = 0')
      .groupBy('YEAR(ngRoster.train_time)')
      .orderBy('YEAR(ngRoster.train_time)', 'ASC')
      .limit(size) // 每页返回的条数
      .offset((page - 1) * size) // 每页返回的条数
      .getRawMany();

    const total = await this.ngRosterRepository
      .createQueryBuilder('ngRoster')
      .innerJoin(
        ngDeclaration,
        'ng_declaration',
        'ng_declaration.id = ngRoster.declaration_id',
      )
      .select('COUNT(DISTINCT YEAR(ngRoster.train_time))', 'count')
      .andWhere('ng_declaration.unified_code = :unifiedCode', {
        unifiedCode: query.unifiedCode,
      })
      .andWhere('ng_declaration.del_flag = 0')
      .getRawOne();

    return {
      list: result,
      pagination: {
        page: page,
        size: size,
        total: Number(total.count) || 0,
      },
    };
  }

  async exportQyknewtjfx(searchQuery) {
    let queryBuilder =
      await this.ngDeclarationRepository.createQueryBuilder('entity');

    queryBuilder.andWhere('entity.district_id LIKE :userDistrict', {
      userDistrict: `${searchQuery.userDistrict}%`,
    });

    queryBuilder = this.xxcxWhereSearch(queryBuilder, searchQuery);

    if (searchQuery.ssdqmc) {
      queryBuilder.andWhere('entity.district_id LIKE :ssdqmc', {
        ssdqmc: `${searchQuery.ssdqmc.replace(/0{3}$|0{6}$|0{8}$|0{10}$/, '')}%`,
      });
    }

    if (searchQuery.economicStatus) {
      queryBuilder.leftJoinAndSelect(
        NgEmployer,
        'ng_employer',
        'ng_employer.unified_code = entity.unified_code',
      );
      queryBuilder.andWhere('ng_employer.employer_status = :economicStatus', {
        economicStatus: searchQuery.economicStatus,
      });
    } else {
      queryBuilder.leftJoinAndSelect(
        NgEmployer,
        'ng_employer',
        'ng_employer.unified_code = entity.unified_code',
      );
    }

    const result = await queryBuilder
      .select([
        'entity.employer_name',
        'entity.unified_code',
        'entity.employer_address',
        'dim_industry.name',
        'entity.declaration_type',
        'entity.declaration_year',
        'entity.declaration_date',
        'dim_economic.economic_type',
        'entity.legal_rep',
        'entity.legal_rep_phone',
        'entity.contactor',
        'entity.contactor_phone',
        'entity.employer_scale',
        'entity.employee_num',
        'entity.exposed_num',
        'entity.employee_training_num',
        'entity.exam_status',
        'entity.detect_type',
        'ng_employer.employer_status',
        'entity.has_dust_hazard',
        'entity.has_chem_hazard',
        'entity.has_phys_hazard',
        'entity.has_radio_hazard',
        'entity.has_bio_hazard',
        'entity.has_other_hazard',
      ])
      .getRawMany();
    return {
      list: result,
    };
  }

  async getHealthTraintListDetail(query) {
    if (query.category === '1') {
      query.category = '主要负责人';
    } else if (query.category === '2') {
      query.category = '职业卫生管理人员';
    } else if (query.category === '3') {
      query.category = '接害劳动者';
    }
    const year = query.year;
    const category = query.category;

    const result = await this.ngRosterRepository
      .createQueryBuilder('ngRoster')
      .innerJoin(
        ngDeclaration,
        'ngDeclaration',
        'ngDeclaration.id = ngRoster.declaration_id',
      )
      .where('YEAR(ngRoster.train_time) = :year', { year })
      .andWhere('ngRoster.category = :category', { category })
      .andWhere('ngDeclaration.unified_code = :unifiedCode', {
        unifiedCode: query.unifiedCode,
      })
      .andWhere('ngDeclaration.del_flag = 0')
      .select([
        'ngRoster.category AS category',
        'ngRoster.emp_name AS empName',
        'ngRoster.id_card_no AS idCardNo',
        'ngRoster.dept AS dept',
        'ngRoster.train_time AS trainTime',
        'ngRoster.train_type AS trainType',
        'ngRoster.train_method AS trainMethod',
        'ngRoster.comm_train_inst AS commTrainInst',
      ])
      .getRawMany();
    return {
      list: result,
    };
  }

  async countByAddress(searchQuery) {
    if (!searchQuery.ssdqmc && searchQuery.userDistrict === '13') {
      let queryBuilder = await this.ngDeclarationRepository
        .createQueryBuilder('entity')
        .innerJoin(DimDistrict, 'd', 'd.id = entity.district_id');

      queryBuilder.andWhere('entity.district_id LIKE :userDistrict', {
        userDistrict: `${searchQuery.userDistrict}%`,
      });

      queryBuilder = this.xxcxWhereSearch(queryBuilder, searchQuery);

      if (searchQuery.economicStatus) {
        queryBuilder.leftJoinAndSelect(
          NgEmployer,
          'ng_employer',
          'ng_employer.unified_code = entity.unified_code',
        );
        queryBuilder.andWhere('ng_employer.employer_status = :economicStatus', {
          economicStatus: searchQuery.economicStatus,
        });
      }

      const sqlResult = await queryBuilder
        .select(['d.full_name AS name', 'COUNT(entity.employer_name) AS count'])
        .groupBy('SUBSTRING(entity.district_id,1,4)')
        .getRawMany();

      const result = sqlResult.map((item) => {
        item.name = item.name.split(' ')[1];
        return item;
      });

      return {
        list: {
          result,
        },
      };
    } else {
      let queryBuilder = await this.ngDeclarationRepository
        .createQueryBuilder('entity')
        .innerJoin(DimDistrict, 'd', 'entity.district_id = d.id');

      queryBuilder.andWhere('entity.district_id LIKE :userDistrict', {
        userDistrict: `${searchQuery.userDistrict}%`,
      });

      queryBuilder = this.xxcxWhereSearch(queryBuilder, searchQuery);

      if (searchQuery.ssdqmc) {
        queryBuilder.andWhere('entity.district_id LIKE :districtIdStr', {
          districtIdStr: `${searchQuery.ssdqmc.replace(/0{3}$|0{6}$|0{8}$|0{10}$/, '')}%`,
        });
      }

      if (searchQuery.economicStatus) {
        queryBuilder.leftJoinAndSelect(
          NgEmployer,
          'ng_employer',
          'ng_employer.unified_code = entity.unified_code',
        );
        queryBuilder.andWhere('ng_employer.employer_status = :economicStatus', {
          economicStatus: searchQuery.economicStatus,
        });
      }

      const sqlResult = await queryBuilder
        .select(['COUNT(entity.employer_name) AS count', 'd.full_name AS name'])
        .groupBy('SUBSTRING(entity.district_id,1,6)')
        .getRawMany();

      const result = sqlResult.map((item) => {
        item.name = item.name.split(' ')[2];
        return item;
      });

      return {
        list: {
          result,
        },
      };
    }
  }
  // 监管端接口
  // 查询填报人所在地区编号
  async getDistrictId(params) {
    const { username } = params;
    const user = await this.ngUserRepository
      .createQueryBuilder('ng_user')
      .where('ng_user.username = :username', { username })
      .select('ng_user.unit_id')
      .getOne();
    const monitor = await this.ngMonitorRepository
      .createQueryBuilder('ng_monitor')
      .where('ng_monitor.id = :unit_id', { unit_id: user.unit_id })
      .select('ng_monitor.district_id')
      .getOne();
    console.log(user, monitor, '=====mon');
    return {
      monitor,
    };
  }

  // 获取回执编号顺序码最大值
  async getMaxReceiptNum() {
    const maxReceiptNum = await this.ngAuditRepository
      .createQueryBuilder()
      .select(`SUBSTRING(ng_audit.receipt_number, -6)`, 'lastSix')
      .from(ngAudit, 'ng_audit')
      .orderBy(
        'CAST(SUBSTRING(ng_audit.receipt_number, -6) AS UNSIGNED)',
        'DESC',
      ) // 将后六位转换为无符号整数并降序排列
      .limit(1) // 限制结果只返回一个，即最大的后六位数值
      .getRawOne();
    return parseInt(maxReceiptNum.lastSix, 10);
  }

  // 存储回执编号到ng_audit
  async saveReceiptNum(data) {
    const { last_audit_id } = data;
    const formData: any = {
      receipt_number: data.receipt_number,
    };
    await this.ngAuditRepository
      .createQueryBuilder('ng_audit')
      .update(ngAudit) // 指定要更新的实体
      .set(formData) // 设置要更新的字段
      .where('id = :last_audit_id', { last_audit_id }) // 添加更新条件
      .execute(); // 执行更新
  }

  // 驳回申报审核
  async updateJgOnlineDeclarationRefuseRecords(data: {
    last_audit_id: number;
    comment: string;
    reason: string;
    isNeedSave?: boolean;
    snapshot_files?: any[];
    id: number;
  }) {
    const { last_audit_id, comment, snapshot_files, id } = data;
    if (data.isNeedSave) {
      // 再次驳回的情况，保存之前的通过记录，
      // 在ng_audit表中重新生成一条记录，并将ng_declartion表的last_audit_id更新为最新的id,
      // 否则正常驳回
      const form: any = {
        audit_status: 2,
        comment,
        audit_time: new Date(),
        reject_reason: data.reason,
        receipt_number: null,
        declaration_id: data.id,
      };
      if (snapshot_files) {
        form.snapshot_files = snapshot_files;
      }
      const newForm = this.ngAuditRepository.create(form);
      const res: any = await this.ngAuditRepository.save(newForm);
      if (res && res.id) {
        // 将审核表关联上申报表
        const updateData: any = { last_audit_id: res.id };
        await this.ngDeclarationRepository
          .createQueryBuilder('ng_declaration')
          .update(ngDeclaration) // 指定要更新的实体
          .set(updateData) // 设置要更新的字段
          .where('id = :id', { id }) // 添加更新条件
          .execute(); // 执行更新
      }
    } else {
      const formData: any = {
        audit_status: 2,
        comment,
        audit_time: new Date(),
        reject_reason: data.reason,
        receipt_number: null,
      };
      if (snapshot_files) {
        formData.snapshot_files = snapshot_files;
      }
      await this.ngAuditRepository
        .createQueryBuilder('ng_audit')
        .update(ngAudit) // 指定要更新的实体
        .set(formData) // 设置要更新的字段
        .where('id = :last_audit_id', { last_audit_id }) // 添加更新条件
        .execute(); // 执行更新
    }
  }

  // 通过申报审核
  async agreeJgOnlineDeclaration(data: {
    last_audit_id: number;
    unified_code: string;
    comment: string;
  }) {
    const { last_audit_id, unified_code, comment } = data;
    const formData: any = {
      audit_status: 1,
      // auditor_id:null,
      comment,
      audit_time: new Date(),
    };

    const employerDeclaredId: any = {
      lastDeclaredId: last_audit_id,
    };

    await this.ngAuditRepository
      .createQueryBuilder('ng_audit')
      .update(ngAudit) // 指定要更新的实体
      .set(formData) // 设置要更新的字段
      .where('id = :last_audit_id', { last_audit_id }) // 添加更新条件
      .execute(); // 执行更新

    await this.NgEmployerRepository.createQueryBuilder('ng_employer')
      .update(NgEmployer) // 指定要更新的实体
      .set(employerDeclaredId) // 设置要更新的字段
      .where('unified_code = :unified_code', { unified_code }) // 添加更新条件
      .execute(); // 执行更新
  }

  // 获取危害申报详情基本信息
  async getDeclarationDetailBasicInfo(data) {
    const res = await this.ngDeclarationRepository.findOne({
      where: { id: data.id },
    });
    console.log(res, '详情===================');
    return res;
  }

  // 获取驳回原因
  async getRejectReason(data) {
    const { last_audit_id } = data;
    const res = await this.ngAuditRepository
      .createQueryBuilder('ng_audit')
      .where('id = :last_audit_id', { last_audit_id })
      .select('ng_audit.reject_reason')
      .getOne();
    return res;
  }

  // 获取申报审核状态
  async fetchDeclarationStatus(data) {
    const { last_audit_id } = data;
    const res = await this.ngAuditRepository
      .createQueryBuilder('ng_audit')
      .where('id = :last_audit_id', { last_audit_id })
      .select([
        'ng_audit.audit_status',
        'ng_audit.snapshot_files',
        'ng_audit.audit_time',
        'ng_audit.receipt_number',
      ])
      // .select('ng_audit.audit_status')
      .getOne();
    return res;
  }

  // 获取申报审核回执
  async getAuditReceiptUrl(data) {
    const { last_audit_id } = data;
    const res = await this.ngAuditRepository
      .createQueryBuilder('ng_audit')
      .where('id = :last_audit_id', { last_audit_id })
      .select('ng_audit.receipt_number')
      .getOne();
    console.log(res, '=======审核回执信息');
    return res;
  }

  // 监管端获取申报列表统计
  async jgOnlineDeclarationStatistics(data: { districtCode: string }) {
    // const district = data.districtCode.replace(/0+$/, '');
    const district: number | string = String(data.districtCode)
      .slice(0, 6)
      .replace(/00$/, '')
      .replace(/00$/, '');
    const qb = this.ngDeclarationRepository
      .createQueryBuilder('ng_declaration')
      .leftJoinAndSelect('ng_declaration.latestAudit', 'ng_audit')
      .where('ng_audit.id IS NOT NULL')
      .andWhere('CAST(ng_declaration.district_id AS CHAR) LIKE :districtId', {
        districtId: `${district}%`,
      })
      .andWhere('ng_declaration.del_flag != :del_flag', { del_flag: 1 });

    const totalCount = await qb
      .select('COUNT(DISTINCT ng_declaration.unified_code)', 'count')
      .getRawOne();

    const auditStatuses = [0, 1, 2];
    const [countNotAudit, countSuccess, countFail] = await Promise.all(
      auditStatuses.map(async (status) => {
        return qb
          .andWhere('ng_audit.audit_status = :auditStatus', {
            auditStatus: status,
          })
          .select('COUNT(DISTINCT ng_declaration.unified_code)', 'count')
          .getRawOne();
      }),
    );
    const declarationStatistics = {
      total: totalCount.count,
      countSuccess: countSuccess.count,
      countFail: countFail.count,
      countNotAudit: countNotAudit.count,
    };
    return declarationStatistics;
  }

  // 监管端获取申报列表
  async jgOnlineDeclarationList(data: {
    matchQuery: {
      districtCode: string;
      employerName?: string;
      declarationStartTime?: string; // eg: '2021-01-01'
      declarationEndTime?: string;
      declarationType?: number;
      declarationState?: number;
    };
    pagination: {
      currentPage?: number;
      pageSize?: number;
    };
    // order: {
    //   declarationDate?: 1 | -1;
    //   declarationState?: 1 | -1;
    // };
  }) {
    const { matchQuery, pagination } = data;
    const { currentPage = 1, pageSize = 10 } = pagination;
    const {
      districtCode,
      declarationStartTime,
      declarationEndTime,
      employerName,
      declarationType,
      declarationState,
    } = matchQuery;
    const qb = this.ngDeclarationRepository
      .createQueryBuilder('ng_declaration')
      .leftJoinAndSelect('ng_declaration.latestAudit', 'ng_audit')
      .where('ng_audit.id IS NOT NULL');
    const district: number | string = String(districtCode)
      .slice(0, 6)
      .replace(/00$/, '')
      .replace(/00$/, '');
    qb.andWhere('CAST(ng_declaration.district_id AS CHAR) LIKE :districtId', {
      districtId: `${district}%`,
    });
    qb.andWhere('ng_declaration.del_flag != :del_flag', { del_flag: 1 });

    if (declarationStartTime && declarationEndTime) {
      qb.andWhere(
        'ng_declaration.declaration_date BETWEEN :startTime AND :endTime',
        { startTime: declarationStartTime, endTime: declarationEndTime },
      );
    }
    if (employerName) {
      qb.andWhere('(ng_declaration.employer_name LIKE :employerName)', {
        employerName: `%${employerName}%`,
      });
    }

    if (declarationType) {
      qb.andWhere('ng_declaration.declaration_type =:declarationType', {
        declarationType,
      });
    }

    if (declarationState || declarationState === 0) {
      qb.andWhere('ng_audit.audit_status =:declarationState', {
        declarationState,
      });
    }

    qb.orderBy('ng_declaration.declaration_date', 'DESC');

    // 排序
    // if (order.declarationDate) {
    //   qb.orderBy(
    //     'ng_declaration.declaration_date',
    //     order.declarationDate === 1 ? 'ASC' : 'DESC',
    //   );
    // } else if (order.declarationState) {
    //   qb.orderBy(
    //     'ng_audit.audit_status',
    //     order.declarationState === 1 ? 'ASC' : 'DESC',
    //   );
    // } else {
    //   qb.orderBy('ng_declaration.declaration_date', 'DESC');
    // }

    // 获取数据
    const docs = await qb
      .select([
        'ng_declaration.id',
        'ng_declaration.declaration_type',
        'ng_declaration.declaration_year',
        'ng_declaration.declaration_date',
        'ng_declaration.employer_name',
        'ng_declaration.unified_code',
        'ng_declaration.filler',
        'ng_declaration.contactor',
        'ng_declaration.contactor_phone',
        'ng_declaration.last_audit_id',
        'ng_audit.audit_status',
        'ng_declaration.sync_status',
        'ng_declaration.last_update_time',
        'ng_audit.audit_time',
        'ng_declaration.district_id',
        'ng_declaration.declaration_files',
      ])
      .skip((currentPage - 1) * pageSize)
      .take(pageSize)
      .getMany();
    // 获取总数
    const count = await qb.getCount();

    // 按企业家数获取统计数据
    // const totalCount = await qb
    //   .select('COUNT(DISTINCT ng_declaration.unified_code)', 'count')
    //   .getRawOne();

    // const auditStatuses = [0, 1, 2];
    // const [countNotAudit, countSuccess, countFail] = await Promise.all(
    //   auditStatuses.map(async (status) => {
    //     return qb
    //       .andWhere('ng_audit.audit_status = :auditStatus', {
    //         auditStatus: status,
    //       })
    //       .select('COUNT(DISTINCT ng_declaration.unified_code)', 'count')
    //       .getRawOne();
    //   }),
    // );
    // const declarationStatistics = {
    //   total: totalCount.count,
    //   countSuccess: countSuccess.count,
    //   countFail: countFail.count,
    //   countNotAudit: countNotAudit.count,
    // };

    // 返回数据
    const result = docs.map((doc) => ({
      ...doc,
      ng_audit_audit_status: doc.latestAudit
        ? doc.latestAudit.audit_status
        : null,
    }));

    return {
      docs: result,
      count,
      // declarationStatistics,
    };
  }

  // 按照审核状态和申报区域统计申报企业数量
  async getCountByAuditStatus(auditStatus: number | null, district: string) {
    if (auditStatus === null) {
      const totalCount = await this.ngDeclarationRepository
        .createQueryBuilder('ng_declaration')
        .leftJoin('ng_declaration.latestAudit', 'ng_audit')
        .where('ng_audit.id IS NOT NULL')
        .andWhere('CAST(ng_declaration.district_id AS CHAR) LIKE :districtId', {
          districtId: `%${district}%`,
        })
        .select('COUNT(DISTINCT ng_declaration.unified_code)', 'count')
        .getRawOne();
      return totalCount.count;
    }
    const countAudit = await this.ngDeclarationRepository
      .createQueryBuilder('ng_declaration')
      .leftJoin('ng_declaration.latestAudit', 'ng_audit')
      .where('ng_audit.audit_status = :auditStatus', { auditStatus })
      .andWhere('CAST(ng_declaration.district_id AS CHAR) LIKE :districtId', {
        districtId: `%${district}%`,
      })
      .select('COUNT(DISTINCT ng_declaration.unified_code)', 'count')
      .getRawOne();

    return countAudit.count;
  }

  async qyOnlineDeclarationLast(query) {
    const lastAudits = await this.ngDeclarationRepository
      .createQueryBuilder('ng_declaration')
      .where({ unified_code: query.unified_code, del_flag: 0 })
      .andWhere('ng_audit.id IS NOT NULL')
      .leftJoinAndSelect(
        ngAudit,
        'ng_audit',
        'ng_audit.declaration_id = ng_declaration.id',
      )
      .orderBy('ng_audit.audit_time', 'DESC')
      .getRawMany();

    if (lastAudits[0]) {
      const lastAudit = lastAudits[0];
      const newItem = {};
      Object.keys(lastAudit).forEach((key) => {
        if (key.startsWith('ng_declaration_')) {
          newItem[key.replace('ng_declaration_', '')] = lastAudit[key];
        } else {
          newItem[key] = lastAudit[key];
        }
      });
      return newItem;
    }
    return null;
  }

  async qyOnlineDeclarationEmployer(query) {
    try {
      const whereQuery: any = {
        where: { unifiedCode: query.unified_code },
      };

      const employer = await this.NgEmployerRepository.findOne(whereQuery);
      return employer;
    } catch (error) {
      this.logger.error('Error in : qyOnlineDeclarationEmployer', error);
    }
  }

  async updateEmployer({ unified_code, updateParams }) {
    const res = await this.ngDeclarationRepository
      .createQueryBuilder('ng_employer')
      .update(NgEmployer) // 指定要更新的实体
      .set(updateParams) // 设置要更新的字段
      .where('unified_code = :unified_code', { unified_code }) // 添加更新条件
      .execute(); // 执行更新
    return res;
  }

  // 获取普瑞的检测数据
  async qyOnlineDeclarationJcProject(data) {
    const query = this.PrJgjgProjectRepository.createQueryBuilder(
      'pr_jgjg_project',
    ).where({ SERVICE_STATUS: '3' });

    if (data.code) {
      query.andWhere({ CREDITCODE: data.code });
    }
    if (data.startDate) {
      query.andWhere('pr_jgjg_project.REPORT_DATE >= :startDate', {
        startDate: data.startDate,
      });
    }

    if (data.endDate) {
      query.andWhere('pr_jgjg_project.REPORT_DATE <= :endDate', {
        endDate: data.endDate,
      });
    }

    const docs = await query.getMany();

    return docs;
  }

  /**
   * 根据统一社会信用代码获取最近一次成功审核的申报记录
   *
   * @param unified_code 企业的统一社会信用代码
   * @param options 可选的查询配置，用于控制是否包含关联数据
   * @returns 最近一条审核状态为已审核(audit_status = 1)的申报记录，如果没有找到则返回null
   */
  async getQyLastSuccessDeclaration(
    unified_code: string,
    options?: {
      includeRoster?: boolean;
      includeAudit?: boolean;
    },
  ): Promise<ngDeclaration> {
    // 创建查询构建器
    const queryBuilder = this.NgEmployerRepository.createQueryBuilder(
      'employer',
    )
      .where('employer.unified_code = :unified_code', { unified_code })
      .leftJoinAndSelect('employer.lastDeclaration', 'declaration');

    // 根据options配置关联查询
    if (options?.includeRoster) {
      queryBuilder.leftJoinAndSelect('declaration.roster', 'roster');
    }
    if (options?.includeAudit) {
      queryBuilder.leftJoinAndSelect('declaration.latestAudit', 'latestAudit');
    }

    // 执行查询并返回结果
    const employer = await queryBuilder.getOne();
    if (!employer) {
      this.logger.error(`未找到 employer: ${unified_code}`);
    }
    if (!employer?.lastDeclaration) {
      this.logger.error(`未找到 lastDeclaration: ${unified_code}`);
    }
    return employer?.lastDeclaration ?? null;
  }

  /**
   * Find the latest audited declaration for given unifiedCode
   *
   * @param unifiedCode 企业的统一社会信用代码
   * @returns The latest audited declaration for given unifiedCode, or null if not found
   */
  async findLatestAuditedDeclaration(
    unifiedCode: string,
  ): Promise<ngDeclaration | null> {
    try {
      const result = await this.ngDeclarationRepository
        .createQueryBuilder('d')
        .select(['d.*', 'a.audit_status', 'a.audit_time'])
        .innerJoin(ngAudit, 'a', 'a.declaration_id = d.id')
        .where('d.unified_code = :unifiedCode', { unifiedCode })
        .andWhere('a.audit_status = :auditStatus', { auditStatus: 1 })
        .orderBy('a.audit_time', 'DESC')
        .limit(1)
        .getRawOne();

      return result || null;
    } catch (error) {
      this.logger.error('Error in findLatestAuditedDeclaration:', error);
      return null;
    }
  }

  async findLatestAuditedDeclarationsByYears(
    unified_code: string,
    startYear: number,
    endYear: number,
  ): Promise<ngDeclaration[]> {
    const declarations = await this.ngDeclarationRepository
      .createQueryBuilder('d')
      .select('d')
      .addSelect('a.audit_status', 'audit_status')
      .innerJoin(ngAudit, 'a', 'a.declaration_id = d.id')
      .where('d.unified_code = :unified_code', { unified_code })
      .andWhere('d.declaration_year >= :startYear', {
        startYear: startYear + '',
      })
      .andWhere('d.declaration_year <= :endYear', {
        endYear: endYear + '',
      })
      .andWhere('a.audit_status = :auditStatus', { auditStatus: 1 })
      .orderBy('d.declaration_year', 'DESC')
      .addOrderBy('a.audit_time', 'DESC')
      .limit(1)
      .getMany();

    return declarations;
  }

  /**
   * 根据统一社会信用代码获取指定数量的成功审核的申报记录
   *
   * @param unified_code 企业的统一社会信用代码
   * @param options 查询配置，包括起始日期、结束日期和查询数量f
   * @returns 符合条件的申报记录数组
   */
  async getQySuccessDeclarations(
    unified_code: string,
    options?: {
      startYear?: string;
      endYear?: string;
      // count?: number;
      includeRoster?: boolean;
    },
  ): Promise<ngDeclaration[]> {
    // 创建查询构建器
    const queryBuilder = this.ngDeclarationRepository
      .createQueryBuilder('declaration')
      .innerJoinAndSelect('declaration.latestAudit', 'audit')
      .where('declaration.unified_code = :unified_code', { unified_code })
      .andWhere('audit.audit_status = :approvedStatus', { approvedStatus: 1 });

    // 如果提供了起始日期，添加起始日期条件
    if (options?.startYear) {
      queryBuilder.andWhere('declaration.declaration_year >= :startYear', {
        startYear: parseInt(options.startYear, 10),
      });
    }

    // 如果提供了结束日期，添加结束日期条件
    if (options?.endYear) {
      queryBuilder.andWhere('declaration.declaration_year <= :endYear', {
        endYear: parseInt(options.endYear, 10),
      });
    }

    // 根据 options 配置关联查询
    if (options?.includeRoster) {
      queryBuilder.leftJoinAndSelect('declaration.roster', 'roster');
    }

    // 排序并限制返回数量
    queryBuilder.orderBy('declaration.declaration_year', 'DESC');
    // if (options?.count) {
    //   queryBuilder.take(options?.count);
    // }

    // 执行查询并返回结果
    const declarations = await queryBuilder.getMany();

    return declarations;
  }

  /**
   * 根据统一社会信用代码获取年份纬度分组的申报记录列表
   *
   * @param unified_code 企业的统一社会信用代码
   * @param options 查询配置，包括起始日期、结束日期和查询数量
   * @returns 符合条件的申报记录数组
   */
  async getYearsDeclarations(
    unified_code: string,
    options?: {
      startYear?: string;
      endYear?: string;
      count?: number;
      includeRoster?: boolean;
      includeOnlyLatest?: boolean;
    },
  ): Promise<
    {
      year: string;
      items: ngDeclaration[];
    }[]
  > {
    // 获取符合条件的申报记录
    const declarations = await this.getQySuccessDeclarations(
      unified_code,
      options,
    );

    // 使用 lodash 的 groupBy 方法按年份分组
    const groupedDeclarations: Record<string, ngDeclaration[]> = groupBy(
      declarations,
      (declaration) => {
        // 从 declaration_date 中提取年份
        return declaration.declaration_year || 'unknown';
      },
    );

    // 转换为所需的数组格式
    const yearsDeclarations = Object.entries(groupedDeclarations)
      .map(([year, items]) => {
        let sortedItems = items;
        if (options.includeOnlyLatest && sortedItems.length > 1) {
          sortedItems = sortBy(items, (item) => {
            return item.latestAudit?.audit_time?.getTime();
          });
        }

        return {
          year,
          items: take(sortedItems, 1),
        };
      })
      .sort((a, b) => b.year.localeCompare(a.year)); // 按年份降序排序

    return yearsDeclarations;
  }

  // 获取体检机构列表
  async getTjHealthCompany() {
    const whereData: any = {
      lose_status: '0',
    };
    const data = await this.tjHealthCompanyRepository.find({
      where: whereData,
    });

    return data;
  }
  // 获取检测机构列表
  async getTjTechnicalqualityCompany() {
    const whereData = {
      del_flag: 0,
    };
    const data = await this.tjTechnicalqualityCompanyRepository.find({
      where: whereData,
    });

    return data;
  }

  // 获取监管单位
  async supervisorList() {
    const whereData = {};
    const data = await this.ngSupervisorRepository.find({
      where: whereData,
    });

    return data;
  }

  // 获取审核回执签发单位
  async getSignGroupName(params: { district_id: number }) {
    const { district_id } = params;
    const res = await this.ngSupervisorRepository
      .createQueryBuilder('ng_supervisor')
      .where('district_id=:district_id', { district_id })
      .select('ng_supervisor.sign_name')
      .getOne();
    return res;
  }

  // 获取审核信息
  async getHasAuditData(params: { last_audit_id: number }) {
    try {
      const { last_audit_id } = params;
      const res = await this.ngAuditRepository
        .createQueryBuilder('ng_audit')
        .where('id=:last_audit_id', { last_audit_id })
        .select(['ng_audit.audit_status', 'ng_audit.receipt_number'])
        .getOne();
      return res;
    } catch (error) {
      this.logger.error('Error in getHasAuditData:', error);
    }
  }

  // 获取申报数据
  async getOnlineDeclarationInfo(params) {
    try {
      const auditInfos = await this.ngDeclarationRepository
        .createQueryBuilder('ng_declaration')
        .where({ id: params.id })
        .andWhere('ng_audit.id IS NOT NULL')
        .leftJoinAndSelect(
          ngAudit,
          'ng_audit',
          'ng_audit.declaration_id = ng_declaration.id',
        )
        .select([
          'ng_declaration.id', // 查询ng_declaration表的id字段
          'ng_declaration.employer_name', // 查询ng_declaration表的name字段
          'ng_declaration.last_audit_id',
          'ng_declaration.unified_code',
          'ng_declaration.sync_status',
          'ng_declaration.district_id',
          'ng_declaration.declaration_type',
          'ng_declaration.declaration_files',
          // 'ng_audit.audit_status', // 查询ng_audit表的id字段
          // 'ng_audit.audit_time',
          'ng_declaration.last_update_time',
        ])
        .getOne();
      return auditInfos;
    } catch (error) {
      this.logger.error('Error in getOnlineDeclarationInfo:', error);
    }
  }

  async onEmployerDeregister(unified_code: string) {
    this.logger.log(`设置企业注销: ${unified_code}`);
    await this.NgEmployerRepository.createQueryBuilder('ng_employer')
      .update(NgEmployer) // 指定要更新的实体
      .set({ employerStatus: 0 }) // 设置要更新的字段
      .where('unified_code = :unified_code', { unified_code }) // 添加更新条件
      .execute(); // 执行更新
  }

  async handleLastRosterToNow({ lastDataId, id }) {
    let doc = await this.ngRosterRepository
      .createQueryBuilder('ng_roster')
      .where({ declaration_id: Number(lastDataId) })
      .getMany();

    if (!doc) {
      return;
    }
    doc = JSON.parse(JSON.stringify(doc));

    const newData = doc.map((e) => {
      return {
        name: e.emp_name,
        dingtree: e.dept,
        IDNum: e.id_card_no,
        trainTime: e.train_time
          ? moment(new Date(e.train_time)).format('YYYY-MM-DD')
          : '',
        trainType: e.train_type,
        trainFun: e.train_method,
        trainOrg: e.comm_train_inst,
        personType: e.category,
      };
    });

    const res = await this.updateQyNgRoster({ personData: newData, id });
    return res;
  }

  async findEmployer(query) {
    const whereQuery: any = {};
    if (query.unified_code) {
      whereQuery.unifiedCode = query.unified_code;
    }
    return await this.NgEmployerRepository.createQueryBuilder('ng_employer')
      .where(whereQuery)
      .getOne();
  }
  async saveEmployer(data) {
    const employeerData = this.NgEmployerRepository.create(data);
    const res = await this.NgEmployerRepository.save(employeerData);
    return res;
  }
}
