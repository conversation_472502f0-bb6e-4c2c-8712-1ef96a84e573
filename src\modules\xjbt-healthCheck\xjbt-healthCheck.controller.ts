import { Controller, Post, Body, Get, Query, Logger } from '@nestjs/common';
import { XjbtHealthCheckService } from './xjbt-healthCheck.service';
import { CreateEmployeeBasicInfoDto } from './dto/create-employee-basic-info.dto';
import { EmployeeBasicInfo } from '../dak/entities/employeeBasicInfo.entity';
import { wrapperResponse } from 'src/utils';

@Controller('xjbt-healthCheck')
export class XjbtHealthCheckController {
  private readonly logger = new Logger(XjbtHealthCheckController.name);

  constructor(private readonly xjbtHealthCheckService: XjbtHealthCheckService) { }

  // 职业健康监护档案抽取
  @Get('extractHealthArchive')
  async extractHealthArchive(
    @Query('idNumber') idNumber: string,
  ): Promise<any> {
    // this.logger.log(`抽取职业健康监护档案: ${idNumber}`);
    return wrapperResponse(
      this.xjbtHealthCheckService.extractHealthArchive(idNumber),
      '抽取职业健康监护档案',
    );
  }

  /**
   * 创建或更新员工基本信息
   * @param createEmployeeBasicInfoDto 员工基本信息DTO
   * @returns 创建或更新的员工基本信息
   */
  @Post('postEmployeeBasicInfo')
  async createEmployeeBasicInfo(
    @Body() createEmployeeBasicInfoDto: CreateEmployeeBasicInfoDto,
  ): Promise<EmployeeBasicInfo> {
    this.logger.log(`上报员工基本信息: ${JSON.stringify(createEmployeeBasicInfoDto)}`);
    return wrapperResponse(
      this.xjbtHealthCheckService.createOrUpdateEmployeeBasicInfo(createEmployeeBasicInfoDto),
      '上报员工基本信息',
    );
  }

  /**
   * 根据身份证号查询员工基本信息
   * @param idNumber 身份证号
   * @returns 员工基本信息
   */
  @Get('getEmployeeBasicInfo')
  async getEmployeeBasicInfo(
    @Query('idNumber') idNumber: string,
  ): Promise<EmployeeBasicInfo> {
    this.logger.log(`查询员工基本信息: ${idNumber}`);
    return wrapperResponse(
      this.xjbtHealthCheckService.findEmployeeBasicInfoByIdNumber(idNumber),
      '查询员工基本信息',
    );
  }



}