import { Modu<PERSON> } from '@nestjs/common';
import { WkzwyAppointmentController } from './wkzwy-appointment.controller';
import { WkzwyAppointmentService } from './wkzwy-appointment.service';
import { Appointment } from './sqlserver/appointment.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HealthCheckAppointmentModule } from '../health-check-appointment/health-check-appointment.module';
import { AdminorgsModule } from '../adminorgs/adminorgs.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Appointment], 'sqlserverConnection'),
    HealthCheckAppointmentModule,
    AdminorgsModule,
  ],
  controllers: [WkzwyAppointmentController],
  providers: [WkzwyAppointmentService],
})
export class WkzwyAppointmentModule {}
