export class QueryQykDto {
  year?: string; // 年份
  district_id?: number; // 区域Id
  districtName?: string; // 区域名称
  sblb?: '初次申报' | '变更申报' | '年度更新'; // 申报类型
  hylb?: string; // 行业类别 中文名称
  qygm?: '0' | '1' | '2' | '3'; // 企业规模 0-大；1-中；2-小；3-微
  startTime?: string; // 开始时间
  endTime?: string; // 结束时间
  item_type?: '1' | '2' | '3' | '4'; // 检测项目类型：1定期检测 2现状评价 3预评价 4控制效果评价
  company_report_status?: '0' | '1'; // 技术服务机构上报：0未上报 1已上报
  employer_report_status?: '0' | '1'; // 用人单位上报：0未上报 1已上报
  jjlx?: // 经济类型
  | '有限责任公司'
    | '国有企业'
    | '其他企业'
    | '私营企业'
    | '股份有限公司'
    | '其他外商投资企业'
    | '合资经营企业（港或澳、台资）'
    | '外资企业'
    | '股份合作企业'
    | '中外合资经营企业'
    | '外商投资股份有限公司'
    | '集体企业'
    | '港、澳、台商独资经营企业'
    | '联营企业'
    | '港、澳、台商投资股份有限公司'
    | '中外合作经营企业'
    | '其他港、澳、台商投资企业'
    | '合作经营企业（港或澳、台资）'
    | '内资企业';
  name: string; // 名称
  sblx: string; // 申报类型
  sbnf: string; // 申报年份
  sbrq: string[]; // 申报日期
  unifiedCode: string; // 统一社会信用代码
  fr: string; // 法人
  zgzrs: number; // 职工总人数
  jhzrs: number; // 计划总人数
  ssdqmc: string; // 所属地区名称
  ssdqmcArray: string[]; // 所属地区名称
  page: number; // 页码
  size: number; // 每页数量
  sbrqStart: string; // 申报日期开始
  sbrqEnd: string; // 申报日期结束
  zgzrsCountCondition: 'lt' | 'gt' | 'range'; // 职工总人数条件
  zgzrseCount: number; // 职工总人数
  zgzrsCountMin: number; // 职工总人数最小值
  zgzrsCountMax: number; // 职工总人数最大值
}
