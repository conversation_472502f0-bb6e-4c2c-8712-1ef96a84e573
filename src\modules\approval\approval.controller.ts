import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  Version,
  Query,
  ValidationPipe,
  UseGuards,
  ParseIntPipe,
  BadRequestException,
} from '@nestjs/common';
import { ApprovalService } from './approval.service';
import { CreateApprovalDto } from './dto/create-approval.dto';
import { UpdateApprovalDto } from './dto/update-approval.dto';
import { FindApprovalDto } from './dto/find-approval.dto';
import { Approval } from './mysql/approval.entity';
import { OpenidGuard } from 'src/common/guards/openid.guard';
import { User } from 'src/common/decorators/user.decorator';

@Controller('approval')
@UseGuards(OpenidGuard)
export class ApprovalController {
  constructor(private readonly approvalService: ApprovalService) {}

  @Post()
  @Version('1')
  create(
    @User() user,
    @Body(new ValidationPipe({ transform: true, whitelist: true }))
    createApprovalDto: CreateApprovalDto,
  ): Promise<Approval> {
    return this.approvalService.create(createApprovalDto, user.id);
  }

  @Get()
  @Version('1')
  findAll(
    @User() user,
    @Query(new ValidationPipe({ transform: true }))
    findApprovalDto: FindApprovalDto,
  ): Promise<any> {
    return this.approvalService.findAll(findApprovalDto, user.id);
  }

  @Get(':id')
  @Version('1')
  findOne(@User() user, @Param('id', ParseIntPipe) id: number): Promise<Approval> {
    return this.approvalService.findOne(id, user.id);
  }

  @Put(':id')
  @Version('1')
  update(
    @User() user,
    @Param('id', ParseIntPipe) id: number,
    @Body(new ValidationPipe({ transform: true, whitelist: true }))
    updateApprovalDto: UpdateApprovalDto,
  ): Promise<Approval> {
    return this.approvalService.update(id, updateApprovalDto, user.id);
  }

  @Put(':id/approve')
  @Version('1')
  approve(
    @User() user,
    @Param('id', new ParseIntPipe({ 
      errorHttpStatusCode: 400,
      exceptionFactory: (error) => new BadRequestException('Invalid approval ID format'),
    })) id: number,
  ): Promise<Approval> {
    return this.approvalService.approve(id);
  }

  @Put(':id/reject')
  @Version('1')
  reject(
    @User() user,
    @Param('id', new ParseIntPipe({ 
      errorHttpStatusCode: 400,
      exceptionFactory: (error) => new BadRequestException('Invalid approval ID format'),
    })) id: number,
  ): Promise<Approval> {
    return this.approvalService.reject(id);
  }

  @Delete(':id')
  @Version('1')
  remove(@User() user, @Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.approvalService.remove(id, user.id);
  }
}
