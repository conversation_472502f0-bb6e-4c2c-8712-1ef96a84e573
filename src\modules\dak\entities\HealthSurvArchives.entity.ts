import { Entity, Column, ObjectIdC<PERSON>umn, CreateDateColumn, UpdateDateColumn, BeforeInsert } from 'typeorm';
import * as shortid from 'shortid';


@Entity('healthSurvArchives')
export class HealthSurvArchives {
    @ObjectIdColumn()
    _id: string;

    @Column({   comment: '身份证号' })
    idNumber: string;

    @Column({   comment: '姓名' })
    name: string;

    @Column({   comment: '年龄' })
    age: string;

    @Column({   comment: '性别' })
    gender: string;

    @Column({   comment: '联系电话' })
    phoneNum: string;

    @Column({   comment: '籍贯' })
    nativePlace: string;

    @Column({   comment: '嗜好' })
    hobby: string;

    @Column({   comment: '婚姻' })
    marriage: string;

    @Column({   comment: '企业ID' })
    EnterpriseID?: string;

    @Column({   comment: '企业名称' })
    cname?: string;

    @Column({   comment: '企业代码' })
    cCode?: string;

    @Column({   comment: '员工ID' })
    employeeId: string;

    @Column({   comment: '用户ID' })
    userId: string;

    @Column({ comment: '既往史' })
    pastHistory: Array<{
        _id: string;
        diseaseName: string;
        diagnosisDate: string;
        institutionName: string;
        treatmentProcess: string;
        outcomeCode: string;
    }> = [];

    @Column({ comment: '工作史' })
    workHistory: Array<{
        _id: string;
        entryTime: string;
        leaveTime: string;
        workUnit: string;
        workshop: string;
        station: string;
        workType: string;
    }> = [];


    @Column({ comment: '职业危害接触史' })
    exposureHistory: Array<{
        _id: string;
        entryTime: string;
        leaveTime: string;
        workUnit: string;
        workshop: string;
        station: string;
        workType: string;
        harmFactors: Array<{
            _id: string;
            name: string;
            code: string;
            category: string;
        }>;
    }> = [];

    @Column({ comment: '作业场所危害因素检测结果' })
    workspaceHarmResult: any[] = [];

    @Column({ comment: '健康检查结果' })
    healthCheckResult: Array<any> = [];

    @CreateDateColumn({ comment: '创建时间' })
    createdAt: Date;

    @UpdateDateColumn({ comment: '更新时间' })
    updatedAt: Date;

    @BeforeInsert()
    setDefaultId() {
        if (!this._id) {
            this._id = shortid.generate();
        }
    }
    @BeforeInsert()
    setCreatedAt() {
        this.createdAt = new Date();
    }
    @BeforeInsert()
    setUpdatedAt() {
        this.updatedAt = new Date();
    }
}