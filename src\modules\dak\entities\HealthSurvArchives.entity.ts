import { Entity, Column, ObjectIdColumn, CreateDateColumn, UpdateDateColumn, BeforeInsert } from 'typeorm';
import * as shortid from 'shortid';


@Entity('healthSurvArchives')
export class HealthSurvArchives {
    @ObjectIdColumn()
    _id: String;

    @Column({ type: 'varchar', comment: '身份证号' })
    idNumber: String;

    @Column({ type: 'varchar', comment: '姓名' })
    name: String;

    @Column({ type: 'varchar', comment: '年龄' })
    age: String;

    @Column({ type: 'varchar', comment: '性别' })
    gender: String;

    @Column({ type: 'varchar', comment: '联系电话' })
    phoneNum: String;

    @Column({ type: 'varchar', comment: '籍贯' })
    nativePlace: String;

    @Column({ type: 'varchar', comment: '嗜好' })
    hobby: String;

    @Column({ type: 'varchar', comment: '婚姻' })
    marriage: String;

    @Column({ type: 'varchar', comment: '企业ID' })
    EnterpriseID?: String;

    @Column({ type: 'varchar', comment: '企业名称' })
    cname?: String;

    @Column({ type: 'varchar', comment: '企业代码' })
    cCode?: String;

    @Column({ type: 'varchar', comment: '员工ID' })
    employeeId: String;

    @Column({ type: 'varchar', comment: '用户ID' })
    userId: String;

    @Column({ comment: '既往史' })
    pastHistory: Array<{
        _id: String;
        diseaseName: String;
        diagnosisDate: String;
        institutionName: String;
        treatmentProcess: String;
        outcomeCode: String;
    }> = [];

    @Column({ comment: '工作史' })
    workHistory: Array<{
        _id: String;
        entryTime: String;
        leaveTime: String;
        workUnit: String;
        workshop: String;
        station: String;
        workType: String;
    }> = [];


    @Column({ comment: '职业危害接触史' })
    exposureHistory: Array<{
        _id: String;
        entryTime: String;
        leaveTime: String;
        workUnit: String;
        workshop: String;
        station: String;
        workType: String;
        harmFactors: Array<{
            _id: String;
            name: String;
            code: String;
            category: String;
        }>;
    }> = [];

    @Column({ comment: '作业场所危害因素检测结果' })
    workspaceHarmResult: any[] = [];

    @Column({ comment: '健康检查结果' })
    healthCheckResult: Array<any> = [];

    @CreateDateColumn({ comment: '创建时间' })
    createdAt: Date;

    @UpdateDateColumn({ comment: '更新时间' })
    updatedAt: Date;

    @BeforeInsert()
    setDefaultId() {
        if (!this._id) {
            this._id = shortid.generate();
        }
    }
    @BeforeInsert()
    setCreatedAt() {
        this.createdAt = new Date();
    }
    @BeforeInsert()
    setUpdatedAt() {
        this.updatedAt = new Date();
    }
}