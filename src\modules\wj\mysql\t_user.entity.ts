import {
  Entity,
  Column,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
// 系统用户
@Entity('t_user')
export class TUser {
  @PrimaryColumn({ type: 'varchar', length: 64, comment: 'ID' })
  id: string;

  @Column({ type: 'varchar', length: 50, comment: '真实姓名/用户名' })
  name: string;

  @Column({ type: 'varchar', length: 20, nullable: true, comment: '部门ID' })
  dept_id: string;

  @Column({
    type: 'enum',
    enum: ['F', 'M'],
    nullable: true, // 可以为空
    comment: '性别',
  })
  gender: string;

  @Column({ type: 'date', nullable: true, comment: '出生日期' })
  birthday: string;

  @Column({ type: 'varchar', length: 20, nullable: true, comment: '手机号' })
  phone: string;

  @Column({ type: 'varchar', length: 50, nullable: true, comment: 'Email' })
  email: string;

  @Column({ type: 'varchar', length: 200, nullable: true, comment: '头像地址' })
  avatar: string;

  @Column({ type: 'tinyint', default: 1, comment: '用户状态' })
  status: number;

  @Column({ type: 'tinyint', default: 0, comment: '是否删除' })
  is_deleted: number;

  @CreateDateColumn({ type: 'timestamp', comment: '创建时间' })
  create_at: Date;

  @Column({ type: 'varchar', length: 256, nullable: true })
  create_by: string;

  @UpdateDateColumn({ type: 'timestamp', comment: '更新时间' })
  update_at: Date;

  @Column({ type: 'varchar', length: 256, nullable: true })
  update_by: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '个人简介' })
  profile: string;
}
