import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

// 考核指标表 mysql
// 该表用于存储考核指标的相关信息
@Entity('indicator_classify')
export class IndicatorClassify {
  @PrimaryGeneratedColumn({ name: 'id', type: 'int', comment: '自增ID' })
  id: number;

  @Column({ comment: '父级指标id', type: 'int', default: null, nullable: true })
  parent_id?: number;

  @Column({ comment: '指标名称', type: 'varchar' })
  name: string;
  
  children: IndicatorClassify[]
}
