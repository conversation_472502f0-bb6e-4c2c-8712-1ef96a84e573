import {
  <PERSON><PERSON>ty,
  Column,
  ObjectIdColumn,
  BeforeInsert,
  ManyToOne,
  CreateDateColumn,
  BeforeUpdate,
} from 'typeorm';
import { nanoid } from 'nanoid';
import { PhysicalExamUser } from './physical-exam-user.account.entity';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsString,
} from 'class-validator';

@Entity('physicalExamOrgs')
export class PhysicalExamOrg {
  @ObjectIdColumn()
  _id: string;

  @Column({ comment: '机构名称', unique: true })
  @IsNotEmpty()
  @IsString()
  name: string;

  @Column({ comment: '机构简称', default: '' })
  @IsString()
  shortName: string;

  @Column('机构曾用名-simple-array')
  @IsArray()
  @IsString({ each: true })
  formerNames: string[];

  @Column({ comment: '社会统一信用代码', unique: true })
  @IsNotEmpty()
  @IsString()
  organization: string;

  @Column('注册的省市区')
  @IsArray()
  @IsString({ each: true })
  regAddr: string[];

  @Column('注册详细地址')
  @IsString()
  address: string;

  @Column('法人代表')
  @IsString()
  corp: string;

  @Column('管理员')
  @IsArray()
  managers: string[];

  @Column('联系人')
  @IsString()
  contract: string;

  @Column('联系方式')
  @IsString()
  phoneNum: string;

  @Column({ comment: '业务范围', default: '' })
  @IsString()
  lineOfBusiness: string;

  @Column({ comment: '业务范围', default: '1' })
  @IsString()
  projectNums: string;

  @Column()
  @IsString()
  filingType: string;

  @Column()
  @IsString()
  physicalAddress: string;

  @Column()
  @IsString()
  inspectionItem: string;

  @Column({ comment: '组成形式', default: '' })
  @IsString()
  regType: string;

  @Column('能否开展外出职业健康检查')
  @IsBoolean()
  egress: boolean;

  @Column()
  @IsString()
  introduction: string;

  @CreateDateColumn('创建/更改时间')
  ctime: Date;

  @Column('营业执照')
  @IsString()
  img: string;

  @Column({ default: () => [] })
  @IsArray()
  qualifies: string[];

  @Column()
  @IsString()
  message: string;

  @Column({ type: 'enum', enum: [0, 1, 2, 3, 4, 5], default: 0 })
  @IsNumber()
  status: number;

  @Column()
  @IsString()
  landline: string;

  @ManyToOne(() => PhysicalExamUser)
  administrator: PhysicalExamUser;

  @Column({ default: 'operate' })
  @IsString()
  source: string;

  @Column()
  @IsString()
  officialSeal: string;

  @Column()
  @IsString()
  email: string;

  @Column()
  @IsString()
  bankAccount: {
    accountName: string;
    bankName: string;
    account: string;
  };

  @Column({ default: 'defaultIService2HostValue' }) // 替换为实际的默认值
  @IsString()
  iService2Host: string;

  @Column({ default: 'iService2' })
  @IsString()
  createBy: string;

  constructor() {
    this._id = nanoid();
  }

  @BeforeInsert()
  setDefaults() {
    this.qualifies = this.qualifies || [];
    this.formerNames = this.formerNames || [];
    this.regAddr = this.regAddr || [];
    this.managers = this.managers || [];
    this.lineOfBusiness = this.lineOfBusiness || '';
    this.projectNums = this.projectNums || '';
    this.filingType = this.filingType || '';
    this.physicalAddress = this.physicalAddress || '';
    this.inspectionItem = this.inspectionItem || '';
    this.regType = this.regType || '';
    this.introduction = this.introduction || '';
    this.ctime = this.ctime || new Date();
    this.img = this.img || '';
    this.createBy = this.createBy || 'iService2';
    this.name = this.name || '';
    this.shortName = this.shortName || '';
    this.officialSeal = this.officialSeal || '';
  }

  @BeforeInsert()
  @BeforeUpdate()
  setName() {
    this.name = (this.name || '').replace(/（/g, '(').replace(/）/g, ')');
  }

  @BeforeInsert()
  @BeforeUpdate()
  setShortName() {
    this.shortName = (this.shortName || '')
      .replace(/（/g, '(')
      .replace(/）/g, ')');
  }

  // @BeforeInsert()
  // @BeforeUpdate()
  // setImg() {
  //   const upload_http_path = 'your_upload_http_path'; // 替换为实际的路径
  //   if (this.img && this.img.indexOf(upload_http_path) !== -1) {
  //     this.img = this.img.split(upload_http_path)[1];
  //   }
  // }

  // @BeforeInsert()
  // @BeforeUpdate()
  // setOfficialSeal() {
  //   const upload_http_path = 'your_upload_http_path'; // 替换为实际的路径
  //   if (
  //     this.officialSeal &&
  //     this.officialSeal.indexOf(upload_http_path) !== -1
  //   ) {
  //     this.officialSeal = this.officialSeal.split(upload_http_path)[1];
  //   }
  // }
}
