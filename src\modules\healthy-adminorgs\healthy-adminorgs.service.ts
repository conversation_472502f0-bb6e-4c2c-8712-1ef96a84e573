import { IndicatorClassify } from './mysql/indicator-classify.entity';
import { Injectable, Query } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  IsNull,
  Repository,
  Index,
  getConnection,
  In,
  Like,
  Not,
} from 'typeorm';
import { Indicator } from './mysql/indicator.entity';
import { AssessmentModel } from './mysql/assessment-model.entity';
import { BasicConditions } from './mysql/basic-conditions.entity';
import { AssessmentInfo } from './mysql/assessment-info.entity';
import { Declaration } from './mysql/declaration.entity';
import { Attachment } from './mysql/attachment.entity';
import { DeclarationBasicCondition } from './mysql/declaration_basic_condition.entity';
import { DeclarationIndicator } from './mysql/declaration_indicator.entity';

@Injectable()
export class HealthyAdminorgsService {
  constructor(
    @InjectRepository(IndicatorClassify, 'mysqlConnectionXjbtJkqy')
    private indicatorClassifyRepository: Repository<IndicatorClassify>,

    @InjectRepository(Indicator, 'mysqlConnectionXjbtJkqy')
    private indicatorRepository: Repository<Indicator>,

    @InjectRepository(AssessmentModel, 'mysqlConnectionXjbtJkqy')
    private assessmentModelRepository: Repository<AssessmentModel>,

    @InjectRepository(BasicConditions, 'mysqlConnectionXjbtJkqy')
    private basicConditionsRepository: Repository<BasicConditions>,

    @InjectRepository(AssessmentInfo, 'mysqlConnectionXjbtJkqy')
    private assessmentInfoRepository: Repository<AssessmentInfo>,

    @InjectRepository(Declaration, 'mysqlConnectionXjbtJkqy')
    private declarationRepository: Repository<Declaration>,

    @InjectRepository(Attachment, 'mysqlConnectionXjbtJkqy')
    private attachmentRepository: Repository<Attachment>,

    @InjectRepository(DeclarationBasicCondition, 'mysqlConnectionXjbtJkqy')
    private declarationBasicConditionRepository: Repository<DeclarationBasicCondition>,

    @InjectRepository(DeclarationIndicator, 'mysqlConnectionXjbtJkqy')
    private declarationIndicatorRepository: Repository<DeclarationIndicator>,
  ) {}

  /* 指标分类 */
  async queryIndicatorClassifyListByParentId(id: number) {
    return await this.indicatorClassifyRepository.find({
      where: {
        parent_id: id,
      },
    });
  }

  async queryIndicatorClassifyTreeList() {
    // 1. 获取根节点
    const roots = await this.indicatorClassifyRepository.find({
      where: { parent_id: IsNull() },
    });

    // 2. 查找二级节点
    for (const item of roots) {
      const children = await this.indicatorClassifyRepository.find({
        where: { parent_id: item.id },
      });
      item.children = children;
    }

    return roots; // 此时 roots 已包含完整树形数据
  }

  // 递归方法改造为异步函数
  async recursionIndicatorClassify(nodes: any[]) {
    // 使用 for...of 替代 forEach，确保顺序执行
    for (const node of nodes) {
      // 3. 查询子节点
      const children = await this.indicatorClassifyRepository.find({
        where: { parent_id: node.id },
      });

      if (children.length > 0) {
        // 4. 挂载子节点并继续递归
        node.children = children;
        await this.recursionIndicatorClassify(children); // 等待递归完成
      }
    }
  }

  async saveIndciatorClassify(dto: any) {
    return await this.indicatorClassifyRepository.save(dto);
  }

  async updateIndciatorClassify(dto: any) {
    return await this.indicatorClassifyRepository.update(dto.id, dto);
  }

  async deleteIndciatorClassify(id: number) {
    return this.indicatorClassifyRepository.delete({
      id,
    });
  }

  /* 指标 */
  async queryIndicatorListByParentId(id: number) {
    return await this.indicatorRepository.find({
      where: {
        parent_id: id,
      },
    });
  }

  async queryIndicatorTreeList(id: number) {
    let where: any = {};
    if (id) {
      where.id = id;
    } else {
      where.parent_id = IsNull();
    }
    const resultList: any = [];
    const indicatorClassifyList = await this.indicatorClassifyRepository.find({
      where: {
        ...where,
      },
    });
    for (const indicatorClassify of indicatorClassifyList) {
      if (!indicatorClassify.parent_id) {
        // 一级指标
        // 查询子集节点
        const child = await this.indicatorClassifyRepository.find({
          where: {
            parent_id: indicatorClassify.id,
          },
        });
        indicatorClassify.children = child;
        resultList.push(indicatorClassify);
      } else {
        // 子集指标
        // 判断父级是否已经存在结果列表中
        let parent = resultList.find((item: any) => {
          return (item.id = indicatorClassify.parent_id);
        });
        // 查询父级指标
        if (!parent) {
          parent = await this.indicatorClassifyRepository.findOne({
            where: {
              id: indicatorClassify.parent_id,
            },
          });
        }
        if (parent) {
          if (!parent.children) {
            parent.children = [];
          }
          parent.children.push(indicatorClassify);
          resultList.push(parent);
        }
      }
    }

    // 处理三级指标（指标内容）
    const res = await this.indicatorRepository.find();
    res.forEach((indicator) => {
      for (const item of resultList) {
        if (item.children) {
          for (const ele of item.children) {
            if (ele.id === indicator.parent_id) {
              if (!ele.children) {
                ele.children = [];
              }
              ele.children.push(indicator);
            }
          }
        }
      }
    });

    return this.filterData(resultList);
  }

  filterData(data) {
    // 处理二级：移除没有三级数据的二级
    const filteredLevel1 = data.map((level1) => ({
      ...level1,
      children: (level1.children || []).filter((level2) => {
        // 检查是否存在有效的三级数据
        return (level2.children || []).length > 0;
      }),
    }));

    // 处理一级：移除没有有效二级数据的一级
    const result = filteredLevel1.filter((level1) => {
      return (level1.children || []).length > 0;
    });

    return result;
  }

  async saveIndciator(dto: any) {
    return await this.indicatorRepository.save(dto);
  }

  async updateIndciator(dto: any) {
    return await this.indicatorRepository.update(dto.id, dto);
  }

  async deleteIndciator(id: number) {
    return this.indicatorRepository.delete({
      id,
    });
  }

  /* 评估模型 */
  async saveAssessmentModel(dto: any) {
    const assessmentModel = new AssessmentModel();
    assessmentModel.year = dto.year;
    assessmentModel.release_time = dto.release_time;
    assessmentModel.release_status = dto.release_status;
    assessmentModel.basic_conditions = [];

    if (dto.release_status === '1') {
      assessmentModel.release_time = new Date();
    }

    if (!dto.id) {
      assessmentModel.create_time = new Date();
    } else {
      assessmentModel.id = +dto.id;
    }

    if (dto.basic_conditions && dto.basic_conditions.length) {
      for (const item of dto.basic_conditions) {
        if (item.id) {
          item.id = +item.id;
        }
        if (item.assessment_model_id) {
          item.assessment_model_id = +item.assessment_model_id;
        }
        await this.basicConditionsRepository.save(item);
        assessmentModel.basic_conditions.push(item);
      }
    }

    await this.assessmentModelRepository.save(assessmentModel);

    // 删除
    await this.assessmentInfoRepository.delete({
      assessment_model_id: assessmentModel.id,
    });

    if (dto.assessment_info && dto.assessment_info.length > 0) {
      for (const [index, item] of dto.assessment_info.entries()) {
        const assessmentInfo = new AssessmentInfo();
        if (item.id) {
          assessmentInfo.id = +item.id;
        }
        assessmentInfo.assessment_model_id = +assessmentModel.id;
        assessmentInfo.indicator_id = +item.indicator_id;
        assessmentInfo.score = +item.score;
        assessmentInfo.review_mode = item.review_mode;
        assessmentInfo.score_criteria = item.score_criteria;
        assessmentInfo.indicator_type = item.indicator_type;
        assessmentInfo.order = index;

        await this.assessmentInfoRepository.save(assessmentInfo);
      }
    }
  }

  async queryAssessmentModel(id: number) {
    const res = await this.assessmentModelRepository.findOne({
      where: {
        id,
      },
      relations: {
        basic_conditions: true,
      },
    });

    if (!res) {
      return [];
    }

    const list: any = await this.assessmentInfoRepository.find({
      where: {
        assessment_model_id: id,
      },
      order: {
        order: 'ASC',
      },
    });

    const tree = await this.queryIndicatorTreeList(null);

    if (list && list.length > 0) {
      for (let val of list) {
        tree.forEach((item: any) => {
          item.children.forEach((ele: any) => {
            ele.children.forEach((i) => {
              if (i.id === val.indicator_id) {
                i.item = val;
              }
            });
          });
        });
      }
    }

    const res1 = this.filterTreeData(tree);

    this.sortTreeByOrder(res1);

    res.assessment_info = res1;

    return res;
  }

  filterTreeData(data) {
    // 深拷贝原始数据避免污染原数据
    const clonedData = JSON.parse(JSON.stringify(data));

    return clonedData.filter((level1) => {
      // 处理一级节点
      level1.children = level1.children.filter((level2) => {
        // 处理二级节点
        level2.children = level2.children.filter(
          (level3) => level3.item, // 过滤三级节点
        );
        return level2.children.length > 0; // 保留有子节点的二级节点
      });
      return level1.children.length > 0; // 保留有子节点的一级节点
    });
  }

  sortTreeByOrder(nodes) {
    if (!nodes || !nodes.length) return;

    nodes.forEach((node) => {
      if (node.children) {
        this.sortTreeByOrder(node.children);
      }
    });

    const getSortKey = (node) => {
      if (node.item?.order !== undefined) return node.item.order;
      if (node.children?.length) return getSortKey(node.children[0]);
      return Infinity;
    };

    nodes.sort((a, b) => getSortKey(a) - getSortKey(b));
  }

  async queryAssessmentInfoList(id: number) {
    return await this.assessmentInfoRepository.find({
      where: {
        indicator_id: id,
      },
    });
  }

  async queryAssessmentModelList(query: any) {
    const { page = 1, pageSize = 10 } = query;
    const skip = (page - 1) * pageSize;
    const take = pageSize;

    const where: any = {};

    if (query.year) {
      where.year = query.year;
    }

    if (query.release_status) {
      where.release_status = query.release_status;
    }
    const [records, total] = await this.assessmentModelRepository.findAndCount({
      skip,
      take,
      where,
    });

    return {
      records,
      total,
    };
  }

  async enableAssessmenModel(dto: any) {
    return await this.assessmentModelRepository.update(
      {
        id: +dto.id,
      },
      dto,
    );
  }

  async deleteAssessmentModel(id: number) {
    await this.assessmentModelRepository.delete({ id });
    await this.basicConditionsRepository.delete({
      assessment_model_id: id,
    });
    await this.assessmentInfoRepository.delete({
      assessment_model_id: id,
    });
    return;
  }

  /* 申报 */
  async queryDeclarationList(query: any) {
    console.log('query', query);
    const { page = 1, pageSize = 10 } = query;
    const skip = (page - 1) * pageSize;
    const take = pageSize;

    const where: any = {};

    if (query.enterprise_id) {
      where.enterprise_id = query.enterprise_id;
    }

    if (query.unit_name) {
      where.unit_name = Like(`%${query.unit_name}%`);
    }

    if (query.year) {
      where.year = query.year;
    }

    if (query.declaration_status) {
      where.declaration_status = query.declaration_status;
    } else {
      if (query.type === '0') {
        where.declaration_status = Not(IsNull());
      } else if (query.type === '1') {
        where.declaration_status = In(['3', '4', '5']);
      }
    }

    if (query.is_submit) {
      where.is_submit = query.is_submit;
    }

    console.log(where);

    const [records, total] = await this.declarationRepository.findAndCount({
      where,
      skip,
      take,
    });

    return {
      records,
      total,
    };
  }

  async queryDeclaration(id: number) {
    const res = await this.declarationRepository.findOne({
      where: {
        id,
      },
      relations: {
        declaration_basic_condition: true,
        attachment: true,
      },
    });

    // 处理指标数据
    const indicator = await this.declarationIndicatorRepository.find({
      where: {
        parent_id: IsNull(),
        declaration_id: id,
      },
    });
    await this.queryTreeIndicator(indicator);

    res.declaration_indicator = indicator;

    return res;
  }

  async queryTreeIndicator(list: any) {
    if (!(list && list.length > 0)) return;
    for (const item of list) {
      const res = await this.declarationIndicatorRepository.find({
        where: {
          parent_id: item.id,
        },
      });
      if (res && res.length > 0) {
        item.children = res;
        await this.queryTreeIndicator(res);
      }
    }
  }

  async queryAssessmentModelDictionary() {
    return await this.assessmentModelRepository.find({
      where: {
        release_status: '1',
      },
    });
  }

  async saveDeclaration(dto: any) {
    const declaration = new Declaration();

    if (dto.id) {
      declaration.id = +dto.id;

      // 删除之前的数据
      await this.declarationBasicConditionRepository.delete({
        declaration_id: declaration.id,
      });
    }

    declaration.enterprise_id = dto.enterprise_id;
    declaration.assessment_model_id = +dto.assessment_model_id;
    declaration.unit_name = dto.unit_name;
    declaration.year = dto.year;
    declaration.self_assessment_status = dto.self_assessment_status;
    if (!dto.self_assessment_time) {
      declaration.self_assessment_time = new Date();
      declaration.declaration_time = new Date();
    }
    declaration.declaration_status = dto.declaration_status;
    declaration.score = +dto.score;
    if (dto.city_score) {
      declaration.city_score = +dto.city_score;
    }
    declaration.city_assessment_status = dto.city_assessment_status;
    if (!declaration.city_review_time) {
      declaration.city_review_time = new Date();
    }
    if (dto.province_score) {
      declaration.province_score = +dto.province_score;
    }
    declaration.code = dto.code;
    declaration.companyScale = dto.companyScale;
    declaration.contract = dto.contract;
    declaration.phoneNum = dto.phoneNum;
    declaration.regAdd = dto.regAdd;
    declaration.regType = dto.regType;
    declaration.province_assessment_status = dto.province_assessment_status;
    declaration.declaration_basic_condition = [];
    declaration.attachment = [];

    if (
      dto.declaration_basic_condition &&
      dto.declaration_basic_condition.length > 0
    ) {
      for (const item of dto.declaration_basic_condition) {
        if (item.id) {
          item.id = +item.id;
        }
        if (item.declaration_id) {
          item.declaration_id = +item.declaration_id;
        }
        await this.declarationBasicConditionRepository.save(item);
        declaration.declaration_basic_condition.push(item);
      }
    }

    // 处理附件
    if (dto.attachment && dto.attachment.length > 0) {
      for (const file of dto.attachment) {
        const attachment = new Attachment();
        if (dto.id) {
          attachment.id = +file.id;
        }
        // if (file.declaration_id) {
        //   attachment.declaration_id = +file.declaration_id;
        // }
        attachment.file_name = file.file_name;
        attachment.static_name = file.static_name;
        attachment.file_type = file.file_type;

        await this.attachmentRepository.save(attachment);
        declaration.attachment.push(attachment);
      }
    }

    await this.declarationRepository.save(declaration);

    // 删除之前的指标数据
    await this.declarationIndicatorRepository.delete({
      declaration_id: declaration.id,
    });

    // 处理指标
    if (dto.declaration_indicator && dto.declaration_indicator.length > 0) {
      for (const item1 of dto.declaration_indicator) {
        const declarationIndicator1 = new DeclarationIndicator();
        // if (item1.id) {
        //   declarationIndicator1.id = +item1.id;
        // }
        declarationIndicator1.declaration_id = +declaration.id;
        // if (item1.parent_id) {
        //   declarationIndicator.parent_id = +item1.parent_id;
        // }
        declarationIndicator1.name = item1.name;

        await this.declarationIndicatorRepository.save(declarationIndicator1);

        if (item1.children && item1.children.length) {
          for (const item2 of item1.children) {
            const declarationIndicator2 = new DeclarationIndicator();
            // if (item2.id) {
            //   declarationIndicator2.id = +item2.id;
            // }
            declarationIndicator2.declaration_id = +declaration.id;
            declarationIndicator2.parent_id = declarationIndicator1.id;
            declarationIndicator2.name = item2.name;

            await this.declarationIndicatorRepository.save(
              declarationIndicator2,
            );

            if (item2.children && item2.children.length) {
              for (const item3 of item2.children) {
                const declarationIndicator = new DeclarationIndicator();
                // if (item3.id) {
                //   declarationIndicator.id = +item3.id;
                // }
                declarationIndicator.declaration_id = +declaration.id;
                declarationIndicator.parent_id = declarationIndicator2.id;
                declarationIndicator.name = item3.name;
                declarationIndicator.score = +item3.score;
                declarationIndicator.review_mode = item3.review_mode;
                declarationIndicator.score_criteria = item3.score_criteria;
                declarationIndicator.indicator_type = item3.indicator_type;
                if (item3.self_score) {
                  declarationIndicator.self_score = +item3.self_score;
                }
                declarationIndicator.self_missing = item3.self_missing;
                if (item3.city_score) {
                  declarationIndicator.city_score = +item3.city_score;
                }
                declarationIndicator.city_missing = item3.city_missing;
                if (item3.province_score) {
                  declarationIndicator.province_score = +item3.province_score;
                }
                declarationIndicator.province_missing = item3.province_missing;

                await this.declarationIndicatorRepository.save(
                  declarationIndicator,
                );
              }
            }
          }
        }
      }
    }
  }

  // 初审需修改
  async rejectDeclaration(dto: any) {
    return await this.declarationRepository.update(dto.id, {
      reason: dto.reason,
      declaration_status: '1',
    });
  }

  // 提交兵团
  async submitDeclaration(dto: any) {
    return await this.declarationRepository.update(dto.id, {
      is_submit: dto.is_submit,
    });
  }
}
