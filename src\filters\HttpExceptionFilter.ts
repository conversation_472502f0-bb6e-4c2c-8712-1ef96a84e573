import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
} from '@nestjs/common';
import { Response } from 'express';

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const status = exception.getStatus();
    const exceptionResponse = exception.getResponse();

    // 统一处理所有错误响应
    const errorResponse = typeof exceptionResponse === 'object' 
      ? exceptionResponse 
      : { message: exceptionResponse };

    // 获取错误消息
    let message = exception.message;
    if (errorResponse['message']) {
      message = Array.isArray(errorResponse['message'])
        ? errorResponse['message'].join(',')  // 数组取第一个错误信息
        : errorResponse['message'];
    }

    response.status(status).json({
      code: status,
      message,
      data: {
        ...errorResponse,
        stack: process.env.NODE_ENV === 'development' ? exception.stack : undefined
      }
    });
  }
}
