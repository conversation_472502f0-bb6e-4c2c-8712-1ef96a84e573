import { Module, Global } from '@nestjs/common';
import { ConfigFactory } from '@nestjs/config';

// config files
import defaultConfig from './config/default';
import developmentConfig from './config/development';
import productionConfig from './config/production';
import fzConfig from './config/fz';
import wkzwyConfig from './config/wkzwy';
import sitConfig from './config/sit';
import hzConfig from './config/hz';
import sxccConfig from './config/sxcc';
import xjbtConfig from './config/xjbt';

const envConfig = {
  development: developmentConfig,
  sit: sitConfig,
  production: productionConfig,
  fz: fzConfig,
  wkzwy: wkzwyConfig,
  hz: hzConfig,
  sxcc: sxccConfig,
  xjbt: xjbtConfig,
};

const currentConfig =
  envConfig[process.env.NODE_ENV || 'development'] || (() => ({})); // 单元测试环境变量下没有适用的配置，使用默认配置

@Global()
@Module({})
export class GlobalModule {
  static config: object = { ...defaultConfig(), ...currentConfig() };
  static currentConfig: ConfigFactory = currentConfig;
  static defaultConfig: ConfigFactory = defaultConfig;
}
