import { nanoid } from 'nanoid';
import {
  Entity,
  Column,
  ObjectIdColumn,
  Index,
  CreateDateColumn,
  UpdateDateColumn,
  BeforeUpdate,
} from 'typeorm';

// 企业用人单位 信息
@Entity('adminorgs')
export class AdminOrgsOrg {
  @ObjectIdColumn()
  _id: string;

  @Column({ comment: '单位名称' })
  cname: string;

  @Column({ comment: '统一社会信用代码' })
  @Index()
  code: string;

  @Column({ comment: '注册地址' })
  regAdd: string;

  @Column({ comment: '唯一编码' })
  @Index()
  unitCode: string;

  @Column({ comment: '联系方式' })
  phoneNum: string;

  @Column({ comment: '管理员ID' })
  adminUserId: string;

  @Column({ comment: '申请状态' })
  isactive: string; // 0 未审核，0.5.暂不审核（为了排序/(ㄒoㄒ)/~~）， 1 审核通过，2 申请打回，3.未注册，4 体验账号

  @Column({ comment: '' })
  group: string;

  @Column({ type: 'array', default: [] })
  adminArray: string[];

  @Column({ type: 'boolean', default: false })
  isDelete: boolean;

  @Column({ type: 'string' })
  productionStatus: string;

  @CreateDateColumn({ default: () => new Date() })
  createTime: Date;

  @UpdateDateColumn({ default: () => new Date() })
  updateTime: Date;

  constructor() {
    this._id = this._id || nanoid();
    this.cname = this.cname || '';
    this.code = this.code || '';
    this.unitCode = this.unitCode || '';
    this.phoneNum = this.phoneNum || '';
    this.adminUserId = this.adminUserId || '';
    this.adminArray = this.adminArray || [];
    this.isactive = this.isactive || '1';
    this.isDelete = this.isDelete || false;
    this.productionStatus = this.productionStatus || '2';
  }

  @BeforeUpdate()
  async beforeUpdate() {
    console.log('开始更新用人单位信息', this._id, this);
  }
}
