export default () => ({
  database_mongodb: {
    name: 'mongodbConnection',
    type: process.env.mdbScheme || 'mongodb',
    replicaSet: process.env.mdbRs,
    // set host for none replica set, and hostReplicaSet for replica set
    hostReplicaSet: 'i11.zyws.cn,i12.zyws.cn,i13.zyws.cn',
    readPreference: 'nearest',
    ssl: false,
    useNewUrlParser: true,
    useUnifiedTopology: true,
    authSource: 'admin',
    username: process.env.mdbUser,
    password: process.env.mdbPass,
    database: process.env.mdbName,
    logging: 'info',
  },
  database_sqlserver: {
    name: 'sqlserverConnection',
    type: 'mssql',
    host: process.env.hfSqlserverHost,
    port: +process.env.hfSqlserverPort,
    username: process.env.hfSqlserverUser,
    password: process.env.hfSqlserverPass,
    database: process.env.hfSqlserverDb,
    logging: 'all',
    options: {
      trustServerCertificate: true, // 信任服务器的自签名证书
    },
    synchronize: false,
  },
});
