import { Entity, ObjectIdColumn, Column, BeforeInsert } from 'typeorm';
import * as shortid from 'shortid';

@Entity('groupEnterprisesRecords')
export class GroupEnterprisesRecords {
  @ObjectIdColumn()
  _id: string;

  // 企业id 发起企业
  @Column({ type: 'varchar', comment: '企业id 发起企业' })
  EnterpriseID: string;

  // 目标企业id
  @Column('varchar', { comment: '目标企业id' })
  targetEnterpriseID: string;

  // Boolean
  @Column({
    type: 'boolean',
    default: true,
    comment: '用于扩展的申请类型，true为添加上级单位，false为添加下级单位',
  })
  isUp: boolean;

  // 消息状态
  @Column({
    type: 'int',
    default: 0,
    comment: '消息状态 0 无申请 1 待审核 2 通过 3 拒绝',
  })
  messageStatus: number;

  @BeforeInsert()
  setDefaultId() {
    if (!this._id) {
      this._id = shortid.generate();
    }
  }
}
