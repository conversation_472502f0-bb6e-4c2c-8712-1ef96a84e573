// 本地上传文件 存储引擎
import * as multer from 'multer';

export const storage = multer.diskStorage({
  destination: './public/uploads', // 目录配置（需要在config.js中配置）
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9); // 文件名
    const fileType = file.mimetype.split('/')[1]; // 获取文件类型
    cb(null, file.fieldname + '-' + uniqueSuffix + '.' + fileType);
  },
});
