import {
  IsBoolean,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Length,
} from 'class-validator';
import { nanoid } from 'nanoid';
import { Column, Entity, ObjectIdColumn } from 'typeorm';

export type Member = {
  _id: string;
  name: string;
  phoneNum: string;
  userName: string;
  roles?: [];
  jobTitle?: string;
};

@Entity('superusers')
export class SuperUser {
  @ObjectIdColumn()
  _id: string;

  @IsNotEmpty()
  @IsNumber()
  @Column({
    type: 'int',
    nullable: false,
    enum: [1, 2, 3, 4],
  }) // 单位类型：1 卫生健康委 2 监督所 3 疾控中心 4 职防院
  type: number;

  @Column({
    length: 12,
    nullable: false,
  })
  @IsString()
  @Length(12, 12)
  @IsNotEmpty()
  area_code: string;

  @Column()
  @IsOptional()
  @IsString()
  unifiedCode: string;

  @Column()
  @IsString()
  @IsNotEmpty()
  userName: string;

  @Column()
  @IsString()
  @IsNotEmpty()
  cname: string;

  @Column({ nullable: true })
  @IsString()
  @IsOptional()
  name: string;

  @Column({ nullable: true })
  @IsString()
  @IsOptional()
  jobTitle: string;

  @Column({ nullable: true })
  @IsEmail()
  @IsOptional()
  email: string;

  @Column({ nullable: true })
  // @IsPhoneNumber()
  @IsOptional()
  phoneNum: string;

  @Column({ nullable: true })
  @IsString()
  @IsOptional()
  landline: string;

  @Column({ default: '86' })
  @IsString()
  countryCode: string;

  @Column({ default: true })
  @IsBoolean()
  enable: boolean; // 账号是否受限

  @Column({ default: '1' })
  @IsEnum(['0', '1'])
  state: string; // 账号状态 1正常，0删除

  @Column({ type: 'array' })
  regAdd: string[];

  @Column({ type: 'array' })
  members: Member[];

  constructor() {
    this._id = nanoid();
    this.enable = this.enable === false ? false : true;
    this.countryCode = '86';
    this.state = this.state || '1';
    this.cname = '';
    this.regAdd = [];
    this.members = [];
  }
}
