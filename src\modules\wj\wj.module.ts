import { Module } from '@nestjs/common';
import { Wj<PERSON><PERSON>roller } from './wj.controller';
import { WjService } from './wj.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TUser } from './mysql/t_user.entity';
import { TAccount } from './mysql/t_account.entity';
import { TRole } from './mysql/t_role.entity';
import { TUserRole } from './mysql/t_user_role.entity';
import { TDept } from './mysql/t_dept.entity';
import { CustomJwtModule } from '../jwt/jwt.module';
// 职业健康问卷系统
@Module({
  imports: [
    TypeOrmModule.forFeature([TUser], 'mysqlConnection'),
    TypeOrmModule.forFeature([TAccount], 'mysqlConnection'),
    TypeOrmModule.forFeature([TRole], 'mysqlConnection'),
    TypeOrmModule.forFeature([TUserRole], 'mysqlConnection'),
    TypeOrmModule.forFeature([TDept], 'mysqlConnection'),
    CustomJwtModule,
  ],
  controllers: [WjController],
  providers: [WjService],
})
export class WjModule {}
