import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
} from '@nestjs/common';
import { ExpertService } from './expert.service';
import { ExpertStatisticsService } from './expert-statistics.service';
import { Expert } from './mysql/expert.entity';
import { wrapperResponse, error } from 'src/utils';
import { ExpertReviewService } from './expert-review.service';
import { ExpertExtractionRecordsService } from './expert-extraction-records.service';
import { RandomExtractDto } from './dto/expert-random-extract.dto';
import { ExpertWorkService } from './expert_work.service';

@Controller('expert')
export class ExpertController {
  constructor(
    private readonly expertService: ExpertService,
    private readonly expertStatisticsService: ExpertStatisticsService,
    private readonly expertReviewService: ExpertReviewService,
    private readonly expertExtractionRecordsService: ExpertExtractionRecordsService,
    private readonly expertWorkService: ExpertWorkService,
  ) {}

  @Get()
  findAll(@Query() query: any): Promise<Expert[]> {
    return wrapperResponse(this.expertService.findAll(query), '获取专家列表');
  }

  @Get(':id')
  findOne(@Param('id') id: number) {
    return wrapperResponse(this.expertService.findOne(id), '获取专家详情');
  }

  // 专家端用户登录
  @Post('sso')
  findOne2(@Body() body: any) {
    return wrapperResponse(
      this.expertService.findOne2(body),
      '专家端单点登录，获取专家详情',
    );
  }

  // 统一门户单点登录
  @Post('sso2')
  sso(@Body() body: any) {
    return wrapperResponse(
      this.expertService.sso(body),
      '统一门户单点登录，获取专家详情',
    );
  }

  @Post()
  create(@Body() createDto: Expert) {
    return wrapperResponse(this.expertService.create(createDto), '创建专家');
  }

  @Put(':id')
  update(@Param('id') id: number, @Body() updateDto: Expert) {
    return wrapperResponse(
      this.expertService.update(id, updateDto),
      '更新专家信息',
    );
  }

  @Delete(':id')
  remove(@Param('id') id: number) {
    return wrapperResponse(this.expertService.remove(id), '删除专家');
  }

  @Get('statistics/byType')
  statisticsByType() {
    return wrapperResponse(
      this.expertStatisticsService.statisticsByType(),
      '专家类型分布情况',
    );
  }

  @Get('statistics/byAge')
  statisticsByAge() {
    return wrapperResponse(
      this.expertStatisticsService.statisticsByAge(),
      '专家年龄、性别分布情况',
    );
  }

  @Get('statistics/byEducation')
  statisticsByEducation() {
    return wrapperResponse(
      this.expertStatisticsService.statisticsByEducation(),
      '专家年龄、学历分布情况',
    );
  }

  @Get('statistics/byMobility')
  statisticsByMobility(@Query() query: any) {
    return wrapperResponse(
      this.expertStatisticsService.statisticsByMobility(query),
      '专家流动情况',
    );
  }

  @Post('recommend')
  recommend(@Body() body: any) {
    return wrapperResponse(
      this.expertReviewService.recommend(body),
      '专家推荐',
    );
  }

  @Post('dismissal')
  dismissal(@Body() body: any) {
    return wrapperResponse(
      this.expertReviewService.dismissal(body),
      '专家解聘',
    );
  }

  @Post('review')
  createReview(@Body() body: any) {
    return wrapperResponse(
      this.expertReviewService.create(body),
      '添加待审核记录',
    );
  }

  @Put('declaration/review')
  updateReview(
    @Body()
    body: {
      review_status: number;
      id: number;
      review_comment?: string;
      reviewer: string;
    },
  ) {
    return wrapperResponse(
      this.expertReviewService.update(body.id, body),
      '更新审核记录',
    );
  }

  @Delete('declaration/review')
  deleteReview(
    @Query()
    body: {
      id: number;
      expert_id?: number;
    },
  ) {
    return wrapperResponse(
      this.expertReviewService.delete({
        id: +body.id,
        expert_id: +body.expert_id,
      }),
      '取消审核记录',
    );
  }

  @Get('review/list')
  reviewList(@Query() query: any) {
    return wrapperResponse(
      this.expertReviewService.list(query),
      '获取专家审核列表',
    );
  }

  @Get('extraction/random')
  extraction(@Query() query: RandomExtractDto): Promise<Expert[]> {
    if (typeof query.area_code === 'string') {
      query.area_code = [query.area_code];
    }
    if (typeof query.category_id === 'string') {
      query.category_id = [query.category_id];
    }
    if (typeof query.exclude_expert_id === 'string') {
      query.exclude_expert_id = [query.exclude_expert_id];
    }
    return wrapperResponse(
      this.expertService.randomExtract(query),
      '随机抽取专家',
    );
  }

  @Get('extraction/list')
  expertExtractionRecords(@Query() query: any) {
    return wrapperResponse(
      this.expertExtractionRecordsService.list(query),
      '获取专家抽取记录列表',
    );
  }

  @Post('work')
  async createWork(@Body() body: any) {
    const { expert_ids, management_element, work_item } = body;
    if (!Array.isArray(expert_ids)) {
      return error('传参错误，expert_ids必须为数组。');
    }
    if (!management_element || !work_item) {
      return error('传参错误，management_element和work_item不能为空。');
    }
    // 校验每个专家是否存在
    for (let i = 0; i < expert_ids.length; i++) {
      const expert = await this.expertService.findInfo(+expert_ids[i]);
      if (!expert) {
        return error(`专家id：${expert_ids[i]} 不存在`);
      }
    }
    // 给每个专家创建工作任务
    const createWorkPromises = expert_ids.map((expert_id) =>
      this.expertWorkService.create({
        expert_id: +expert_id,
        ...body,
      }),
    );
    return wrapperResponse(
      Promise.all(createWorkPromises),
      '给专家创建工作任务',
    );
  }

  @Put('work/:id')
  async updateWork(@Param('id') id: number, @Body() body: any) {
    try {
      if (!id) {
        return error('传参错误，id不能为空。');
      }
      if (body.status && ![1, 2, 3, 4, 5].includes(+body.status)) {
        return error('传参错误，status必须为1、2、3、4、5中的一个。');
      }
      const work = await this.expertWorkService.findOne(+id);
      if (!work) {
        return error('工作任务id不存在');
      }
      return wrapperResponse(
        this.expertWorkService.update(id, body),
        '更新工作任务',
      );
    } catch (e) {
      console.log('更新工作任务失败: ' + e.message);
      return error('更新工作任务失败: ' + e.message);
    }
  }

  @Get('work/list')
  workList(@Query() query: any) {
    return wrapperResponse(
      this.expertWorkService.findAll(query),
      '获取工作任务列表',
    );
  }

  @Post('healthUnit/assignDeclaration')
  assignDeclaration(
    @Body()
    body: {
      declarationId: string;
      expertIds: string[];
    },
  ) {
    return wrapperResponse(
      this.expertService.assignDeclaration(body),
      '分配健康企业申报评审',
    );
  }
}
