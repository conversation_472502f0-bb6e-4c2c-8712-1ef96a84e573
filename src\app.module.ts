import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { GlobalModule } from './global.module';
import { AppService, MyService } from './app.service';
import { AppController } from './app.controller';
// import Module
import { DpStatisticsModule } from './modules/dp-statistics/dp-statistics.module';
import { FzDpModule } from './modules/fz-dp/fz-dp.module';
import { FzLabanalysisModule } from './modules/fz-labanalysis/fz-labanalysis.module';
import { HealthCheckAppointmentModule } from './modules/health-check-appointment/health-check-appointment.module';
import { WkzwyAppointmentModule } from './modules/wkzwy-appointment/wkzwy-appointment.module';
import { AdminorgsModule } from './modules/adminorgs/adminorgs.module';
import { RedisModule } from './modules/redis/redis.module';
import { SystemConfigsModule } from './modules/system-configs/system-configs.module';
import { SxccduijieModule } from './modules/sxccduijie/sxccduijie.module';
import { WjModule } from './modules/wj/wj.module';
import { CustomJwtModule } from './modules/jwt/jwt.module';
import { CertificateModule } from './modules/certificate/certificate.module'; // 资质证书
import { ExpertModule } from './modules/expert/expert.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ApprovalModule } from './modules/approval/approval.module';
import { TodoModule } from './modules/todo/todo.module';
import { DictionaryModule } from './modules/dictionary/dictionary.module';
import { HebeiModule } from './modules/hebei/hebei.module';
import { DakModule } from './modules/dak/dak.module'; // xjbt档案库
import { generateMultipleDataSourceConfig } from './utils/mutil-data-source-config';
import { XjbtSmzqModule } from './modules/xjbt-smzq/xjbt-smzq.module';
import { HealthyAdminorgsModule } from './modules/healthy-adminorgs/healthy-adminorgs.module';

@Module({
  imports: [
    // 配置模块
    GlobalModule,
    EventEmitterModule.forRoot(),
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      load: [GlobalModule.defaultConfig, GlobalModule.currentConfig],
    }),
    // 数据库模块
    ...(GlobalModule.config['database_mongodb']
      ? [
          TypeOrmModule.forRoot({
            ...GlobalModule.config['database_mongodb'],
            entities: [__dirname + '/**/*.entity{.ts,.js}'],
          }),
        ]
      : []),
    ...(GlobalModule.config['database_oracle']
      ? [
          TypeOrmModule.forRoot({
            ...GlobalModule.config['database_oracle'],
            entities: [__dirname + '/**/oracle/*.entity{.ts,.js}'],
          }),
        ]
      : []),
    ...(GlobalModule.config['database_lab_oracle']
      ? [
          TypeOrmModule.forRoot({
            ...GlobalModule.config['database_lab_oracle'],
            entities: [__dirname + '/**/oracle/*.entity{.ts,.js}'],
          }),
        ]
      : []),
    ...(GlobalModule.config['database_sqlserver']
      ? [
          TypeOrmModule.forRoot({
            ...GlobalModule.config['database_sqlserver'],
            entities: [__dirname + '/**/sqlserver/*.entity{.ts,.js}'],
          }),
        ]
      : []),
    ...(GlobalModule.config['database_mysql']
      ? [
          TypeOrmModule.forRoot({
            ...GlobalModule.config['database_mysql'],
            entities: [__dirname + '/**/mysql/*.entity{.ts,.js}'],
          }),
        ]
      : []),
    ...(GlobalModule.config['database_mysql_iservice']
      ? [
          TypeOrmModule.forRoot({
            ...GlobalModule.config['database_mysql_iservice'],
            entities: [__dirname + '/**/mysql/*.entity{.ts,.js}'],
          }),
        ]
      : []),
    ...(GlobalModule.config['database_mysql_xjbt']
      ? [
          TypeOrmModule.forRoot({
            ...GlobalModule.config['database_mysql_xjbt'],
            entities: [__dirname + '/**/mysql/*.entity{.ts,.js}'],
          }),
        ]
      : []),
    ...(GlobalModule.config['database_mysql_xjbt_jkqy']
      ? [
          TypeOrmModule.forRoot({
            ...GlobalModule.config['database_mysql_xjbt_jkqy'],
            entities: [__dirname + '/**/mysql/*.entity{.ts,.js}'],
          }),
        ]
      : []),
    ...generateMultipleDataSourceConfig(
      GlobalModule.config['mysql_data_sources_hb'],
      [
        __dirname + '/**/mysql/*.entity{.ts,.js}',
        __dirname +
          '/**/enterprise-declare-dashboard/entities/*.entity{.ts,.js}',
      ],
    ),
    // 子模块
    DpStatisticsModule,
    HealthCheckAppointmentModule,
    ...(GlobalModule.config['database_oracle'] ? [FzDpModule] : []),
    ...(GlobalModule.config['database_lab_oracle']
      ? [FzLabanalysisModule]
      : []),
    ...(GlobalModule.config['database_sqlserver']
      ? [WkzwyAppointmentModule]
      : []),
    ...(GlobalModule.config['database_redis'] ? [RedisModule] : []),
    AdminorgsModule,
    CustomJwtModule, // jwt模块
    SystemConfigsModule, // 系统配置模块
    SxccduijieModule,
    DakModule, // 诊断鉴定记录模块
    ...(GlobalModule.config['mysql_data_sources_hb'] ? [HebeiModule] : []),
    ...(GlobalModule.config['database_mysql'] ? [WjModule] : []),
    ...(GlobalModule.config['database_mysql_iservice'] // 新疆项目
      ? [
          CertificateModule,
          ExpertModule,
          TodoModule,
          ApprovalModule,
          DictionaryModule,
          HealthyAdminorgsModule,
        ]
      : []),
    ...(GlobalModule.config['database_mysql_xjbt'] // 新疆项目
      ? [HebeiModule]
      : []),
    XjbtSmzqModule,
  ],
  controllers: [AppController],
  providers: [AppService, MyService],
})
export class AppModule {}
