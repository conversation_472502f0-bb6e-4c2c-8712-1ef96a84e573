import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

@Entity('tj_health_company')
export class tjHealthCompany {
  @PrimaryGeneratedColumn({ type: 'int', comment: '序号' })
  id: number;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '统一社会信用代码' })
  unified_code: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '区划' })
  district_id: string;

  @Column({ type: 'datetime', nullable: true, comment: '首次备案时间' })
  first_record_time: Date;

  @Column({ type: 'datetime', nullable: true, comment: '最新变更完成日期' })
  update_record_time: Date;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '机构名称' })
  name: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '单位地址' })
  address: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '固定电话' })
  gddh: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '邮编' })
  yb: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '法定代表人' })
  fddbr: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '职务/职称' })
  post: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '主检医师' })
  zjys: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '联系人' })
  lxr: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '联系人手机号' })
  lxr_phone: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '传真' })
  cz: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '单位电子邮箱' })
  email: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '检查信息报送人员' })
  jcxxbsry: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '医疗机构职业许可证发放机关' })
  xkzffjg: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '申请外出职业健康检查区域' })
  sqwcjcqy: string;

  @Column({ type: 'text', nullable: true, comment: '医疗机构职业执业许可证' })
  zyxkz_file_path: string;

  @Column({ type: 'text', nullable: true, comment: '放射诊疗许可证' })
  fszlxkz_file_path: string;

  @Column({ type: 'text', nullable: true, comment: '检查场所平面图' })
  jccspmt_file_path: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '第三方id' })
  three_id: string;

  @Column({ type: 'text', nullable: true, comment: '职业健康检查质量管理制度文件目录' })
  zdglwj_file_path: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '状态' })
  state: string;

  @Column({ type: 'datetime', nullable: true, comment: '审批时间' })
  audit_time: Date;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '是否失效：0正常1失效（签发后上一条记录失效）' })
  lose_status: string;

  @Column({ type: 'varchar', length: 10, nullable: true, comment: '备注' })
  remark: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '创建人' })
  create_by: string;

  @Column({ type: 'datetime', comment: '创建时间' })
  create_time: Date;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '修改人' })
  update_by: string;

  @Column({ type: 'datetime', comment: '修改时间' })
  update_time: Date;
}
