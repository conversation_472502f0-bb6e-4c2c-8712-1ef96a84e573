import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

// 行政区域编码
@Entity('expert_classifier')
export class ExpertClassifier {
  @PrimaryGeneratedColumn({ comment: '自增ID' })
  id: number;

  @Column({ type: 'varchar', length: 40, comment: '专家分类名称' })
  classification_name: string;

  @Column({
    type: 'varchar',
    length: 40,
    nullable: true,
    comment: '要求任职年限',
  })
  employment_years?: string;

  @Column({ type: 'varchar', length: 64, nullable: true, comment: '专业要求' })
  professional_requirements?: string;

  @Column({
    type: 'varchar',
    length: 128,
    nullable: true,
    comment: '工作经历要求',
  })
  job_requirements?: string;

  @Column({
    type: 'varchar',
    length: 128,
    nullable: true,
    comment: '所需上报材料',
  })
  submit_materials?: string;

  @Column({
    type: 'boolean',
    default: true,
    nullable: true,
    comment: '是否可用, 默认可用',
  })
  enable?: boolean;
}
