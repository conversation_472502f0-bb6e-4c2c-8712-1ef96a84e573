import { Entity, Column, PrimaryColumn } from 'typeorm';
import { GlobalModule } from '../../../global.module';

const schema = (
  GlobalModule?.config['database_lab_oracle']?.database || 'SYSTEM'
).toUpperCase();

@Entity({
  name: 'LIMS_ZYWS_CHKRSLTM',
  schema,
  comment: '职业卫生实验室-福州检测项目-原始记录表',
})
export class LIMS_ZYWS_CHKRSLTM {
  @PrimaryColumn({ type: 'varchar2', comment: '检测结果值序号' })
  RESULTSEQ: number;

  @Column({ type: 'varchar2', nullable: true, length: 200, comment: '备注' })
  REMARK: string;

  @Column({
    type: 'blob',
    nullable: true,
    comment: '原始单文件',
  })
  FCONT: Buffer;

  @Column({ type: 'varchar2', nullable: true, comment: '仪器编号' })
  FINSTNOS: string;

  @Column({ type: 'varchar2', nullable: true, comment: '仪器名称' })
  FINSTNMS: string;

  @Column({ type: 'varchar2', nullable: true, comment: '仪器型号' })
  FINSTXHS: string;

  @Column({ type: 'varchar2', nullable: true, comment: '仪器类型' })
  FINSTLXS: string;
}
