import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';
// 职业卫生大屏-职业卫生科室工作量-列表
@Entity({
  name: 'DP_ZYWSDP_ZYWSKSGZL_LB',
  comment: '职业卫生大屏-职业卫生科室工作量-列表',
})
export class DP_ZYWSDP_ZYWSKSGZL_LB {
  @PrimaryGeneratedColumn()
  ID: number;

  @Column({ type: 'varchar2', length: 255, comment: '时间' })
  SJ: string;

  @Column({ type: 'varchar2', length: 255, comment: '监测单位总数' })
  JCDWZS: string;

  @Column({ type: 'varchar2', length: 255, comment: '监测单位分布区域' })
  JCDWFBQY: string;

  @Column({ type: 'varchar2', length: 255, comment: '行业类别' })
  HYLB: string;

  @Column({ type: 'varchar2', length: 255, comment: '监测单位规模' })
  JCDWGM: string;

  @Column({ type: 'varchar2', length: 255, comment: '本月监测完成数' })
  BYJCWCS: string;

  @Column({ type: 'varchar2', length: 255, comment: '待监测完成数' })
  DJCWCS: string;
}
