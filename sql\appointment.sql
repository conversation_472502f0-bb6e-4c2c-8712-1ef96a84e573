-- 预约表
CREATE TABLE `appointment` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `id_card` VARCHAR(18) NOT NULL COMMENT '身份证号',
  `name` VARCHAR(50) NOT NULL COMMENT '姓名',
  `inst_id` BIGINT NOT NULL COMMENT '机构ID',
  `doctor_id` BIGINT COMMENT '医师ID（可为空表示不指定医师）',
  `appt_date` DATETIME NOT NULL COMMENT '预约日期时间',
  `disease_category` CHAR(7) COMMENT '职业病病人分类编码',
  `service_type` TEXT COMMENT '预约服务类型描述',
  `department` VARCHAR(50) COMMENT '预约科室',
  `requirement` TEXT COMMENT '预约需求',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '状态：0-待审核，1-通过，2-拒绝，3-已取消，4-已完成',
  `reject_reason` VARCHAR(500) COMMENT '拒绝原因',
  `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-否，1-是',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_id_card` (`id_card`),
  INDEX `idx_inst_id` (`inst_id`),
  INDEX `idx_doctor_id` (`doctor_id`),
  INDEX `idx_appt_date` (`appt_date`),
  INDEX `idx_status` (`status`),
  INDEX `idx_disease_category` (`disease_category`),
  INDEX `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='预约表'; 