import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

@Entity({
  name: 'DP_ZYWSDP_GDQZYWSFGTJ_LB',
  comment: '职业卫生大屏-各地区职业卫生覆盖统计-列表',
})
export class DP_ZYWSDP_GDQZYWSFGTJ_LB {
  @PrimaryGeneratedColumn()
  ID: number;

  @Column({ type: 'varchar2', length: 255, comment: '地区' })
  DQ: string;

  @Column({ type: 'varchar2', length: 255, comment: '年份' })
  NF: string;

  @Column({ type: 'varchar2', length: 255, comment: '开展数量' })
  KZSL: string;

  @Column({ type: 'varchar2', length: 255, comment: '完成监测任务数' })
  WCJCRWS: string;

  @Column({ type: 'varchar2', length: 255, comment: '完成监测任务占比（%）' })
  WCJCRWZB: string;
}
