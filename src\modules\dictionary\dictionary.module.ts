import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DictArea } from './mysql/dict_area.entity';
import { DictionaryService } from './dictionary.service';
import { DictionaryController } from './dictionary.controller';
import { ExpertClassifierService } from './expert_classifier.service'; // 专家分类
import { ExpertClassifier } from './mysql/expert_classifier.entity'; // 专家分类
// 字典表模块
@Module({
  imports: [
    TypeOrmModule.forFeature(
      [ExpertClassifier, DictArea],
      'mysqlConnectionIservice',
    ),
  ],
  controllers: [DictionaryController],
  providers: [DictionaryService, ExpertClassifierService],
  exports: [DictionaryService, ExpertClassifierService],
})
export class DictionaryModule {}
