import { Injectable, Logger } from '@nestjs/common';
import { NgUser } from './mysql/ng_user.entity';
import { NgEmployer } from './mysql/ng_employer.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OnEvent } from '@nestjs/event-emitter';
import { UNIFIED_ACCOUNT_CREATED } from '../unified-account/events/constants';
import { AccountCreatedEvent } from '../unified-account/events/account-created-event';
import { TjQykGssj } from './mysql/tj_qyk_gssj.entity';
import { QueryOrgInfoDto } from './dto/queryOrgInfoDto.dto';
import { UnifiedAccountService } from '../unified-account/unified-account.service';
import { END_POINT } from 'src/constants/endPoint';
import { AdminOrgsOrg } from '../unified-account/account-service/admin-orgs/admin-orgs-org.account.entity';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(NgUser, 'mysqlConnectionHebei')
    private readonly ngUserRepository: Repository<NgUser>,

    @InjectRepository(NgEmployer, 'mysqlConnectionHebei')
    private readonly ngEmployerRepository: Repository<NgEmployer>,

    @InjectRepository(TjQykGssj, 'mysqlConnectionHebeiTy')
    private readonly tjQykGssjRepository: Repository<TjQykGssj>,

    private readonly unifiedAccountService: UnifiedAccountService,
  ) {}

  private readonly logger = new Logger(UserService.name);

  // async getUserById(id: number) {
  //   return await this.ngUserRepository.findOne({
  //     where: { id },
  //   });
  // }

  // async createUser(data) {
  //   return await this.ngUserRepository.save(data);
  // }

  @OnEvent(UNIFIED_ACCOUNT_CREATED)
  async onUnifiedAccountCreated(event: AccountCreatedEvent) {
    const { user, org, endPoint } = event;
    if (endPoint === END_POINT.qy) {
      await this.processAdminOrg(org);
    }
  }

  async processAdminOrg(org: AdminOrgsOrg) {
    const orgInfoRecord = await this.QueryOrgInfoFromTjQykGssj({
      unifiedCode: org.code,
      phone: '',
    });
    if (!orgInfoRecord) {
      return;
    }
    await this.syncOrgInfoFromTJQykGssj(org._id, orgInfoRecord);
    await this.saveEmployer(orgInfoRecord);
  }

  async saveEmployer(orgInfoRecord: TjQykGssj) {
    const employer = await this.ngEmployerRepository.findOne({
      where: {
        unifiedCode: orgInfoRecord.unified_code,
      },
    });
    if (!employer) {
      await this.ngEmployerRepository.save({
        employerName: orgInfoRecord.name,
        unifiedCode: orgInfoRecord.unified_code,
        districtId: Number(orgInfoRecord.district_id),
        industryId: orgInfoRecord.hylbdm,
        registeredAddress: orgInfoRecord.address,
        legalPersonName: orgInfoRecord.fr,
      });
    }
  }

  async syncOrgInfoFromTJQykGssj(
    orgId: string,
    orgInfoRecord: TjQykGssj,
  ): Promise<void> {
    await this.unifiedAccountService.updateQyOrgAccount(orgId, {
      cname: orgInfoRecord.name,
      regAdd: orgInfoRecord.address,
    } as AdminOrgsOrg);
  }

  async QueryOrgInfoFromTjQykGssj(query: QueryOrgInfoDto): Promise<TjQykGssj> {
    this.logger.log('开始在 TjQykGssj 中查询用人单位信息', query);

    const record = await this.tjQykGssjRepository
      .createQueryBuilder('tjQykGssj')
      .where('tjQykGssj.unified_code = :unifiedCode', {
        unifiedCode: query.unifiedCode,
      })
      .getOne();
    this.logger.log(
      'TjQykGssj 中查询用人单位信息的查询结果',
      JSON.stringify(record),
    );
    return record;
  }
}
