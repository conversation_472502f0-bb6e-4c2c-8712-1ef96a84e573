import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
} from 'typeorm';

// 用人单位花名册
@Entity('ng_user')
export class NgUser {
  @PrimaryGeneratedColumn({ type: 'int', comment: 'roster_id' })
  id: number;
  @Column({ type: 'int', name: 'unit_id', comment: '所属单位id' })
  unit_id: number;

  @Column({ type: 'datetime', name: 'reg_date', comment: '注册时间' })
  regDate: Date;

  @Column({ type: 'varchar', name: 'display_name', comment: '显示名称' })
  displayName: string;

  @Column({
    type: 'tinyint',
    nullable: true,
    comment: '用户类型(1用人单位2监管单位3运维单位)',
  })
  userType: number | null;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '用户名(登录用)',
  })
  userName: string | null;

  @Column({ type: 'varchar', nullable: true, comment: '密码' })
  passsword: string | null;

  @Column({ type: 'int', nullable: true, comment: '角色id' })
  roleId: number | null;

  @Column({ type: 'tinyint', nullable: true, comment: '账户状态(0冻结1正常)' })
  accountStatus: number | null;

  @Column({ type: 'varchar', nullable: true, comment: '账户头像' })
  accountAvatar: string | null;

  @CreateDateColumn({
    type: 'datetime',
    nullable: true,
    comment: '最后访问时间',
  })
  lastAccessTime: Date | null;
}
