import { Controller, Post, Body, Logger } from '@nestjs/common';
import { WjService } from './wj.service';
import { success, error } from 'src/utils';
import { JwtService } from '../jwt/jwt.service';
import { ConfigService } from '@nestjs/config';

@Controller('wj')
export class WjController {
  constructor(
    private readonly wjService: WjService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}
  private readonly logger = new Logger(WjController.name);

  @Post()
  async checkAccount(@Body() body) {
    try {
      const account = await this.wjService.checkAccount(body);

      if (account && account.id) {
        const token = await this.jwtService.generateToken({ id: account.id });
        const domainNames = this.configService.get('domainNames');
        return success(
          { url: `${domainNames.wj}/home?token=${token}` },
          '用户信息校验成功',
        );
      } else {
        throw new Error('用户信息校验失败');
      }
    } catch (err) {
      this.logger.error('问卷用户信息校验失败: ' + err.message);
      return error(err.message);
    }
  }
}
