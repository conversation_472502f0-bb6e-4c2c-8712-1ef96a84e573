import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Certificate } from './mysql/certificate.entity';
import * as moment from 'moment';

@Injectable()
export class CertificateService {
  constructor(
    @InjectRepository(Certificate, 'mysqlConnectionIservice')
    private readonly CertificateRepository: Repository<Certificate>,
  ) {}

  // 获取资质证书列表
  async findAll(query: any): Promise<any[]> {
    const certificates = await this.CertificateRepository.find({
      where: query,
    });
    return certificates.map((certificate) => ({
      ...certificate,
      get_time: certificate.get_time
        ? moment(certificate.get_time).format('YYYY-MM-DD')
        : '',
      valid_time: certificate.valid_time
        ? moment(certificate.valid_time).format('YYYY-MM-DD')
        : '',
    }));
  }

  async findOne(id: number): Promise<Certificate> {
    const data: any = await this.CertificateRepository.findOne({
      where: { id },
    });
    if (data) {
      return {
        ...data,
        get_time: data.get_time
          ? moment(data.get_time).format('YYYY-MM-DD')
          : '',
        valid_time: data.valid_time
          ? moment(data.valid_time).format('YYYY-MM-DD')
          : '',
      };
    }
    return data;
  }

  // 创建资质证书
  async create(createDto: Certificate): Promise<Certificate> {
    const { number } = createDto;
    if (!number) {
      throw new Error('证书编号不能为空');
    }
    const isExist = await this.CertificateRepository.findOne({
      where: { number },
    });
    if (isExist) {
      throw new Error('证书编号已存在, 请勿重复添加');
    }
    createDto.user_id = +createDto.user_id;
    const Certificate = this.CertificateRepository.create(createDto);
    return await this.CertificateRepository.save(Certificate);
  }

  // // 更新资质证书
  // async update(id: number, updateDto: Certificate): Promise<any> {
  //   const res = await this.CertificateRepository.update(id, updateDto);
  //   if (res.affected === 0) {
  //     throw new Error('删除失败, 请检查id是否正确');
  //   }
  //   return await this.findOne(id);
  // }

  // 删除资质证书
  async remove(id: number): Promise<any> {
    const isExist = await this.CertificateRepository.findOne({
      where: { id },
    });
    if (!isExist) {
      throw new Error('删除失败, id不存在');
    }
    await this.CertificateRepository.delete(id);
    return { id, number: isExist.number, annex: isExist.annex };
  }
}
