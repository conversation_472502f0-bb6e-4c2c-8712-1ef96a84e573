import { Entity, Column, ObjectIdColumn, Index, BeforeInsert } from 'typeorm';
import * as shortid from 'shortid';

// 企业用人单位 信息
@Entity('adminorgs')
export class Adminorgs {
  @ObjectIdColumn()
  _id: string;

  @Column({ comment: '单位名称' })
  cname: string;

  @Column({ comment: '统一社会信用代码' })
  @Index()
  code: string;

  @Column({ comment: '唯一编码' })
  @Index()
  unitCode: string;

  @Column({ comment: '注册地址' })
  regAdd: string;

  @Column({ comment: '法人' })
  corp: string;

  @Column({ comment: '联系人' })
  contract: string;

  @Column({ comment: '联系方式' })
  phoneNum: string;

  @Column({ comment: '单位规模 (大、中、小、微)' })
  companyScale: string;

  @Column({ comment: '册地址 [ 省，市，区 ]', type: 'array', default: [] })
  districtRegAdd: string[];

  @Column({ comment: '管理员ID' })
  adminUserId: string;

  @Column({ type: 'array', default: [] })
  adminArray: string[];

  @Column({ comment: '简称' })
  shortName: string;

  // 0 注销， 1 暂时停产，2 正常生产
  @Column({ comment: '企业生产状态' })
  productionStatus: string;

  // 上级单位ID组
  @Column({ comment: '上级单位ID组', type: 'array', default: [] })
  parentId: string[];

  @Column({ comment: '下级单位ID组', type: 'array', default: [] })
  childrenId: string[];

  // 企业类型 集团 分公司 独立公司 enum: [ 'group', 'branch', 'subsidiary' ]
  @Column({ comment: '企业类型' })
  companyType: string;

  @Column({ default: () => new Date() })
  createTime: Date;

  @Column({ default: () => new Date() })
  updateTime: Date;

  @BeforeInsert()
  setDefaultId() {
    if (!this._id) {
      this._id = shortid.generate();
    }
  }

  constructor() {
    this.cname = this.cname || '';
    this.code = this.code || '';
    this.unitCode = this.unitCode || '';
    this.regAdd = this.regAdd || '';
    this.corp = this.corp || '';
    this.contract = this.contract || '';
    this.phoneNum = this.phoneNum || '';
    this.adminUserId = this.adminUserId || '';
    this.shortName = this.shortName || '';
    this.companyType = this.companyType || '';
    this.districtRegAdd = this.districtRegAdd || [];
    this.parentId = this.parentId || [];
    this.childrenId = this.childrenId || [];
    this.adminArray = this.adminArray || [];
    this.productionStatus = this.productionStatus || '2';
  }
}
