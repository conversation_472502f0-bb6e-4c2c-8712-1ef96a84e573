import { Controller, Post, Body, Get, Query, Logger } from '@nestjs/common';
import { DakService } from './dak.service';
import { CreateDiagnosticRecordDto } from './dto/create-diagnostic-record.dto';
import { CreateDeterminationRecordDto } from './dto/create-determination-record.dto';
import { DiagnosticRecord } from './entities/diagnosticRecord.entity';
import { DeterminationRecord } from './entities/determinationRecord.entity';
import { HealthSurvArchives } from './entities/HealthSurvArchives.entity';
import { HealthCheckRegister } from './entities/HealthCheckRegister.entity';
import { wrapperResponse } from 'src/utils';

@Controller('dak')
export class DakController {
  private readonly logger = new Logger(DakController.name);

  constructor(private readonly dakService: DakService) { }

  /**
   * 推送诊断记录
   * @param createDiagnosticRecordDto 诊断记录DTO
   * @returns 创建或更新的诊断记录
   */
  @Post('postDiagnosticRecord')
  async createDiagnosticRecord(
    @Body() createDiagnosticRecordDto: CreateDiagnosticRecordDto,
  ): Promise<DiagnosticRecord> {
    this.logger.log(`推送诊断记录: ${JSON.stringify(createDiagnosticRecordDto)}`);
    return wrapperResponse(
      this.dakService.createOrUpdateDiagnosticRecord(createDiagnosticRecordDto),
      '推送诊断记录',
    );
  }

  /**
   * 推送鉴定记录
   * @param createDeterminationRecordDto 鉴定记录DTO
   * @returns 创建或更新的鉴定记录
   */
  @Post('postDeterminationRecord')
  async createDeterminationRecord(
    @Body() createDeterminationRecordDto: CreateDeterminationRecordDto,
  ): Promise<DeterminationRecord> {
    // 兼容 entity 字段的默认值
    if (!createDeterminationRecordDto.occupationalDisease) {
      createDeterminationRecordDto.occupationalDisease = [];
    }
    if (!createDeterminationRecordDto.fileList) {
      createDeterminationRecordDto.fileList = [];
    }
    if (!createDeterminationRecordDto.createdAt) {
      createDeterminationRecordDto.createdAt = new Date();
    }
    if (!createDeterminationRecordDto.updatedAt) {
      createDeterminationRecordDto.updatedAt = new Date();
    }
    this.logger.log(`推送鉴定记录: ${JSON.stringify(createDeterminationRecordDto)}`);
    return wrapperResponse(
      this.dakService.createOrUpdateDeterminationRecord(createDeterminationRecordDto),
      '推送鉴定记录',
    );
  }

  /**
   * 根据身份证号查询诊断记录
   * @param idNumber 身份证号
   * @returns 诊断记录列表
   */
  @Get('getDiagnosticRecords')
  async findDiagnosticRecords(
    @Query('idNumber') idNumber: string,
  ): Promise<DiagnosticRecord[]> {
    this.logger.log(`查询诊断记录: ${idNumber}`);
    return wrapperResponse(
      this.dakService.findDiagnosticRecordsByIdNumber(idNumber),
      '查询诊断记录',
    );
  }

  /**
   * 根据身份证号查询鉴定记录
   * @param idNumber 身份证号
   * @returns 鉴定记录列表
   */
  @Get('getDeterminationRecords')
  async findDeterminationRecords(
    @Query('idNumber') idNumber: string,
  ): Promise<DeterminationRecord[]> {
    this.logger.log(`查询鉴定记录: ${idNumber}`);
    return wrapperResponse(
      this.dakService.findDeterminationRecordsByIdNumber(idNumber),
      '查询鉴定记录',
    );
  }

  /**
   * 根据体检编号修改诊断申请状态
   * @param checkNo 体检编号
   * @param status 诊断申请状态
   * @returns 诊断记录
   */
  @Post('updateDiagnosisStatus')
  async updateDiagnosisStatus(
    @Body('checkNo') checkNo: string,
    @Body('status') status: string,
  ): Promise<string> {
    this.logger.log(`更新体检记录: ${checkNo}, 诊断状态: ${status}`);
    return wrapperResponse(
      this.dakService.updateDiagnosisStatus(checkNo, status),
      '更新诊断申请状态',
    );
  }

  // 根据身份证抽取劳动者职业健康监护档案
  @Get('extractHealthArchive')
  async extractHealthArchive(
    @Query('idNumber') idNumber: string,
  ): Promise<HealthSurvArchives> {
    this.logger.log(`抽取劳动者职业健康监护档案: ${idNumber}`);
    return wrapperResponse(
      this.dakService.extractHealthArchive(idNumber),
      '抽取劳动者职业健康监护档案',
    );
  }
}
