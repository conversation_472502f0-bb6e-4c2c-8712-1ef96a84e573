import {
  Entity,
  Column,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
// 组织机构
@Entity('t_dept')
export class TDept {
  @PrimaryColumn({ type: 'varchar', length: 64, comment: 'ID' })
  id: string;

  @Column({ type: 'varchar', length: 64, comment: '父ID' })
  parent_id: string;

  @Column({ type: 'varchar', length: 64, nullable: true, comment: '名称' })
  name: string;

  @Column({ type: 'varchar', length: 64, comment: '简称' })
  short_name: string;

  @Column({
    type: 'varchar',
    length: 64,
    nullable: true,
    comment: '数据权限类型',
  })
  code: string;

  @Column({ type: 'varchar', length: 64, nullable: true, comment: '扩展字段' })
  manager_id: string;

  @Column({ type: 'int', nullable: true, comment: '排序码' })
  sort_code: number;

  @Column({ type: 'varchar', length: 256, nullable: true, comment: '扩展字段' })
  property_json: string;

  @Column({ type: 'varchar', length: 20, nullable: true, comment: '扩展字段' })
  status: string;

  @Column({ type: 'varchar', length: 256, nullable: true, comment: '扩展字段' })
  remark: string;

  @Column({ type: 'tinyint', default: 0, comment: '是否删除' })
  is_deleted: number;

  @CreateDateColumn({ type: 'timestamp', comment: '创建时间' })
  create_at: Date;

  @Column({ type: 'varchar', length: 256, nullable: true })
  create_by: string;

  @UpdateDateColumn({ type: 'timestamp', comment: '更新时间' })
  update_at: Date;

  @Column({ type: 'varchar', length: 256, nullable: true })
  update_by: string;
}
