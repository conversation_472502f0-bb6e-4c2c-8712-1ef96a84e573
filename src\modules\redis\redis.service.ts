import Redis from 'ioredis';
import {
  Injectable,
  OnModuleInit,
  OnModuleDestroy,
  Logger,
} from '@nestjs/common';
import { GlobalModule } from '../../global.module';
import { SystemConfigsService } from '../system-configs/system-configs.service';
import { SystemConfigs } from '../system-configs/system-configs.entity';

@Injectable()
export class RedisService implements OnModuleInit, OnModuleDestroy {
  constructor(private readonly systemConfigsService: SystemConfigsService) {}
  private client: Redis;
  private systemConfigKeys = ['warningConfig']; // 系统配置信息的键
  private readonly logger = new Logger(RedisService.name);

  onModuleInit() {
    this.initClient();
  }
  initClient(): void {
    try {
      this.client = new Redis({
        ...GlobalModule.config['database_redis'],
      });

      this.client.on('connect', () => {
        this.logger.log('Connected to <PERSON>is!');
      });

      this.client.on('ready', () => {
        this.logger.log('Redis is ready!');
      });

      this.client.on('error', (err) => {
        this.logger.error(`Redis Connect Error: ${err.message}`);
      });

      this.client.on('end', () => {
        this.logger.log('Redis connection closed');
      });
    } catch (error) {
      this.logger.error(`Redis Connect Error: ${error.message}`);
    }
  }
  onModuleDestroy() {
    this.client.quit();
  }

  getClient(): Redis {
    return this.client;
  }

  // 设置cache
  async set(key: string, value: any, ttl?: number): Promise<void> {
    try {
      if (this.systemConfigKeys.includes(key)) {
        return await this.setSystemConfig(key, value);
      }
      return await this.setRedis(key, value, ttl);
    } catch (error) {
      this.logger.error(`Failed to set key ${key} in cache:`, error);
      throw error;
    }
  }

  // 设置redis缓存
  async setRedis(key: string, value: any, ttl?: number): Promise<void> {
    try {
      let result: any;
      if (ttl) {
        result = await this.client.set(key, JSON.stringify(value), 'EX', ttl);
      } else {
        result = await this.client.set(key, JSON.stringify(value)); // 永不过期
      }
      this.logger.log(
        `Set key ${key} in Redis, value: ${value}, result: ${result}`,
      );
      return result;
    } catch (error) {
      this.logger.error(`Failed to set key ${key} in Redis:`, error);
      throw error;
    }
  }

  // 删除redis缓存
  async del(key: string): Promise<void> {
    await this.client.del(key);
  }

  // 获取redis缓存
  async get(key: string): Promise<any> {
    const data = await this.client.get(key);
    if (data) return JSON.parse(data);
    if (this.systemConfigKeys.includes(key)) {
      return this.getSystemConfig(key);
    }
  }

  // 设置系统配置信息
  async setSystemConfig(key: string, value: any): Promise<void> {
    const result = await this.systemConfigsService.update({
      [key]: value,
    });
    if (result.ok === 0) {
      throw new Error('Failed to update systemConfigs');
    }
    this.logger.log(`Set key ${key} in systemConfigs`);
    this.del(key);
    return result;
  }

  // 获取systemconfigs表中的信息, 如果数据库中没有则使用默认配置
  async getSystemConfig(key: string): Promise<any> {
    const systemConfigs = await this.systemConfigsService.findOne([
      key as keyof SystemConfigs,
    ]);
    const value = systemConfigs[key] || GlobalModule.config[key] || null;
    if (value) this.setRedis(key, value);
    return value;
  }

  // 获取redis缓存所有键值对
  async getAllKeysAndValues() {
    let cursor = '0';
    const result = {};

    try {
      do {
        // 使用 SCAN 命令迭代键，这里使用默认的 COUNT 值
        const reply = await this.client.scan(cursor, 'MATCH', '*');
        cursor = reply[0];
        const keys = reply[1];

        // 对于每个键，使用 GET 命令获取其值
        for (const key of keys) {
          const value = await this.client.get(key);
          try {
            result[key] = JSON.parse(value);
          } catch (e) {
            result[key] = value;
          }
        }
      } while (cursor !== '0');
    } catch (error) {
      throw error;
    }

    return result;
  }
}
