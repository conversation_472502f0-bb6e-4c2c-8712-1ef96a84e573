import { Entity, ObjectIdColumn, Column } from 'typeorm';
import { ObjectId } from 'mongodb';
import shortid from 'shortid';

class FormData {
  @ObjectIdColumn({ default: () => shortid.generate() })
  _id: ObjectId;

  @Column()
  projectNumbers: Array<string>;

  @Column()
  harmFactorsNames: string;

  @Column()
  comments: string;

  @Column()
  count: string;

  @Column()
  status: boolean;
}

class SamplingLeader {
  @ObjectIdColumn({ default: () => shortid.generate() })
  _id: ObjectId;

  @Column()
  name: string;

  @Column()
  phoneNum: string;
}

@Entity('samplePreparation')
export class SamplePreparation {
  @ObjectIdColumn()
  _id: ObjectId;

  @Column()
  projectSN: Array<string>;

  @Column({ type: 'date' })
  date: Date;

  @Column(() => SamplingLeader)
  samplingLeader: SamplingLeader;

  @Column(() => FormData)
  FormData: FormData[];

  @Column()
  status: number;
}
