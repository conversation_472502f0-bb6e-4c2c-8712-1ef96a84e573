import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

@Entity({
  name: 'DP_ZYWSDP_YRDWZYWHYSSBTJ',
  comment: '用人单位职业危害因素申报统计',
})
export class DP_ZYWSDP_YRDWZYWHYSSBTJ {
  @PrimaryGeneratedColumn()
  ID: number;

  @Column({ type: 'varchar2', length: 255, comment: '地区' })
  DQ: string;

  @Column({ type: 'varchar2', length: 255, comment: '年度' })
  ND: string;

  @Column({ type: 'varchar2', length: 255, comment: '用人单位数' })
  YRDWS: string;

  @Column({ type: 'varchar2', length: 255, comment: '已申报的用人单位数' })
  YSBDYRDWS: string;

  @Column({ type: 'varchar2', length: 255, comment: '申报率' })
  SBL: string;
}
