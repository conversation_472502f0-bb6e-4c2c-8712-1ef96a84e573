import { Entity, Column, ObjectIdColumn, BeforeInsert } from 'typeorm';
import * as shortid from 'shortid';

class Allergy {
  @Column({ type: 'varchar', default: () => shortid.generate() })
  _id: string;

  @Column({ type: 'varchar', comment: '过敏原' })
  allergySource: string;

  @Column({ type: 'varchar', comment: '过敏症状' })
  allergySymptoms: string;

  @Column({ type: 'varchar', comment: '过敏日期' })
  allergyDate: string;

  @Column({ type: 'varchar', comment: '处理措施' })
  treatment: string;

  @Column({ type: 'varchar', comment: '效果记录' })
  effectRecord: string;
}

class PastHistory {
  @Column({ type: 'varchar', default: () => shortid.generate() })
  _id: string;

  @Column({ type: 'varchar', comment: '疾病名称' })
  diseaseName: string;

  @Column({ type: 'varchar', comment: '诊断日期' })
  diagnosisDate: string;

  @Column({ type: 'varchar', comment: '机构名称' })
  institutionName: string;

  @Column({ type: 'varchar', comment: '治疗经过' })
  treatmentProcess: string;

  @Column({ type: 'varchar', comment: '转归编码' })
  outcomeCode: string;
}

class FamilyHistory {
  @Column({ type: 'varchar', default: () => shortid.generate() })
  _id: string;

  @Column({ type: 'varchar', comment: '家族成员' })
  familyMember: string;

  @Column({ type: 'varchar', comment: '疾病名称' })
  diseaseName: string;

  @Column({ type: 'varchar', comment: '诊断日期' })
  diagnosisDate: string;

  @Column({ type: 'varchar', comment: '机构名称' })
  institutionName: string;

  @Column({ type: 'varchar', comment: '治疗经过' })
  treatmentProcess: string;
}

class EmergencyContact {
  @Column({ type: 'varchar', default: () => shortid.generate() })
  _id: string;

  @Column({ type: 'varchar', comment: '姓名' })
  name: string;

  @Column({ type: 'varchar', comment: '关系' })
  relationship: string;

  @Column({ type: 'varchar', comment: '手机号' })
  phoneNum: string;
}

/**
 * 员工个人基本信息实体
 */
@Entity('employeeBasicInfos')
export class EmployeeBasicInfo {
  @ObjectIdColumn()
  _id: string;

  @Column({ type: 'varchar', comment: '用户id' })
  userId: string;

  @Column({ type: 'varchar', comment: '员工id' })
  employeeId: string;

  @Column({ type: 'varchar', comment: '企业id' })
  EnterpriseID: string;

  @Column({ type: 'varchar', comment: '姓名' })
  name: string;

  @Column({ type: 'varchar', comment: '性别' })
  gender: string;

  @Column({ type: 'varchar', comment: '身份证' })
  IDNum: string;

  @Column({ type: 'varchar', comment: '手机号' })
  phoneNum: string;

  @Column({ type: 'varchar', comment: '联系电话', nullable: true })
  contactPhoneNum: string;

  @Column({ type: 'varchar', comment: '户籍所在地', nullable: true })
  nativePlace: string;

  @Column({ type: 'varchar', comment: '户籍地址', nullable: true })
  nativePlaceAddress: string;

  @Column({ type: 'varchar', comment: '常住所在地', nullable: true })
  residencePlace: string;

  @Column({ type: 'varchar', comment: '常住地址', nullable: true })
  residenceAddress: string;

  @Column({ type: 'varchar', comment: '民族', nullable: true })
  nation: string;

  @Column({ type: 'varchar', comment: '婚姻状况', nullable: true })
  maritalStatus: string;

  @Column({ type: 'varchar', comment: '文化程度', nullable: true })
  education: string;

  @Column({ type: 'varchar', comment: '血型', nullable: true })
  bloodType: string;

  @Column(_type => Allergy)
  allergy: Allergy[] = [];

  @Column(_type => PastHistory)
  pastHistory: PastHistory[] = [];

  @Column(_type => FamilyHistory)
  familyHistory: FamilyHistory[] = [];

  // 健康行为相关字段
  @Column({ type: 'varchar', comment: '吸烟史-吸烟情况', nullable: true })
  smokingHistory: string;

  @Column({ type: 'varchar', comment: '吸烟史-吸烟数（支/天）', nullable: true })
  smokingAmount: string;

  @Column({ type: 'varchar', comment: '吸烟史-年', nullable: true })
  smokingYear: string;

  @Column({ type: 'varchar', comment: '吸烟史-月', nullable: true })
  smokingMonth: string;

  @Column({ type: 'varchar', comment: '饮酒史-饮酒情况', nullable: true })
  drinkingHistory: string;

  @Column({ type: 'varchar', comment: '饮酒史-饮酒量ml/日', nullable: true })
  drinkingAmount: string;

  @Column({ type: 'varchar', comment: '饮酒史-饮酒多少年', nullable: true })
  drinkingYear: string;

  // 生育史相关字段
  @Column({ type: 'varchar', comment: '现有子女数', nullable: true })
  currentChildren: string;

  @Column({ type: 'varchar', comment: '流产数', nullable: true })
  abortion: string;

  @Column({ type: 'varchar', comment: '死产数', nullable: true })
  stillbirth: string;

  @Column({ type: 'varchar', comment: '早产数', nullable: true })
  premature: string;

  @Column({ type: 'varchar', comment: '异常胎数', nullable: true })
  abnormalFetus: string;

  @Column({ type: 'varchar', comment: '子女健康状况', nullable: true })
  childrenHealth: string;

  // 月经史相关字段
  @Column({ type: 'varchar', comment: '初潮年龄', nullable: true })
  menarcheAge: string;

  @Column({ type: 'varchar', comment: '经期（天）', nullable: true })
  menstruationDays: string;

  @Column({ type: 'varchar', comment: '周期（天）', nullable: true })
  menstruationCycle: string;

  @Column({ type: 'varchar', comment: '绝经年龄', nullable: true })
  menopauseAge: string;

  @Column({ type: 'varchar', comment: '运动习惯', nullable: true })
  exerciseHabit: string;

  @Column(_type => EmergencyContact)
  emergencyContact: EmergencyContact[] = [];

  @Column({ type: 'date', default: () => new Date(), comment: '创建时间' })
  createdAt: Date;

  @Column({ type: 'date', default: () => new Date(), comment: '更新时间' })
  updatedAt: Date;

  @BeforeInsert()
  setDefaultId() {
    if (!this._id) {
      this._id = shortid.generate();
    }
  }
} 