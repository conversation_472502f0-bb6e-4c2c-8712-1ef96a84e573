import { Entity, Column, ObjectIdColumn, CreateDateColumn, UpdateDateColumn, BeforeInsert } from 'typeorm';
import * as shortid from 'shortid';

// 过敏史接口
interface Allergy {
  _id: string;
  allergySource: string;
  allergySymptoms: string;
  allergyDate: string;
  treatment: string;
  effectRecord: string;
}

// 既往史接口
interface PastHistory {
  _id: string;
  diseaseName: string;
  diagnosisDate: string;
  institutionName: string;
  treatmentProcess: string;
  outcomeCode: string;
}

// 家族史接口
interface FamilyHistory {
  _id: string;
  familyMember: string;
  diseaseName: string;
  diagnosisDate: string;
  institutionName: string;
  treatmentProcess: string;
}

// 紧急联系人接口
interface EmergencyContact {
  _id: string;
  name: string;
  relationship: string;
  phoneNum: string;
}

/**
 * 员工个人基本信息实体
 */
@Entity('employeeBasicInfos')
export class EmployeeBasicInfo {
  @ObjectIdColumn()
  _id: string;

  @Column({ comment: '用户id' })
  userId: string;

  @Column({ comment: '员工id' })
  employeeId: string;

  @Column({ comment: '企业id' })
  EnterpriseID: string;

  @Column({ comment: '姓名' })
  name: string;

  @Column({ comment: '性别' })
  gender: string;

  @Column({ comment: '身份证号' })
  IDNum: string;

  @Column({ comment: '手机号' })
  phoneNum: string;

  @Column({ comment: '联系电话', nullable: true })
  contactPhoneNum?: string;

  @Column({ comment: '户籍所在地', nullable: true })
  nativePlace?: string;

  @Column({ comment: '户籍地址', nullable: true })
  nativePlaceAddress?: string;

  @Column({ comment: '常住所在地', nullable: true })
  residencePlace?: string;

  @Column({ comment: '常住地址', nullable: true })
  residenceAddress?: string;

  @Column({ comment: '民族', nullable: true })
  nation?: string;

  @Column({ comment: '婚姻状况', nullable: true })
  maritalStatus?: string;

  @Column({ comment: '文化程度', nullable: true })
  education?: string;

  @Column({ comment: '血型', nullable: true })
  bloodType?: string;

  @Column({ comment: '过敏史' })
  allergy: Allergy[] = [];

  @Column({ comment: '既往史' })
  pastHistory: PastHistory[] = [];

  @Column({ comment: '家族史' })
  familyHistory: FamilyHistory[] = [];

  // 健康行为相关字段
  @Column({ comment: '吸烟史-吸烟情况', nullable: true })
  smokingHistory?: string;

  @Column({ comment: '吸烟史-吸烟数（支/天）', nullable: true })
  smokingAmount?: number;

  @Column({ comment: '吸烟史-年', nullable: true })
  smokingYear?: number;

  @Column({ comment: '吸烟史-月', nullable: true })
  smokingMonth?: number;

  @Column({ comment: '饮酒史-饮酒情况', nullable: true })
  drinkingHistory?: string;

  @Column({ comment: '饮酒史-饮酒量ml/日', nullable: true })
  drinkingAmount?: number;

  @Column({ comment: '饮酒史-饮酒多少年', nullable: true })
  drinkingYear?: number;

  // 生育史相关字段
  @Column({ comment: '现有子女数', nullable: true })
  currentChildren?: number;

  @Column({ comment: '流产数', nullable: true })
  abortion?: number;

  @Column({ comment: '死产数', nullable: true })
  stillbirth?: number;

  @Column({ comment: '早产数', nullable: true })
  premature?: number;

  @Column({ comment: '异常胎数', nullable: true })
  abnormalFetus?: number;

  @Column({ comment: '子女健康状况', nullable: true })
  childrenHealth?: string;

  // 月经史相关字段
  @Column({ comment: '初潮年龄', nullable: true })
  menarcheAge?: number;

  @Column({ comment: '经期（天）', nullable: true })
  menstruationDays?: number;

  @Column({ comment: '周期（天）', nullable: true })
  menstruationCycle?: number;

  @Column({ comment: '绝经年龄', nullable: true })
  menopauseAge?: number;

  @Column({ comment: '运动习惯', nullable: true })
  exerciseHabit?: string;

  @Column({ comment: '紧急联系人' })
  emergencyContact: EmergencyContact[] = [];

  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date;

  @BeforeInsert()
  setDefaults() {
    if (!this._id) {
      this._id = shortid.generate();
    } const now = new Date();
    this.createdAt = now;
    this.updatedAt = now;
  }
}