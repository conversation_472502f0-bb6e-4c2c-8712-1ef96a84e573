const mysqlDataSourceConfig = {
  type: 'mysql',
  host: process.env.mysqlHost,
  port: +process.env.mysqlPort,
  username: process.env.mysqlUser,
  password: process.env.mysqlPass,
  logging: 'info',
  synchronize: false,
  // autoLoadEntities: true,
};

export default () => ({
  database_mongodb: {
    name: 'mongodbConnection',
    type: 'mongodb',
    hostReplicaSet: process.env.mdbHostRs,
    port: +process.env.mdbPort || 27017,
    replicaSet: process.env.mdbRs,
    readPreference: 'nearest',
    ssl: false,
    useNewUrlParser: true,
    useUnifiedTopology: true,
    authSource: 'admin',
    synchronize: false,
    username: process.env.mdbUser,
    password: process.env.mdbPass,
    database: process.env.mdbName,
    logging: 'all',
    autoLoadEntities: false,
  },

  database_mysql_iservice: {
    ...mysqlDataSourceConfig,
    name: 'mysqlConnectionIservice',
    database: process.env.mysqlDb,
  },
  database_mysql_xjbt: {
    name: 'mysqlConnectionXjbt',
    ...mysqlDataSourceConfig,
    database: process.env.mysqlDbpn,
  },
  database_mysql_xjbt_jkqy: {
    name: 'mysqlConnectionXjbtJkqy',
    ...mysqlDataSourceConfig,
    database: process.env.mysqlDbjkqy,
  },
  mysql_data_sources_hb: {
    mysqlConnectionHebei: {
      ...mysqlDataSourceConfig,
      name: 'mysqlConnectionHebei',
      database: process.env.hbMysqlDb,
    },
    mysqlConnectionHebeiTy: {
      ...mysqlDataSourceConfig,
      name: 'mysqlConnectionHebeiTy',
      database: process.env.hbMysqlDbTy,
    },
    mysqlConnectionHebeiPr: {
      ...mysqlDataSourceConfig,
      name: 'mysqlConnectionHebeiPr',
      database: process.env.hbMysqlDbPr,
    },
    mysqlConnectionHebeiWx: {
      ...mysqlDataSourceConfig,
      name: 'mysqlConnectionHebeiWx',
      database: process.env.hbMysqlDbWx,
    },
  },
});
