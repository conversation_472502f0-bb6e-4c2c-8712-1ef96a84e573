import {
  Controller,
  Get,
  Post,
  // Put,
  Delete,
  Param,
  Body,
  Query,
} from '@nestjs/common';
import { CertificateService } from './certificate.service';
import { Certificate } from './mysql/certificate.entity';
import { wrapperResponse } from 'src/utils';

@Controller('certificate')
export class CertificateController {
  constructor(private readonly certificateService: CertificateService) {}

  @Get()
  findAll(@Query() query: any): Promise<Certificate[]> {
    return wrapperResponse(
      this.certificateService.findAll(query),
      '获取资质证书列表',
    );
  }

  @Get(':id')
  findOne(@Param('id') id: number) {
    return wrapperResponse(
      this.certificateService.findOne(id),
      '获取资质证书详情',
    );
  }

  @Post()
  create(@Body() createDto: Certificate) {
    return wrapperResponse(
      this.certificateService.create(createDto),
      '创建资质证书',
    );
  }

  // @Put(':id')
  // update(@Param('id') id: number, @Body() updateDto: Certificate) {
  //   return wrapperResponse(
  //     this.certificateService.update(id, updateDto),
  //     '更新资质证书',
  //   );
  // }

  @Delete(':id')
  remove(@Param('id') id: number) {
    return wrapperResponse(this.certificateService.remove(id), '删除资质证书');
  }
}
