import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
// 专家审核
@Entity('expert_review')
export class ExpertReview {
  @PrimaryGeneratedColumn({ comment: '自增ID' })
  id: number;

  @Column({ comment: '专家ID', type: 'int' })
  expert_id: number;

  @Column({
    comment: '审核类型 1、专家申报；2、专家推荐；3、专家解聘',
    type: 'enum',
    enum: [1, 2, 3],
    default: 1,
  })
  review_type: number;

  @Column({
    comment: '专家级别 1、兵团级；2、师市级；3、团镇级',
    type: 'enum',
    enum: [1, 2, 3],
  })
  level: number;

  @Column({ comment: '专家类别id', type: 'int', nullable: true })
  category_id?: number;

  @Column({
    comment: '审核状态 1: 审核通过, 2: 待审核 3: 审核未通过 4、已撤销',
    type: 'enum',
    enum: [1, 2, 3, 4],
    nullable: true,
    default: 2,
  })
  review_status: number;

  @Column({ comment: '审核意见', type: 'varchar', length: 255, nullable: true })
  review_comment?: string;

  @Column({
    comment: '审核人',
    type: 'varchar',
    length: 64,
    nullable: true,
  })
  reviewer?: string;

  @Column({
    comment: '申请人',
    type: 'varchar',
    length: 64,
    nullable: true,
  })
  applicant?: string;

  @Column({ comment: '审核时间', type: 'datetime', nullable: true })
  review_time: Date;

  @CreateDateColumn({ comment: '创建时间', type: 'datetime' })
  created_at: Date;

  @UpdateDateColumn({ comment: '更新时间', type: 'datetime' })
  updated_at: Date;
}
