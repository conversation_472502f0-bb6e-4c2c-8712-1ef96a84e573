import { Module } from '@nestjs/common';
import { AdminorgsController } from './adminorgs.controller';
import { AdminorgsService } from './adminorgs.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Adminorgs } from './adminorgs.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Adminorgs], 'mongodbConnection')],
  controllers: [AdminorgsController],
  providers: [AdminorgsService],
  exports: [AdminorgsService],
})
export class AdminorgsModule {}
