import { Controller, Get, Query } from '@nestjs/common';
import { FzDpService } from './fz-dp.service';
import { wrapperResponse } from 'src/utils';

@Controller('fz-dp')
export class FzDpController {
  constructor(private readonly fzDpService: FzDpService) {}

  @Get() // http://127.0.0.1:3000/fz-dp?table=DP_ZYWSDP_GDQZYWSFGTJ_LB
  async find(@Query() query: { table: string }) {
    return wrapperResponse(
      this.fzDpService.find(query),
      '在oracle中获取table全部数据',
    );
  }
}
