# iservice2 版本 更新日志

## 0.0.278 更新日志
- feat
  - 健康企业申报
## 0.0.277 更新日志
- feat
  - 诊断记录推送新增 诊断证明书、附件列表，修复职业病为空
  - 新增 修改体检记录的诊断申请状态接口
## 0.0.272 更新日志
- Feat
  - 健康企业评估体系
## 0.0.266 更新日志
- Fix
  - 专家申请审核之后专家类别未更新
## 0.0.265 更新日志
- Fix
  - 专家申请审核之后专家级别未更新

## 0.0.260 更新日志
- 功能调整
  - 推送诊断鉴定接口

## 0.0.255、258 更新日志
- 功能新增
  - 档案库接口：上报诊断/鉴定记录 ; 上报后更新user的hasOccupationalDisease 是否患有职业病
  - 档案库接口：根据身份证号查询诊断/鉴定记录
## 0.0.254 更新日志
- Fix
  - 分配专家接口
## 0.0.246 更新日志
- 功能新增
  - 专家抽取接口调整
## 0.0.246 更新日志
- 功能新增
  - 专家工作安排查询优化
  - 新疆兵团测试环境数据库优化
## 0.0.243 更新日志
- 功能新增
  - 专家bug解决
## 0.0.241 更新日志
- 功能新增
  - 分配示范单位申报审核
## 0.0.239 更新日志
- 功能调整
  - 申报查询增加单位名称字段
## 0.0.237 更新日志
- Fix
  - 申报统计/地区限制
## 0.0.233 更新日志
- Fix
  - 修复专家端单点登录错误

## 0.0.233 更新日志
- Fix
  - 修复专家端单点登录错误

## 0.0.227 更新日志
- Fix
  - 更新专家工作任务接口的逻辑缺陷

## 0.0.226 更新日志
- 功能新增
  - 添加专家抽取记录相关接口
  - 添加专家任务安排相关接口
  - 获取专家详情时新增返回area_name字段

## 0.0.222 更新日志
- 功能新增
  - 统一门户单点登录专家子系统

## 0.0.202 更新日志
- 功能新增
  - 新增专家随机抽取接口

## 0.0.202 更新日志
- 功能新增
  - 新增专家相关接口

## 0.0.198 更新日志
- 功能新增
  - 专家推荐和解聘

## 0.0.191 更新日志
- 功能新增
  - 专家情况查询

## 0.0.191 更新日志
- 功能新增
  - 完善专家及证书模块

## 0.0.175 更新日志
- 功能新增
  - 资质证书模块
  - 新疆字典 - 专家分类模块

## 0.0.93 更新日志
- 配置修改
  - 支持多平台镜像打包

## 0.0.58 更新日志
- 功能新增
  - docs:修改ci文件

## 0.0.57 更新日志
- 功能新增
  - docs:修改ci文件

## 0.0.56 更新日志
- 功能新增
  - feat: 焦煤处理年龄和工龄计算

## 0.0.53 更新日志
- 修复缺陷
  - fix: 修改没有配置文件时获取配置文件不对的问题

## 0.0.53 更新日志
- 修复缺陷
  - fix: 杭州配置文件中的数据库连接配置

## 0.0.52 更新日志
- 修复缺陷
  - fix: 修复由于闭包导致无法更新对象,一直引用初始对象的问题

## 0.0.50 更新日志
- 修复缺陷
  - fix: 修复采样体积因分钟存在小数引起的计算误差

## 0.0.49 更新日志
- 修复缺陷
  - fix: 更正分段采样计算方式

## 0.0.48 更新日志
- 新增功能
  - feat: 新增问卷系统和jwt两个模块
  - 新增mysql数据库连接配置

## 0.0.47 更新日志
- 修复缺陷
  - fix: 当检测现场的温度不满足5-35℃或大气压不在98.9-103.4kPa时需要进行换算

## 0.0.46 更新日志
- 修复缺陷
  - feat: 更改分批推送问题

## 0.0.45 更新日志
- 修复缺陷
  - fix: 修复实验室收样数错误

## 0.0.44 更新日志
- 修复缺陷
  - fix: 修改项目创建年份获取

## 0.0.43 更新日志
- 修复缺陷
  - fix: 修改oracle查询方法

## 0.0.42 更新日志
- 修复缺陷
  - fix: 福州同步仪器信息更改目标MONGO存储表

## 0.0.41 更新日志
- 修复缺陷
  - fix: 对智源数据库原始记录单脏数据过滤

## 0.0.40 更新日志
- 新增功能
  - feat:福州新增同步仪器信息

## 0.0.39 更新日志
- 修复缺陷
  - 修复moment引入方式
  - 修改redis初始化方式

## 0.0.38 更新日志
- 修改表实体
  - 福州实验室数据对接：修改个体采样的分段结构及其对应采样参数同步
  - 福州实验室数据对接：修改保存十六进制字符串为PDF的方法
  
## 0.0.37 更新日志
- 新增功能
  - feat:焦煤基础数据对接定时任务
  
## 0.0.36 更新日志
- 新增功能
  - 注入redis数据库连接配置
  - 新增redis缓存读写模块
  - 新增hz.config配置文件
  - 新增system-configs子模块

## 0.0.35 更新日志
- 新增功能
  - 实验室数据对接：收样状态确认

## 0.0.34 更新日志
- 新增功能
  - 实验室数据对接：同步备注及自编号

## 0.0.33 更新日志
- 修改配置
  - 新增数据表模型导入

## 0.0.32 更新日志
- 修改查询语句
  - 修改游离二氧化硅浓度同步

## 0.0.31 更新日志
- 修改配置
  - 修改为定时器触发

## 0.0.30 更新日志
- 修改配置
  - 福州日志等级修改

## 0.0.29 更新日志
- 修改配置
  - 修改福州实验室数据库同步逻辑

## 0.0.28 更新日志
- 修改配置
  - 修改福州实验室表连接配置

## 0.0.27 更新日志
- 修改表实体
  - 修改福州实验室表实体定义

## 0.0.26 更新日志
- 修改查询语句
  - nest-typeorm 查询条件修改

## 0.0.25 更新日志
- 修改查询语句
  - 表实体关联关系更新

## 0.0.24 更新日志
- 修改查询语句
  - 服务日志记录更新

## 0.0.23 更新日志
- 修改查询语句
  - 修改Oracle表名及相关字段大小写

## 0.0.22 更新日志
- 修改查询语句
  - 修改Oracle订阅时的查询语句，去掉表名中的引号

## 0.0.21 更新日志
- BUG修复
  - 修复: 实验室数据对接：接受实验室分析结果，同步更新全流程项目状态

## 0.0.20 更新日志
- 新增功能
  - 实验室数据对接：样品信息推送至实验室收样
  - 实验室数据对接：接受实验室分析结果 - 接受样品浓度值/检测结果报告单推送

## 0.0.19 更新日志
- BUG修复
  - 修复: 福州大屏定时任务导致连接池超过最大限制问题

## 0.0.18 更新日志
- 新增调整
  - 体检系统对接：同步sqlserver表appointment中的文件时，传送一个完整的文件路径地址，而不是单单一个文件名

## 0.0.17 更新日志
- 新增调整
  - 体检系统对接：sqlserver表appointment新增三个字段

## 0.0.16 更新日志
- 功能调整
  - 为兼容SQL Server 2008，采用原生SQL语句进行数据查询
  - 同步预约单时添加报错日志的输出

## 0.0.15 更新日志
- 配置调整
  - 修复wkzwy产线上的虹丰医院的数据库连接失败的问题
  - 修改dev环境sqlserver数据库的database

## 0.0.14 更新日志
- 配置更新
  - 新增sit环境配置以及wkzyw产线上的虹丰医院的数据库配置

## 0.0.13 更新日志
- 新增功能
  - 添加同步mongodb中的预约单信息到sqlserver中
  - 添加wkzwyAppointment表的查询接口

## 0.0.12 更新日志
- 新增功能
  - 补充sqlserver数据库name配置
  - 实现HealthCheckAppointment模块的数据操作

## 0.0.11 更新日志
- 新增功能
  - 添加sqlserver数据库连接配置
  - 优化兼容：根据不同的环境变量来选择性地加载不同的数据库模块及其用到该数据库的子模块
  - 增加WkzwyAppointmentModule和HealthCheckAppointmentModule两个子模块

## 0.0.10 更新日志
- 功能调整
  - 分别新建和删除3张福州大屏oracle表entity

  ## 0.0.9 更新日志
- 配置调整
  - 在appModule中配置表实体路径

## 0.0.8 更新日志
- 功能调整
  - 在appModule中根据配置文件动态添加数据库连接配置
  - 修复oracle数据库insert报错问题（key值不能为null）
  - 使用SchedulerRegistry来动态地创建和启动Cron定时作业
  - 配置文件中添加福州定时任务时间间隔参数
  - 把oracle实体中id的列名改为ID

## 0.0.7 更新日志
- 配置更新
  - Set oracle thick mode

## 0.0.3, 0.0.4, 0.0.5, 0.0.6 更新日志
- 配置更新
  - 尝试service方式连接oracle数据库
  - 产线去掉database再试
  - 修复oracle数据库连接字符串
  - 修改mongodb和oracle数据库连接字符串

## 0.0.2 更新日志
- 新增功能
  - 新增大屏统计模块，从mongodb中获取大屏统计数据
  - 修复oracle数据库连接问题
  - 完成福州大屏模块，通过定时器添加数据到oracle数据库

## 0.0.2 更新日志
- 新增功能
  - 新增大屏统计模块，从mongodb中获取大屏统计数据
  - 修复oracle数据库连接问题
  - 完成福州大屏模块，通过定时器添加数据到oracle数据库

## 0.0.1 更新日志
- 新增功能
  - 项目初始化
  - mongodb数据库连接
  - 本地oracle连接配置
  - 根据process.env.NODE_ENV动态加载配置文件