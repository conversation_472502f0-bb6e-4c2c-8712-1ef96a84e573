import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { HealthyAdminorgsService } from './healthy-adminorgs.service';
import { error, success } from 'src/utils';
import { AssessmentIndicatorDto } from './dto/assessment-indicator.dto';

@Controller('healthy-adminorgs')
export class HealthyAdminorgsController {
  constructor(
    private readonly healthyAdminorgsService: HealthyAdminorgsService,
  ) {}

  /* 指标分类 */
  @Get('/getIndicatorClassifyTreeList')
  async getIndicatorClassifyTreeList() {
    try {
      const list =
        await this.healthyAdminorgsService.queryIndicatorClassifyTreeList();
      return success(list, '指标库分类数据获取成功');
    } catch (e) {
      return error('指标库分类数据获取失败');
    }
  }

  @Post('/addOrEditIndicatorClassify')
  async addOrEditIndicatorClassify(@Body() dto: any) {
    if (dto.id) {
      // 修改
      try {
        await this.healthyAdminorgsService.updateIndciatorClassify(dto);
        return success(null, '修改成功');
      } catch (e) {
        return error('修改失败');
      }
    }
    // 保存
    try {
      await this.healthyAdminorgsService.saveIndciatorClassify(dto);
      return success(null);
    } catch (e) {
      console.log(e);
      return error('保存失败');
    }
  }

  @Delete('/deleteIndicatorClassify')
  async deleteIndicatorClassify(@Query('id') id: string) {
    try {
      const list =
        await this.healthyAdminorgsService.queryIndicatorClassifyListByParentId(
          +id,
        );
      if (list && list.length > 0) {
        return error('当前节点下存在子节点，不允许删除');
      }
      const res =
        await this.healthyAdminorgsService.queryIndicatorListByParentId(+id);
      if (res && res.length > 0) {
        return error('当前指标已被使用，不允许删除');
      }
      await this.healthyAdminorgsService.deleteIndciatorClassify(+id);
      return success(null, '删除成功');
    } catch (e) {
      console.log(e);
      return error('删除失败');
    }
  }

  /* 指标 */
  @Get('/getIndicatorTreeList')
  async getIndicatorTreeList(@Query('id') id: string) {
    try {
      const res =
        await this.healthyAdminorgsService.queryIndicatorTreeList(+id);
      return success(res, '指标数据获取成功');
    } catch (e) {
      return error('指标数据获取失败');
    }
  }

  @Post('/addOrEditIndicator')
  async addOrEditIndicator(@Body() dto: any) {
    if (dto.id) {
      // 修改
      try {
        await this.healthyAdminorgsService.updateIndciator(dto);
        return success(null, '修改成功');
      } catch (e) {
        return error('修改失败');
      }
    } else {
      // 保存
      try {
        await this.healthyAdminorgsService.saveIndciator(dto);
        return success(null, '保存成功');
      } catch (e) {
        return error('保存失败');
      }
    }
  }

  @Delete('/deleteIndicator')
  async deleteIndicator(@Query('id') id: string) {
    try {
      const res =
        await this.healthyAdminorgsService.queryAssessmentInfoList(+id);
      if (res && res.length > 0) {
        return error('当前指标已被使用，不允许删除');
      }
      await this.healthyAdminorgsService.deleteIndciator(+id);
      return success(null, '删除成功');
    } catch (e) {
      return error('删除失败');
    }
  }

  /* 评估模型 */
  @Post('/addOrEditAssessmentModel')
  async addOrEditAssessmentModel(@Body() dto: any) {
    try {
      await this.healthyAdminorgsService.saveAssessmentModel(dto);
      return success(null, '保存成功');
    } catch (e) {
      console.log(e);
      return error('保存失败');
    }
  }

  @Get('/getAssessmentModel')
  async getAssessmentModel(@Query('id') id: string) {
    try {
      const res = await this.healthyAdminorgsService.queryAssessmentModel(+id);
      return success(res, '数据获取成功');
    } catch (e) {
      console.log(e);
      return error('数据获取失败');
    }
  }

  @Get('/getAssessmentModelList')
  async getAssessmentModelList(@Query() query: any) {
    try {
      const res =
        await this.healthyAdminorgsService.queryAssessmentModelList(query);
      return success(res, '数据获取成功');
    } catch (e) {
      return error('数据获取失败');
    }
  }

  @Put('/enableAssessmenModel')
  async enableAssessmenModel(@Body() dto: any) {
    try {
      await this.healthyAdminorgsService.enableAssessmenModel(dto);
      return success(null, '修改成功');
    } catch (e) {
      return error('修改失败');
    }
  }

  @Delete('/deleteAssessmentModel')
  async deleteAssessmentModel(@Query('id') id: string) {
    try {
      await this.healthyAdminorgsService.deleteAssessmentModel(+id);
      return success(null, '删除成功');
    } catch (e) {
      return error('删除失败');
    }
  }

  /* 申报 */
  @Post('/addOrEditDeclaration')
  async addOrEditDeclaration(@Body() dto: any) {
    try {
      await this.healthyAdminorgsService.saveDeclaration(dto);
      return success(null, '保存成功');
    } catch (e) {
      console.log(e);
      return error('保存失败');
    }
  }

  @Get('/getDeclarationList')
  async getDeclarationList(@Query() query: any) {
    try {
      const res =
        await this.healthyAdminorgsService.queryDeclarationList(query);
      return success(res, '数据获取成功');
    } catch (e) {
      return error('数据获取失败');
    }
  }

  @Get('/getDeclaration')
  async getDeclaration(@Query('id') id: string) {
    try {
      const res = await this.healthyAdminorgsService.queryDeclaration(+id);
      return success(res, '数据获取成功');
    } catch (e) {
      console.log(e);
      return error('数据获取失败');
    }
  }

  @Get('/getAssessmentModelDictionary')
  async getAssessmentModelDictionary() {
    try {
      const res =
        await this.healthyAdminorgsService.queryAssessmentModelDictionary();
      return success(res, '数据获取成功');
    } catch (e) {
      console.log(e);
      return error('数据获取失败');
    }
  }

  @Put('/rejectDeclaration')
  async rejectDeclaration(@Body() dto: any) {
    try {
      await this.healthyAdminorgsService.rejectDeclaration(dto);
      return success(null, '保存成功');
    } catch (e) {
      console.log(e);
      return error('保存失败');
    }
  }

  @Put('/submitDeclaration')
  async submitDeclaration(@Body() dto: any) {
    try {
      await this.healthyAdminorgsService.submitDeclaration(dto);
      return success(null, '提交成功');
    } catch (e) {
      console.log(e);
      return error('提交失败');
    }
  }
}
