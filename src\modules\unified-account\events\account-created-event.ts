import { END_POINT } from 'src/constants/endPoint';
import { AdminOrgsUser } from '../account-service/admin-orgs/admin-orgs-user.account.entity';
import { AdminOrgsOrg } from '../account-service/admin-orgs/admin-orgs-org.account.entity';

class AdminAccountCreatedEvent {
  constructor(
    public readonly endPoint: typeof END_POINT.qy,
    public readonly org: Admin<PERSON>rgsOrg,
    public readonly user: AdminOrgsUser,
  ) {}
}

export type AccountCreatedEvent = AdminAccountCreatedEvent;
