import { IsString, IsNotEmpty, IsOptional, IsDateString, IsArray, ValidateNested, ArrayMinSize, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';

export class Approver {
  @IsString()
  @IsNotEmpty({ message: '审批人ID不能为空' })
  id: string;

  @IsString()
  @IsNotEmpty({ message: '审批人名称不能为空' })
  name: string;
}

export class CreateApprovalDto {
  @IsString()
  @IsNotEmpty({ message: '标题不能为空' })
  title: string;

  @IsString()
  @IsNotEmpty({ message: '描述不能为空' })
  description: string;

  @IsString()
  @IsNotEmpty({ message: '链接不能为空' })
  link: string;

  @IsOptional()
  @IsDateString()
  start_at?: Date;

  @IsOptional()
  @IsDateString()
  end_at?: Date;

  @IsArray({ message: '审批人必须是数组' })
  @ArrayMinSize(1, { message: '至少需要一个审批人' })
  @ValidateNested({ each: true })
  @Type(() => Approver)
  approvers: Approver[];

  @IsOptional()
  @IsString()
  related_id?: string;
}