import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Expert } from './mysql/expert.entity';
import { ExpertClassifierService } from '../dictionary/expert_classifier.service'; // 专家分类
import * as moment from 'moment';
import { DictionaryService } from '../dictionary/dictionary.service';

@Injectable()
export class ExpertStatisticsService {
  constructor(
    @InjectRepository(Expert, 'mysqlConnectionIservice')
    private readonly expertRepository: Repository<Expert>,
    private readonly expertClassifierService: ExpertClassifierService,
    private readonly dictionaryService: DictionaryService,
  ) {}

  // 统计专家类型分布情况
  async statisticsByType() {
    const result = await this.expertRepository
      .createQueryBuilder('expert')
      .select('expert.category_id')
      .addSelect('COUNT(*) as count')
      .groupBy('expert.category_id')
      .getRawMany();
    const expertClassifiers = await this.expertClassifierService.findAll();
    const data = expertClassifiers.map((item) => {
      const count = result.find((res) => res.expert_category_id === item.id);
      return {
        id: item.id,
        name: item.classification_name,
        count: count ? +count.count : 0,
      };
    });
    return data;
  }

  // 专家年龄、性别分布情况
  async statisticsByAge() {
    const experts = await this.expertRepository.find();

    const ageGroups = {
      '30岁及以下': { male: 0, female: 0, total: 0 },
      '31-40岁': { male: 0, female: 0, total: 0 },
      '41-50岁': { male: 0, female: 0, total: 0 },
      '51-60岁': { male: 0, female: 0, total: 0 },
      '60岁以上': { male: 0, female: 0, total: 0 },
    };

    experts.forEach((expert) => {
      if (expert.birthday) {
        const age = moment().diff(expert.birthday, 'years');
        const group = this.getAgeGroup(age);

        if (expert.gender === 'M') {
          ageGroups[group].male += 1;
        } else if (expert.gender === 'F') {
          ageGroups[group].female += 1;
        }
        ageGroups[group].total += 1;
      }
    });

    const res = [];
    Object.entries(ageGroups).forEach(([key, value]) => {
      res.push({
        age_group: key,
        ...value,
      });
    });

    return res;
  }

  // 获取年龄段
  private getAgeGroup(age: number): string {
    if (age <= 30) {
      return '30岁及以下';
    } else if (age >= 31 && age <= 40) {
      return '31-40岁';
    } else if (age >= 41 && age <= 50) {
      return '41-50岁';
    } else if (age >= 51 && age <= 60) {
      return '51-60岁';
    } else {
      return '60岁以上';
    }
  }

  // 专家年龄、学历分布情况
  async statisticsByEducation() {
    const experts = await this.expertRepository.find();

    const ageGroups = {
      '30岁及以下': { total: 0 },
      '31-40岁': { total: 0 },
      '41-50岁': { total: 0 },
      '51-60岁': { total: 0 },
      '60岁以上': { total: 0 },
    };
    const education = await this.dictionaryService.education();
    const educationIds = education.map((item) => item.id);

    experts.forEach((expert) => {
      if (expert.birthday) {
        const age = moment().diff(expert.birthday, 'years');
        const group = this.getAgeGroup(age);

        ageGroups[group].total += 1;
        // 按照education字段进行统计
        if (educationIds.includes(expert.education)) {
          ageGroups[group][expert.education] = ageGroups[group][
            expert.education
          ]
            ? ageGroups[group][expert.education] + 1
            : 1;
        }
      }
    });

    const res = [];
    Object.entries(ageGroups).forEach(([key, value]) => {
      res.push({
        age_group: key,
        ...value,
      });
    });

    return { list: res, education };
  }

  // 专家流动情况
  async statisticsByMobility(query: {
    name?: string;
    phone?: string;
    pageSize?: number | string;
    curPage?: number | string;
    level?: number | string;
  }) {
    const { name, phone, level } = query;
    const limit = Number(query.pageSize) || 10;
    const curPage = Number(query.curPage) || 1;

    const queryBuilder = this.expertRepository.createQueryBuilder('expert');

    if (name) {
      queryBuilder.andWhere('expert.name LIKE :name', { name: `%${name}%` });
    }
    if (phone) {
      queryBuilder.andWhere('expert.phone LIKE :phone', {
        phone: `%${phone}%`,
      });
    }
    if (level) {
      queryBuilder.andWhere('expert.level = :level', { level });
    }

    const [experts, total] = await queryBuilder
      .skip((curPage - 1) * limit)
      .take(limit)
      .getManyAndCount();

    const education = await this.dictionaryService.education();
    const educationNames = education.map((item) => item.name);
    const expertClassifiers = await this.expertClassifierService.findAll();
    const expertClassifierObj = {};
    expertClassifiers.forEach((item) => {
      expertClassifierObj[item.id] = item.classification_name;
    });

    const result = experts.map((expert) => {
      return {
        id: expert.id,
        category: expertClassifierObj[expert.category_id],
        level: ['兵团级', '师市级', '团镇级'][expert.level - 1],
        name: expert.name,
        gender: expert.gender ? (expert.gender === 'M' ? '男' : '女') : '',
        id_number: expert.id_number,
        phone: expert.phone,
        age: expert.birthday ? moment().diff(expert.birthday, 'years') : '',
        education: educationNames[expert.education - 1] || '',
        work_unit: expert.work_unit || '',
        employment_date: expert.employment_date
          ? moment(expert.employment_date).format('YYYY-MM-DD')
          : '',
        dismissa_date: expert.dismissal_date
          ? moment(expert.dismissal_date).format('YYYY-MM-DD')
          : '', // 解聘时间
      };
    });
    return { total, list: result };
  }
}
