import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
} from 'typeorm';
// 专家抽取记录
@Entity('expert_extraction_records')
export class ExpertExtractionRecords {
  @PrimaryGeneratedColumn({ comment: '自增ID' })
  id: number;

  @Column({ comment: '管理要素', type: 'varchar', length: 64, nullable: false })
  management_element: string;

  @Column({
    comment: '工作事项',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  work_item: string;

  @Column({
    comment: '抽取专家名单',
    type: 'varchar',
    length: 64,
    nullable: false,
  })
  expert_list: string;

  @Column({ comment: '事项要求', type: 'varchar', length: 255, nullable: true })
  item_requirements?: string;

  @CreateDateColumn({ comment: '抽取时间', type: 'datetime' })
  created_at: Date;
}
