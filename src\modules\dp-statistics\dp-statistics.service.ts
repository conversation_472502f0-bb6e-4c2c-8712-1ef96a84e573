import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { MongoRepository } from 'typeorm';
import { DpStatistical } from './dp-statistics.entity';

@Injectable()
export class DpStatisticsService {
  constructor(
    @InjectRepository(DpStatistical, 'mongodbConnection')
    private dpStatisticalRepository: MongoRepository<DpStatistical>,
  ) {}
  async findOne(query: {
    table: string;
    adcode?: string;
  }): Promise<DpStatistical> {
    return await this.dpStatisticalRepository.findOneBy(query);
  }
  async findAll(query: {
    table?: string;
    adcode?: string;
  }): Promise<DpStatistical[]> {
    return await this.dpStatisticalRepository.find(query);
  }
}
