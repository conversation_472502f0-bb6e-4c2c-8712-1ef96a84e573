import { IsString, IsEnum, IsDate, IsOptional, IsBoolean, IsArray } from 'class-validator';

/**
 * 创建诊断记录DTO
 */
export class CreateDiagnosticRecordDto {
  @IsString()
  employerName: string;

  @IsString()
  employerCreditCode: string;

  @IsOptional()
  @IsString()
  laborEmployerName?: string;

  @IsOptional()
  @IsString()
  laborEmployerCreditCode?: string;

  @IsBoolean()
  hasOccupationalDisease: boolean;

  @IsOptional()
  @IsString()
  diagnosisConclusionDescription?: string;

  @IsOptional()
  occupationalDisease?: { name: string; code: string }[];

  @IsString()
  treatmentOpinion: string;

  @IsString()
  diagnosisInstitution: string;

  @IsDate()
  diagnosisDate: Date;

  @IsString()
  workerName: string;

  @IsString()
  phone: string;

  @IsOptional()
  @IsEnum(['1', '2'], { message: '性别必须是 1(男) 或 2(女)' })
  gender?: string;

  @IsString()
  idNumber: string;

  @IsString()
  diagnosisNumber: string;

  @IsOptional()
  @IsString()
  certificateUrl?: string;

  @IsOptional()
  @IsArray()
  fileList?: { name: string; url: string }[];

  @IsOptional()
  @IsDate()
  createdAt?: Date;

  @IsOptional()
  @IsDate()
  updatedAt?: Date;

  @IsOptional()
  @IsString()
  diagnosisConclusion?: string;
}
