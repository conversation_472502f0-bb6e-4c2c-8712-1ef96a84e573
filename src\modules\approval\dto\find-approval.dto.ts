import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsPositive } from 'class-validator';
import { Transform } from 'class-transformer';

export class FindApprovalDto {
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @IsPositive({ message: '页码必须大于0' })
  page?: number = 1;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1, { message: '每页条数不能小于1' })
  @Max(100, { message: '每页条数不能大于100' })
  pageSize?: number = 10;

  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  status?: number;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  created_by?: string;
}
