import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ngDeclaration } from './ng_declaration.entity';

// 申报审核记录
@Entity('ng_audit')
export class ngAudit {
  @PrimaryGeneratedColumn({ type: 'int', comment: 'audit_id' })
  id: number;

  @Column({ type: 'int', nullable: true, comment: '申报表id' })
  declaration_id: number | null;

  @Column({
    type: 'tinyint',
    nullable: true,
    comment: '审核状态(0待审核1已审核2已驳回)',
  })
  audit_status: number | null;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '驳回理由' })
  reject_reason: string | null;

  @Column({ type: 'int', nullable: true, comment: '审核人' })
  auditor_id: number | null;

  @CreateDateColumn({ type: 'datetime', nullable: true, comment: '审核时间' })
  audit_time: Date | null;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '备注（mongo用户的话nanoid写在此）' })
  comment: string | null;

  @Column({
    type: 'bigint',
    unsigned: true,
    nullable: true,
    comment: '回执编号',
  })

  @Column({ type: 'json', nullable: true, comment: '申报表盖章文件[{original:\"老文件名\", path:\"文件存取路径\"},...]' })
  snapshot_files: object | null;

  @Column({
    type: 'bigint',
    unsigned: true,
    nullable: true,
    comment: '回执编号',
  })
  receipt_number: number | null;

  @ManyToOne(() => ngDeclaration)
  @JoinColumn({ name: 'declaration_id' })
  declaration: ngDeclaration;
}
