import { Controller, Post, Body, Get, Param, Query } from '@nestjs/common';
import { WkzwyAppointmentService } from './wkzwy-appointment.service';
import { wrapperResponse } from 'src/utils';

@Controller('wkzwy-appointment')
export class WkzwyAppointmentController {
  constructor(
    private readonly wkzwyAppointmentService: WkzwyAppointmentService,
  ) {}
  // 同步预约单
  @Post()
  async create(@Body() body) {
    const { appointmentId } = body;
    return wrapperResponse(
      this.wkzwyAppointmentService.insert(appointmentId),
      '同步预约单',
    );
  }
  // 查询所有预约单
  @Get() // http://127.0.0.1:3000/wkzwy-appointment
  async findAll(@Query() query) {
    return wrapperResponse(
      this.wkzwyAppointmentService.findAll(query),
      '获取所有预约单的数据',
    );
  }
  // 根据id查询
  @Get('/:id') //  http://127.0.0.1:3000/wkzwy-appointment/1
  async findOne(@Param('id') id: string) {
    return wrapperResponse(
      this.wkzwyAppointmentService.findById(+id),
      '获取单个预约单的数据',
    );
  }
  // 根据id删除
  // @Delete('/:id')
  // async deleteOne(@Param('id') id: string) {
  //   return wrapperResponse(
  //     this.wkzwyAppointmentService.delete({ id: +id }),
  //     '删除单个预约单',
  //   );
  // }
}
