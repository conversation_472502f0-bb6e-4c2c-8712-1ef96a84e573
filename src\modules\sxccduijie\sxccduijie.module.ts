import { Modu<PERSON> } from '@nestjs/common';
import { SxccduijieService } from './sxccduijie.service';
import { CacheModule } from '@nestjs/cache-manager';
import { SxccduijieController } from './sxccduijie.controller';
import { Dingtrees } from './entities/dingtrees.entity';
import { Employees } from './entities/employees.entity';
import { Adminusers } from './entities/adminusers.entity';
import { Policy } from './entities/polices.entity';
import { GroupEnterprisesRecords } from './entities/groupEnterprisesRecords.entity';
import { Users } from './entities/users.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Adminorgs } from '../adminorgs/adminorgs.entity';
import { ScheduleModule } from '@nestjs/schedule';

@Module({
  imports: [
    TypeOrmModule.forFeature(
      [
        Dingtrees,
        Employees,
        Adminorgs,
        GroupEnterprisesRecords,
        Adminusers,
        Users,
        Policy,
      ],
      'mongodbConnection',
    ),
    ScheduleModule.forRoot(), // 定时任务
    CacheModule.register(),
  ],
  controllers: [SxccduijieController],
  providers: [SxccduijieService],
})
export class SxccduijieModule {}
