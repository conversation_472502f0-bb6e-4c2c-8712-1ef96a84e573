import {
  Entity,
  Column,
  Index,
  CreateDateColumn,
  UpdateDateColumn,
  ObjectIdColumn,
} from 'typeorm';

@Entity('healthcheckregisters')
@Index(['checkNo'], { unique: true })
@Index(['idNumber'])
@Index(['name'])
export class HealthCheckRegister {
  [x: string]: any;
  @ObjectIdColumn()
  _id: string; // 主键，保持string类型

  @Column()
  // 体检编号
  checkNo: string;

  @Column({ nullable: true })
  // 员工ID
  employeeID?: string;

  @Column({ nullable: true })
  // 企业ID
  enterpriseID?: string;

  @Column({ nullable: true })
  // 体检机构ID
  physicalOrgID?: string;

  @Column({ nullable: true })
  // 合同ID
  contractID?: string;

  @Column({ nullable: true })
  // 体检计划ID
  examPlanID?: string;

  @Column({ default: 0 })
  // 状态 0未登记 1已登记 2已总结 3审核通过 4审核不通过
  status: number;

  @Column({ nullable: true })
  // suspectId
  suspectId?: string;

  @Column({ nullable: true })
  // 审核原因
  auditReason?: string;

  @Column()
  // 姓名
  name: string;

  @Column({ nullable: true })
  // 性别 1男 2女
  gender?: string;

  @Column({ default: 1 })
  // 证件类型
  idType: number;

  @Column()
  // 证件号
  idNumber: string;

  @Column({ nullable: true })
  // 出生日期
  birthDate?: Date;

  @Column({ default: '10' })
  // 婚姻状况
  maritalStatus: string;

  @Column({ nullable: true })
  // 手机号码
  phone?: string;

  @Column({ nullable: true })
  // 住址
  address?: string;

  @Column({ nullable: true })
  // 紧急联系人
  emergencyContact?: string;

  @Column({ nullable: true })
  // 紧急联系人电话
  emergencyPhone?: string;

  @Column({ nullable: true })
  // 体检类型 1职业健康体检 2一般体检
  checkType?: string;

  @Column({ nullable: true })
  // 部门
  department?: string;

  @Column({ nullable: true })
  // 工种
  workType?: string;

  @Column({ type: 'simple-array', nullable: true })
  // 接触的危害因素
  hazardFactors?: string[];

  @Column({ type: 'simple-json', nullable: true })
  // 体检的危害因素
  checkHazardFactors?: any[];

  @Column({ default: 0 })
  // 总工龄年
  totalWorkYears: number;

  @Column({ default: 0 })
  // 总工龄月
  totalWorkMonths: number;

  @Column({ default: 0 })
  // 接触工龄年
  exposureWorkYears: number;

  @Column({ default: 0 })
  // 接触工龄月
  exposureWorkMonths: number;

  @Column({ nullable: true })
  // 检查类型 1岗前 2在岗 3离岗 4离岗后 5应急
  examType?: number;

  @Column({ nullable: true })
  // 是否复查
  isRecheck?: boolean;

  @Column({ nullable: true })
  // 复查的体检编号
  recheckNo?: string;

  @Column({ nullable: true })
  // 复查时间
  recheckTime?: Date;

  @Column({ type: 'simple-json', nullable: true })
  // 复查项目
  recheckProjects?: any[];

  @Column({ nullable: true })
  // 项目总价
  totalPrice?: number;

  @Column({ nullable: true })
  // 人脸照片
  facePhoto?: string;

  @Column({ type: 'simple-json', nullable: true })
  // 检查科室
  checkDepartments?: any[];

  @Column({ nullable: true })
  // 健结总结
  healthSummary?: string;

  @Column({ nullable: true })
  // 意见建议
  suggestion?: string;

  @Column({ nullable: true })
  // 职检总结
  jobSummary?: string;

  @Column({ type: 'simple-array', nullable: true })
  // 职检结论
  jobConclusion?: string[];
  // 1: '目前未见异常' 2: '复查'3: '疑似职业病'4: '职业禁忌证'5: '其他疾病或异常'

  @Column({ nullable: true })
  // 登记日期
  registerTime?: Date;

  @Column({ nullable: true })
  // 报告生成日期
  reportTime?: Date;

  @Column({ default: 1 })
  // 上报状态 1未上报 2已上报
  reportStatus: number;

  @Column({ default: '1' })
  // 诊断状态  1未申请
  diagnosisStatus: string;

  @CreateDateColumn()
  // 创建时间
  createdAt: Date;

  @UpdateDateColumn()
  // 更新时间
  updatedAt: Date;
}