-- 康复指导申请表
CREATE TABLE `rehab_guide_application` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `inst_id` BIGINT NOT NULL COMMENT '机构ID',
  `id_card` VARCHAR(18) NOT NULL COMMENT '身份证号',
  `name` VARCHAR(50) NOT NULL COMMENT '姓名',
  `create_date` DATETIME NOT NULL COMMENT '创建日期',
  `disease_category` CHAR(7) COMMENT '职业病病人分类编码',
  `service_type` TEXT COMMENT '服务类型描述',
  `guide_type` VARCHAR(20) NOT NULL COMMENT '指导类型：online-线上，offline-线下',
  `content` TEXT NOT NULL COMMENT '申请内容',
  `requirements` TEXT COMMENT '需求说明',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '状态：0-待处理，1-已接受，2-已拒绝，3-已完成',
  `attachment_urls` TEXT COMMENT '附件URL，多个用逗号分隔',
  `process_time` DATETIME COMMENT '处理时间',
  `process_user_id` BIGINT COMMENT '处理人ID',
  `process_notes` TEXT COMMENT '处理备注',
  `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-否，1-是',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_inst_id` (`inst_id`),
  INDEX `idx_id_card` (`id_card`),
  INDEX `idx_create_date` (`create_date`),
  INDEX `idx_status` (`status`),
  INDEX `idx_disease_category` (`disease_category`),
  INDEX `idx_is_deleted` (`is_deleted`),
  INDEX `idx_guide_type` (`guide_type`),
  CHECK (`guide_type` IN ('online', 'offline'))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='康复指导申请表'; 