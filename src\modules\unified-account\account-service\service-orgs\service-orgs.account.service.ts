import { Injectable } from '@nestjs/common';
import { AccountService } from '../account-service.interface';
import { ServiceOrg } from './service-orgs-org.account.entity';
import { ServiceUser } from './service-orgs-user.account.entity';
import { MongoRepository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { plainToClass } from 'class-transformer';
import { ConfigService } from '@nestjs/config';
import {
  CreateServiceOrgData,
  CreateServiceUserData,
  CreateServiceAccountData,
  QueryServiceOrgData,
} from '../../types';

@Injectable()
export class ServiceOrgAccountService
  implements AccountService<CreateServiceAccountData, ServiceUser>
{
  constructor(
    @InjectRepository(ServiceOrg, 'mongodbConnection')
    private readonly ServiceOrgRepository: MongoRepository<ServiceOrg>,
    @InjectRepository(ServiceUser, 'mongodbConnection')
    private readonly ServiceUserRepository: MongoRepository<ServiceUser>,

    private readonly configService: ConfigService,
  ) {}

  async upsertSubAccounts(
    data: CreateServiceAccountData,
  ): Promise<ServiceUser> {
    const org = await this.upsertOrgAccount(data.org);
    const user = await this.upsertUserAccount({
      ...data.user,
      org_id: org._id,
      org: org.name,
    });
    await this.relationOrgManager(org._id, user._id);
    return user;
  }

  async upsertOrgAccount(data: CreateServiceOrgData): Promise<ServiceOrg> {
    let org = await this.getOrgAccountByQuery({
      organization: data.organization,
    });
    if (!org) {
      org = await this.createOrgAccount(data);
    } else {
      org = await this.updateOrgAccount(org._id, data);
    }
    return org;
  }

  async upsertUserAccount(data: CreateServiceUserData): Promise<ServiceUser> {
    let user = await this.getUserAccountByQuery({
      phoneNum: data.phoneNum,
    });
    if (!user) {
      user = await this.createUserAccount(data);
    } else {
      user = await this.updateUserAccount(user._id, data);
    }
    return user;
  }

  async getAccount(id: string): Promise<ServiceUser> {
    const user = await this.ServiceUserRepository.findOne({
      where: { _id: id },
    });
    return user;
  }

  async getOrgAccountByQuery(query: QueryServiceOrgData): Promise<ServiceOrg> {
    const org = await this.ServiceOrgRepository.findOne({
      where: query,
    });
    return org;
  }

  async getUserAccountByQuery(query: {
    phoneNum: string;
  }): Promise<ServiceUser> {
    const user = await this.ServiceUserRepository.findOne({
      where: query,
    });
    return user;
  }

  async getUserAccountById(id: string): Promise<ServiceUser> {
    const user = await this.ServiceUserRepository.findOne({
      where: { _id: id },
    });
    return user;
  }

  async getAccountByQuery(query: QueryServiceOrgData): Promise<ServiceUser> {
    const org = await this.getOrgAccountByQuery(query);
    if (!org?.administrator) {
      return null;
    }
    const user = await this.getUserAccountById(org.administrator);
    return user;
  }

  async createOrgAccount(data: CreateServiceOrgData): Promise<ServiceOrg> {
    const org = await this.ServiceOrgRepository.save(
      plainToClass(ServiceOrg, {
        ...data,
      }),
    );
    return org;
  }

  async createUserAccount(data: CreateServiceUserData): Promise<ServiceUser> {
    const user = await this.ServiceUserRepository.save(
      plainToClass(ServiceUser, {
        group: this.configService.get('groupID').serviceGroupID,
        ...data,
      }),
    );
    return user;
  }

  async relationOrgManager(orgId: string, userId: string): Promise<void> {
    await this.ServiceOrgRepository.updateOne(
      {
        _id: orgId,
      },
      {
        $addToSet: {
          managers: userId,
        } as any,
        $set: {
          administrator: userId,
        },
      },
    );
  }

  async updateOrgAccount(
    orgId: string,
    data: Partial<ServiceOrg>,
  ): Promise<ServiceOrg> {
    const result = await this.ServiceOrgRepository.updateOne(
      { _id: orgId },
      { $set: data },
      // { upsert: false }  // 如果不存在则不创建新记录
    );
    return plainToClass(ServiceOrg, result);
  }

  async updateUserAccount(
    userId: string,
    data: Partial<ServiceUser>,
  ): Promise<ServiceUser> {
    const result = await this.ServiceUserRepository.updateOne(
      { _id: userId },
      { $set: data },
      // { upsert: false }  // 如果不存在则不创建新记录
    );
    return plainToClass(ServiceUser, result);
  }
  
  async validateAccount(/*id: string*/): Promise<boolean> {
    return Promise.resolve(true);
  }
}
