import {
  IsString,
  <PERSON><PERSON><PERSON>ber,
  IsOptional,
  IsDateString,
  Length,
  IsNotEmpty,
  IsIn,
} from 'class-validator';

export class CreateRehabGuideApplicationDto {
  @IsString()
  @Length(18, 18)
  @IsNotEmpty()
  id_card: string;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsDateString()
  @IsNotEmpty()
  create_date: string;

  @IsString()
  @IsOptional()
  @Length(7, 7)
  disease_category?: string;

  @IsString()
  @IsOptional()
  service_type?: string;

  @IsString()
  @IsNotEmpty()
  @IsIn(['online', 'offline'])
  guide_type: 'online' | 'offline';

  @IsString()
  content: string;

  @IsString()
  @IsOptional()
  requirements?: string;

  @IsString()
  @IsOptional()
  attachment_urls?: string;
}
