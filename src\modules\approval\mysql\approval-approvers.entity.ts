import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, <PERSON>inColumn } from 'typeorm';
import { Approval } from './approval.entity';

@Entity('approval_approvers')
export class ApprovalApprovers {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'int', nullable: true })
  approval_id: number;

  @Column({ type: 'varchar', nullable: true })
  approver_id: string;

  @Column({ type: 'varchar', nullable: true })
  approver_name: string;

  @ManyToOne(() => Approval, approval => approval.approvers)
  @JoinColumn({ name: 'approval_id' })
  approval: Approval;
}