import { Module } from '@nestjs/common';
import { UnifiedAccountService } from './unified-account.service';
import { PhysicalExamAccountService } from './account-service/physical-exam/physical-exam-account.service';
import { SuperUserAccountService } from './account-service/super-user/super-user.account.service';
import { AdminOrgsAccountService } from './account-service/admin-orgs/admin-orgs.account.service';
import { UnifiedAccountController } from './unified-account.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PhysicalExamUser } from './account-service/physical-exam/physical-exam-user.account.entity';
import { PhysicalExamOrg } from './account-service/physical-exam/physical-exam-org.account.entity';
import { SuperUser } from './account-service/super-user/super-user.account.entity';
import { AdminOrgsUser } from './account-service/admin-orgs/admin-orgs-user.account.entity';
import { AdminOrgsOrg } from './account-service/admin-orgs/admin-orgs-org.account.entity';
import { ServiceOrg } from './account-service/service-orgs/service-orgs-org.account.entity';
import { ServiceUser } from './account-service/service-orgs/service-orgs-user.account.entity';
import { ServiceOrgAccountService } from './account-service/service-orgs/service-orgs.account.service';

@Module({
  imports: [
    TypeOrmModule.forFeature(
      [
        PhysicalExamUser,
        PhysicalExamOrg,
        SuperUser,
        AdminOrgsOrg,
        AdminOrgsUser,
        ServiceOrg,
        ServiceUser,
      ],
      'mongodbConnection',
    ),
  ],
  controllers: [UnifiedAccountController],
  providers: [
    UnifiedAccountService,
    PhysicalExamAccountService,
    SuperUserAccountService,
    AdminOrgsAccountService,
    ServiceOrgAccountService,
  ],
  exports: [UnifiedAccountService],
})
export class UnifiedAccountModule {}
