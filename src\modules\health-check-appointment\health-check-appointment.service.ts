import { Injectable } from '@nestjs/common';
import { HealthCheckAppointment } from './health-check-appointment.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { MongoRepository } from 'typeorm';

@Injectable()
export class HealthCheckAppointmentService {
  constructor(
    @InjectRepository(HealthCheckAppointment, 'mongodbConnection')
    private healthCheckAppointmentRepository: MongoRepository<HealthCheckAppointment>,
  ) {}

  async findOne(query: { _id?: string }): Promise<HealthCheckAppointment> {
    return await this.healthCheckAppointmentRepository.findOneBy(query);
  }

  async findAll(query = {}): Promise<HealthCheckAppointment[]> {
    return await this.healthCheckAppointmentRepository.find(query);
  }
}
