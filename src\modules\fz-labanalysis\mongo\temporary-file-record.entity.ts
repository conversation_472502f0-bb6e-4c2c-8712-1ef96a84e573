import { Entity, Column, ObjectIdColumn } from 'typeorm';
import { ObjectId } from 'mongodb';
import shortid from 'shortid';

class WordFileName {
  @ObjectIdColumn({ default: () => shortid.generate() })
  _id: ObjectId;

  @Column()
  sampleSN: string;

  @Column()
  resultseq: string;

  @Column()
  name: string;

  @Column()
  url: string;

  @Column()
  FINSTLXS: string;

  @Column()
  FINSTNMS: string;

  @Column()
  FINSTNOS: string;

  @Column()
  FINSTXHS: string;
}

@Entity('temporaryFileRecord')
export class temporaryFileRecord {
  @ObjectIdColumn({ default: () => shortid.generate() })
  _id: ObjectId;

  @Column()
  projectSN: string;

  @Column()
  projectId: string;

  @Column(() => WordFileName)
  temporaryFile: WordFileName[]; // 原始记录单信息
}
