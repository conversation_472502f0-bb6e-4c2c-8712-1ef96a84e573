import { AssessmentInfo } from './assessment-info.entity';
import { BasicConditions } from './basic-conditions.entity';
import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  OneToMany,
  AfterLoad,
} from 'typeorm';

import { format } from 'date-fns';

// 考核指标表 mysql
// 该表用于存储考核指标的相关信息
@Entity('assessment_model')
export class AssessmentModel {
  @PrimaryGeneratedColumn({ name: 'id', type: 'int', comment: '自增ID' })
  id: number;

  @Column({ comment: '年度', type: 'varchar' })
  year: string;

  @CreateDateColumn({ comment: '发布时间', type: 'datetime' })
  release_time: Date;

  @CreateDateColumn({ comment: '创建时间', type: 'datetime' })
  create_time: Date;

  @Column({
    type: 'enum',
    enum: ['0', '1', '2'],
    comment: '发布状态：0 暂存 1 发布 2 禁用',
  })
  release_status: string;

  @OneToMany(
    () => BasicConditions,
    (basicConditions) => basicConditions.assessmentModel,
  )
  basic_conditions: BasicConditions[];

  // @OneToMany(() => AssessmentInfo, assessmentInfo => assessmentInfo.assessmentModel)
  assessment_info: AssessmentInfo[];

  release_time_format: string;

  create_time_format: string;

  @AfterLoad()
  formatDate() {
    if (this.release_time) {
      this.release_time_format = format(
        this.release_time,
        'yyyy-MM-dd HH:mm:ss',
      );
    }

    if (this.create_time) {
      this.create_time_format = format(this.create_time, 'yyyy-MM-dd HH:mm:ss');
    }
  }
}
