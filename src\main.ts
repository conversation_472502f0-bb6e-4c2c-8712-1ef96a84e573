import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import { VersioningType } from '@nestjs/common';
// import { AppService } from './app.service';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // 启动 OracleService，并监听表的修改
  // const oracleService = app.get(AppService);
  // await oracleService.subscribeToTableChanges();

  app.enableVersioning({
    type: VersioningType.URI, // 启用 URI 版本控制
    prefix: 'v', // 版本前缀，例如 /v1/todos
  });

  const port = app.get(ConfigService).get<number>('port');
  await app.listen(port);
  // 监听 0.0.0.0 表示监听所有网络接口
  // await app.listen(port, '0.0.0.0');
  
  // console.log(`Application is running on: ${await app.getUrl()}`);
}
bootstrap();
