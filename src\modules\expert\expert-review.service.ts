import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ExpertReview } from './mysql/expert_review.entity';
import { ExpertService } from './expert.service';
import { ExpertClassifierService } from '../dictionary/expert_classifier.service';
import * as moment from 'moment';

@Injectable()
export class ExpertReviewService {
  constructor(
    @InjectRepository(ExpertReview, 'mysqlConnectionIservice')
    private readonly expertReviewRepository: Repository<ExpertReview>,
    private readonly expertService: ExpertService,
    private readonly expertClassifierService: ExpertClassifierService,
  ) {}
  private readonly logger = new Logger(ExpertReviewService.name);

  // 新建专家申请
  async create(body: {
    expert_id: number;
    category_id: number;
    level: number;
    review_type: number;
    applicant?: string;
  }) {
    try {
      const {
        expert_id,
        category_id,
        level,
        review_type,
        applicant = '',
      } = body;

      if (!expert_id || !category_id || !level || !review_type) {
        console.log(1111, body);
        throw new HttpException(
          '创建待审核记录时的参数不全',
          HttpStatus.BAD_REQUEST,
        );
      }
      if (![1, 2, 3].includes(review_type)) {
        throw new HttpException('无效的申请类型', HttpStatus.BAD_REQUEST);
      }
      const typeName = ['专家申报', '专家推荐', '专家解聘'][review_type - 1];
      if (![1, 2, 3].includes(level)) {
        throw new HttpException('无效的专家级别', HttpStatus.BAD_REQUEST);
      }
      const expert = await this.expertService.findOne(expert_id);
      if (!expert) {
        throw new HttpException('未找到该专家信息', HttpStatus.BAD_REQUEST);
      }
      if (+expert.declaration_status === 2) {
        throw new HttpException(
          `该专家已提交${typeName}申请，请勿重复操作`,
          HttpStatus.BAD_REQUEST,
        );
      }
      if ([0, 4, 5].includes(+expert.state)) {
        throw new HttpException(
          '该专家状态异常，不可申报。',
          HttpStatus.BAD_REQUEST,
        );
      }
      // 查询是否已有待审核记录
      const review = await this.expertReviewRepository.findOne({
        where: { expert_id, review_type, category_id, level },
      });
      if (
        review &&
        review.review_status !== 4 &&
        review &&
        review.review_status !== 3
      ) {
        throw new HttpException(
          `该专家已提交${typeName}申请，请勿重复操作11.`,
          HttpStatus.BAD_REQUEST,
        );
      }
      const res = await this.expertReviewRepository.save({
        review_type,
        expert_id,
        category_id,
        level,
        applicant,
      });
      if (res && res.id) {
        await this.expertService.updateInner(expert_id, {
          declaration_status: 2,
        });
        return res;
      }
    } catch (e) {
      this.logger.error(e);
      throw new HttpException('操作失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 审核记录列表
  async list(
    query: {
      review_type?: number;
      expert_id?: number;
      pageSize?: number | string;
      curPage?: number | string;
      category_id?: number;
      review_status?: number;
    } = {},
  ): Promise<any> {
    try {
      const {
        pageSize = 10,
        curPage = 1,
        expert_id,
        review_type,
        category_id,
        review_status,
      } = query;
      console.log(1111, query);
      // 查询条件
      const where: any = {};
      if (review_type) {
        where.review_type = +review_type;
      }
      if (category_id) {
        where.category_id = +category_id;
      }
      if (review_status) {
        where.review_status = +review_status;
      }

      // 查询专家分类
      const expertClassifiers = await this.expertClassifierService.findAll();
      const expertClassifierObj = {};
      expertClassifiers.forEach((item) => {
        expertClassifierObj[item.id] = item.classification_name;
      });
      // 返回的数据处理
      const formatReview = (item: ExpertReview) => ({
        id: item.id,
        expert_id: item.expert_id,
        review_type: ['专家申报', '专家推荐', '专家解聘'][item.review_type - 1],
        level: ['兵团级', '师市级', '团镇级'][item.level - 1],
        category: expertClassifierObj[item.category_id],
        review_status:
          typeof item.review_status === 'number'
            ? ['审核通过', '待审核', '审核未通过', '已撤销'][
                item.review_status - 1
              ]
            : '',
        review_comment: item.review_comment || '',
        reviewer: item.reviewer || '',
        review_time: item.review_time
          ? moment(item.review_time).format('YYYY-MM-DD HH:mm')
          : '',
        created_at: moment(item.created_at).format('YYYY-MM-DD HH:mm'),
        updated_at: moment(item.updated_at).format('YYYY-MM-DD HH:mm'),
      });
      // 查询单个专家的审核记录
      if (expert_id) {
        where.expert_id = +expert_id;
        const res = await this.expertReviewRepository.find({
          where,
          order: { created_at: 'DESC' },
        });
        return res.map((ele) => formatReview(ele));
      }
      // 分页查询
      const [list, total] = await this.expertReviewRepository.findAndCount({
        where,
        order: { created_at: 'DESC' },
        take: +pageSize,
        skip: (+curPage - 1) * +pageSize,
      });
      const resList = [];
      for (const item of list) {
        const expert_info = await this.expertService.findInfo(item.expert_id);
        if (!expert_info) continue;
        resList.push({
          ...formatReview(item),
          expert_name: expert_info.name,
        });
      }
      return { total, list: resList };
    } catch (e) {
      this.logger.error(e);
      throw new HttpException('查询失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 专家申报审核
  async update(id: number, body: any) {
    try {
      const { review_status, review_comment, reviewer } = body;
      if (![1, 3, 4].includes(review_status)) {
        throw new HttpException('无效的审核状态', HttpStatus.BAD_REQUEST);
      }
      const res = await this.expertReviewRepository.update(id, {
        review_comment,
        reviewer,
        review_time: new Date(),
        review_status,
      });
      if (res && res.affected) {
        const review = await this.expertReviewRepository.findOne({
          where: { id },
        });
        const expert_id = review.expert_id;
        if (!expert_id) {
          throw new HttpException('未找到该专家信息', HttpStatus.BAD_REQUEST);
        }
        const { category_id, level } = review;
        const updateData: {
          declaration_status: number;
          level?: number;
          category_id?: number;
        } = {
          declaration_status:
            review_status === 1 ? 1 : review_status === 3 ? 3 : 0,
        };
        if (review_status === 1) {
          // 通过审核
          if (level) updateData.level = +level;
          if (category_id) updateData.category_id = +category_id;
        }
        await this.expertService.updateInner(expert_id, updateData);
        return body;
      }
      throw new HttpException('操作失败', HttpStatus.INTERNAL_SERVER_ERROR);
    } catch (e) {
      this.logger.error(e);
      throw new HttpException('操作失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 专家推荐
  async recommend(
    query: {
      id?: number;
      expert_id?: number;
      operate: number; //  1专家推荐申请、2同意兵团级专家申请、3驳回申请
      review_comment?: string; // 驳回原因
      applicant?: string; // 申请人
      reviewer?: string; // 审核人
    } = null,
  ) {
    try {
      if (!query) {
        throw new HttpException('参数不能为空', HttpStatus.BAD_REQUEST);
      }
      const { id, operate, review_comment = '', expert_id, reviewer } = query;
      if (!expert_id) {
        throw new HttpException('expert_id不能为空', HttpStatus.BAD_REQUEST);
      }
      if (![1, 2, 3].includes(+operate)) {
        throw new HttpException('operate参数错误', HttpStatus.BAD_REQUEST);
      }
      if (operate !== 1 && !id) {
        throw new HttpException('审核时id不能为空', HttpStatus.BAD_REQUEST);
      }
      // 推荐逻辑
      const expert = await this.expertService.findInfo(expert_id);
      if (!expert) {
        throw new HttpException('未找到该专家信息', HttpStatus.BAD_REQUEST);
      }
      const { level, state } = expert;
      if (level === 1) {
        throw new HttpException('兵团级专家不可推荐', HttpStatus.BAD_REQUEST);
      }
      if (operate == 1) {
        // 推荐申请
        if (+state === 6) {
          throw new HttpException(
            '该专家已被推荐，请勿重复推荐',
            HttpStatus.BAD_REQUEST,
          );
        }
        const res = await this.create({
          expert_id,
          category_id: expert.category_id,
          level: level - 1,
          review_type: 2,
          applicant: query.applicant,
        });
        if (res && res.id) {
          await this.expertService.updateInner(expert_id, { state: 6 });
          return res;
        } else {
          throw new HttpException(
            '推荐记录添加失败',
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
        }
      } else if (operate == 2) {
        // 同意推荐
        const res = await this.expertReviewRepository.update(id, {
          review_status: 1,
          review_comment,
          reviewer,
          review_time: new Date(),
        });
        if (res && res.affected) {
          await this.expertService.updateInner(expert_id, {
            level: level - 1,
            state: 1,
          });

          return res;
        } else {
          throw new HttpException(
            '推荐同意失败',
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
        }
      } else {
        // 驳回推荐
        const res = await this.expertReviewRepository.update(id, {
          review_status: 3,
          review_comment,
          reviewer,
          review_time: new Date(),
        });
        if (res && res.affected) {
          await this.expertService.updateInner(expert_id, { state: 1 });
          return res;
        }
        throw new HttpException(
          '推荐驳回失败',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    } catch (e) {
      console.log(4444, e);
      // this.logger.error(e.message);
      throw new HttpException(e.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 专家解聘
  async dismissal(
    query: {
      id?: number;
      expert_id?: number;
      operate: number; //  1添加申请、2同意申请、3驳回申请
      review_comment?: string; // 驳回原因
      applicant?: string; // 申请人
      reviewer?: string; // 审核人
    } = null,
  ) {
    if (!query) {
      throw new HttpException('参数不能为空', HttpStatus.BAD_REQUEST);
    }
    const { id, operate, review_comment = '', expert_id, reviewer } = query;
    if (!expert_id) {
      throw new HttpException('expert_id不能为空', HttpStatus.BAD_REQUEST);
    }
    if (![1, 2, 3].includes(+operate)) {
      throw new HttpException('operate参数错误', HttpStatus.BAD_REQUEST);
    }
    if (operate !== 1 && !id) {
      throw new HttpException('审核时id不能为空', HttpStatus.BAD_REQUEST);
    }

    // 推荐逻辑
    const expert = await this.expertService.findInfo(expert_id);
    if (!expert) {
      throw new HttpException('未找到该专家信息', HttpStatus.BAD_REQUEST);
    }

    const state = +expert.state;
    if (operate == 1) {
      // 添加解聘申请
      if (state === 5) {
        throw new HttpException(
          '该专家解聘申请已提交，请勿重复操作',
          HttpStatus.BAD_REQUEST,
        );
      }
      if (state === 4) {
        throw new HttpException(
          '该专家已解聘，请勿重复操作',
          HttpStatus.BAD_REQUEST,
        );
      }
      const res = await this.create({
        expert_id,
        review_type: 3,
        level: expert.level,
        category_id: expert.category_id,
        applicant: query.applicant || '',
      });
      if (res && res.id) {
        await this.expertService.updateInner(expert_id, { state: 5 });
        return res;
      }
      throw new HttpException(
        '解聘申请添加失败',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    } else if (operate == 2) {
      // 同意解聘
      const res = await this.expertReviewRepository.update(id, {
        review_status: 1,
        review_comment,
        reviewer,
        review_time: new Date(),
      });
      if (res && res.affected) {
        await this.expertService.updateInner(expert_id, {
          state: 4,
          dismissal_date: new Date(),
        });
        return res;
      }
      throw new HttpException(
        '同意解聘申请失败',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    } else {
      // 驳回解聘申请
      const res = await this.expertReviewRepository.update(id, {
        review_status: 3,
        review_comment,
        reviewer,
        review_time: new Date(),
      });
      if (res && res.affected) {
        await this.expertService.updateInner(expert_id, { state: 1 });
        return res;
      }
      throw new HttpException(
        '驳回解聘申请失败',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // 取消审核记录
  async delete(data: { id: number; expert_id?: number }) {
    try {
      const { id, expert_id } = data;
      console.log(1111, data);
      if (!id || !expert_id) {
        throw new HttpException('参数不全', HttpStatus.BAD_REQUEST);
      }
      const review = await this.expertReviewRepository.findOne({
        where: { id },
      });
      if (!review) {
        throw new HttpException(
          `未找到id为${id}的申请记录`,
          HttpStatus.BAD_REQUEST,
        );
      }
      if (review.review_status === 1) {
        throw new HttpException('已通过审核，不可取消', HttpStatus.BAD_REQUEST);
      } else if (review.review_status === 4) {
        throw new HttpException(
          '申请已撤销，请勿重复操作',
          HttpStatus.BAD_REQUEST,
        );
      }
      if (expert_id && review.expert_id !== expert_id) {
        throw new HttpException(
          '专家id不匹配，不可进行取消操作。',
          HttpStatus.BAD_REQUEST,
        );
      }
      const res = await this.expertReviewRepository.update(id, {
        review_status: 4,
      });
      if (res && res.affected) {
        await this.expertService.updateInner(expert_id, {
          declaration_status: 0,
        });
        return res;
      }
      throw new HttpException(
        '取消审核记录失败',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    } catch (e) {
      this.logger.error(e);
      throw new HttpException(
        '取消审核记录失败',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
