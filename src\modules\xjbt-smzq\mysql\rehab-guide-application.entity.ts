import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  Index,
  ManyToOne,
  JoinColumn,
  Check,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { DiseaseCategoryDict } from './disease-category-dict.entity';

@Entity('rehab_guide_application')
@Check(`guide_type IN ('online', 'offline')`)
export class RehabGuideApplication {
  @PrimaryGeneratedColumn({ name: 'id', type: 'int', comment: 'ID' })
  id: number;

  @Column({ type: 'bigint', comment: '机构ID' })
  @Index('idx_inst_id')
  inst_id: number;

  @Column({ type: 'varchar', length: 18, comment: '身份证号' })
  @Index('idx_id_card')
  id_card: string;

  @Column({ type: 'varchar', length: 50, comment: '姓名' })
  name: string;

  @Column({ type: 'datetime', comment: '创建日期' })
  @Index('idx_create_date')
  create_date: Date;

  @Column({
    type: 'char',
    length: 7,
    nullable: true,
    comment: '职业病病人分类编码',
  })
  @Index('idx_disease_category')
  disease_category: string;

  @ManyToOne(() => DiseaseCategoryDict)
  @JoinColumn({
    name: 'disease_category',
    referencedColumnName: 'category_code',
  })
  disease_category_relation: DiseaseCategoryDict;

  @Column({ type: 'text', nullable: true, comment: '服务类型描述' })
  service_type: string;

  @Column({
    type: 'varchar',
    length: 20,
    comment: '指导类型：online-线上，offline-线下',
  })
  @Index('idx_guide_type')
  guide_type: 'online' | 'offline';

  @Column({ type: 'text', nullable: true, comment: '申请内容' })
  content: string;

  @Column({ type: 'text', nullable: true, comment: '需求说明' })
  requirements: string;

  @Column({
    type: 'tinyint',
    default: 0,
    comment: '状态：0-待处理，1-已接受，2-已拒绝，3-已完成',
  })
  @Index('idx_status')
  status: number;

  @Column({ type: 'text', nullable: true, comment: '附件URL，多个用逗号分隔' })
  attachment_urls: string;

  @Column({ type: 'datetime', nullable: true, comment: '处理时间' })
  process_time: Date;

  @Column({ type: 'bigint', nullable: true, comment: '处理人ID' })
  process_user_id: number;

  @Column({ type: 'text', nullable: true, comment: '处理备注' })
  process_notes: string;

  @Column({
    type: 'tinyint',
    width: 1,
    default: 0,
    comment: '是否删除：0-否，1-是',
  })
  @Index('idx_is_deleted')
  is_deleted: boolean;

  @CreateDateColumn({ comment: '创建时间' })
  create_time: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  update_time: Date;
}
