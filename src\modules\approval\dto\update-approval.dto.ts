import { IsOptional, ValidateNested, IsArray } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateApprovalDto, Approver } from './create-approval.dto';

export class UpdateApprovalDto {
  @IsOptional()
  title?: string;

  @IsOptional()
  description?: string;

  @IsOptional()
  link?: string;

  @IsOptional()
  start_at?: Date;

  @IsOptional()
  end_at?: Date;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Approver)
  approvers?: Approver[];
}