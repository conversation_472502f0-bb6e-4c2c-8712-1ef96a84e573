import {
  Controller,
  Get,
  Post,
  Logger,
  Query,
  Body,
  Put,
  Delete,
} from '@nestjs/common';
import { QykService } from './qyk.service';
import { QyOnlineDeclarationService } from './qyOnlineDeclaration.service';
import { success, error } from 'src/utils';
import { QueryQykDto } from './dto/qyk.dto';
import { QueryOnlineDeclaration } from './dto/queryOnlineDeclaration.dto';
import { QueryOrgInfoDto } from './dto/queryOrgInfoDto.dto';
import { QueryStsProject } from './dto/queryStsProject.dto';
import { EmployerService } from './employer.service';

@Controller('hebei')
export class HebeiController {
  constructor(
    private readonly qykService: QykService,
    private readonly qyOnlineDeclarationService: QyOnlineDeclarationService,
    private readonly employerService: EmployerService,
  ) {}
  private readonly logger = new Logger(HebeiController.name);

  // 用人单位统计 - 危害申报
  // 1、累积已申报用人单位数；2、新增申报用人单位数；
  // /hebei/qyCount?type=1&year=2023&jjlx=有限责任公司&districtName=河北省&hylb=水泥制品制造&qygm=2&sblb=初次申报&startTime=2023-1-1&endTime=2023-11-24
  @Get('qyHasSbCount')
  async qyHasSbCount(@Query() query: QueryQykDto) {
    try {
      const res = await this.qykService.qyHasSbCount(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  // 用人单位统计 - 危害申报
  // 1、已检测未申报用人单位数；
  @Get('qyNotSbCount')
  async qyNotSbCount(@Query() query: QueryQykDto) {
    try {
      const res = await this.qykService.qyNotSbCount(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  @Get('QueryOrgInfo')
  async QueryOrgInfo(@Query() query: QueryOrgInfoDto) {
    try {
      const res = await this.qykService.QueryOrgInfo(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }
  /**
   * 统计分析 ods_ent_tj_qyk_new
   *  筛选条件
   */
  @Get('qyknewtjfx')
  async qyknewtjfx(@Query() query: QueryQykDto) {
    try {
      console.log('query', query);
      const res = await this.qyOnlineDeclarationService.qyknewtjfx(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }
  /**
   * 统计分析 ods_ent_tj_qyk_new
   *  导出所有
   */
  @Get('exportQyknewtjfx')
  async exportQyknewtjfx(@Query() query: QueryQykDto) {
    try {
      console.log('query', query);
      const res = await this.qyOnlineDeclarationService.exportQyknewtjfx(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }
  /**
   * 统计分析 ods_ent_tj_qyk_new
   *  图数据
   */
  @Get('countByAddress')
  async countByAddress(@Query() query: QueryQykDto) {
    try {
      console.log('query', query);
      const res = await this.qyOnlineDeclarationService.countByAddress(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }
  /**
   * 首页 ods_ent_tj_qyk_new
   *  图数据
   */
  @Get('calculateQyknewtjfx')
  async calculateQyknewtjfx(@Query() query: QueryQykDto) {
    try {
      const res = await this.qyOnlineDeclarationService.calculateQyknewtjfx(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }
  /**
   * 三同时项目统计 tj_technicalquality_item_kzxg_plan
   *  筛选条件
   */
  @Get('qyStsProjects')
  async qyStsProjects(@Query() query: QueryStsProject) {
    try {
      const res = await this.qykService.qyStsProjects(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }
  /**
   * 体检项目汇总表获取
   *  筛选条件
   */
  @Post('qyHealthCheckList')
  async qyHealthCheckList(@Query() query: any) {
    try {
      // 造点汇总表数据
      const res = await this.qykService.qyHealthCheckList(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }
  /**
   * 体检人员获取 tj_health_check tj_health_check_jl
   *  筛选条件
   */
  @Post('qySuspects')
  async qySuspects(@Query() query: any) {
    try {
      const res = await this.qykService.qySuspects(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }
  /**
   * 职业病诊断与鉴定获取 wj_occupational_disease
   *  筛选条件
   */
  @Post('qyOccupationalDisease')
  async qyOccupationalDisease(@Query() query: any) {
    try {
      console.log(111, query);
      const res = await this.qykService.qyOccupationalDisease();
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 危害申报列表 ng_declaration
   *
   */
  @Post('qyOnlineDeclarationList')
  async qyOnlineDeclarationList(@Body() body: QueryOnlineDeclaration) {
    try {
      const { docs, count } =
        await this.qyOnlineDeclarationService.qyOnlineDeclarationList(body);
      const data = docs.map((e) => {
        const item = {};
        Object.keys(e).forEach((key) => {
          if (key.startsWith('ng_declaration_')) {
            item[key.replace('ng_declaration_', '')] = e[key];
          } else {
            item[key] = e[key];
          }
        });
        return item;
      });
      return success(
        {
          docs: data,
          count,
        },
        '查询成功',
      );
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 危害申报获取 ng_declaration
   *
   */
  @Get('qyOnlineDeclaration')
  async getQyOnlineDeclaration(@Query() query: any) {
    try {
      const data =
        await this.qyOnlineDeclarationService.getOnlineDeclaration(query);

      const auditData = await this.qyOnlineDeclarationService.getByIdNgAudit(
        data.last_audit_id,
      );

      // 获取企业经营状态
      const res = await this.qyOnlineDeclarationService.qyOnlineDeclarationEmployer({unified_code:data.unified_code})
      return success(
        {
          data,
          auditData,
          employerStatus:(res && res.employerStatus) || (res && res.employerStatus == 0) ? res.employerStatus : ''
        },
        '查询成功',
      );
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 危害申报保存 ng_declaration
   *
   */
  @Post('qyOnlineDeclaration')
  async qyOnlineDeclaration(@Body() body: QueryOnlineDeclaration) {
    try {
      // 查询是否存在企业是否存在
      const employeer = await this.qyOnlineDeclarationService.findEmployer({
        unified_code: body.unified_code
      })
      if (!employeer) {
        await this.qyOnlineDeclarationService.saveEmployer({
          unifiedCode: body.unified_code,
          employerName: body.employer_name,
        })
      }
      
      const res =
        await this.qyOnlineDeclarationService.saveOnlineDeclaration(body);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 危害申报更新 ng_declaration
   *
   */
  @Put('qyOnlineDeclaration')
  async updateQyOnlineDeclaration(@Body() body: any) {
    try {
      const res =
        await this.qyOnlineDeclarationService.updateOnlineDeclaration(body);

      if (body.declarationState === 1) {
        // 创建审核表
        await this.qyOnlineDeclarationService.createNgAudit(body);
      }
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 危害申报保存 ng_declaration
   *
   */
  @Delete('qyOnlineDeclaration')
  async delOnlineDeclaration(@Body() body: any) {
    try {
      const { id } = body;
      const res =
        await this.qyOnlineDeclarationService.delByIdOnlineDeclaration(id);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 主要产品获取 ng_product
   *
   */
  @Get('qyProduct')
  async getQyProduct(@Query() query: any) {
    try {
      const res = await this.qyOnlineDeclarationService.getQyProduct(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 主要产品更新 ng_product
   *
   */
  @Post('qyProduct')
  async updateQyProduct(@Body() body: any) {
    try {
      const res = await this.qyOnlineDeclarationService.updateQyProduct(body);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 主要产品删除 ng_product
   *
   */
  @Delete('qyProduct')
  async deleteQyProduct(@Query() query: any) {
    try {
      const res = await this.qyOnlineDeclarationService.deleteQyProduct(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 企业经济类型 dim_economic
   *
   */
  @Get('qyEconomicTypes')
  async qyEconomicTypes() {
    try {
      const res = await this.qyOnlineDeclarationService.getQyEconomicTypes();
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 企业行业分类 dim_employer_industry
   *
   */
  @Get('qyIndustryCategorys')
  async qyIndustryCategorys() {
    try {
      const res =
        await this.qyOnlineDeclarationService.getQyIndustryCategorys();
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 危害申报用人单位花名册 ng_roster
   *
   */
  @Post('qyNgRoster')
  async updateQyNgRoster(@Body() body: any) {
    try {
      // 新增数据
      const res = await this.qyOnlineDeclarationService.updateQyNgRoster(body);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 危害申报用人单位花名册 ng_roster
   *
   */
  @Delete('qyNgRoster')
  async delQyNgRoster(@Query() query: any) {
    try {
      // 新增数据
      const res = await this.qyOnlineDeclarationService.delQyNgRoster(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 危害申报用人单位花名册查询列表 ng_roster
   *
   */
  @Post('qyNgRosterList')
  async getQyNgRosterList(@Body() body: any) {
    try {
      const res = await this.qyOnlineDeclarationService.getQyNgRosterList(body);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  @Get('lastRosterToNow')
  async handleLastRosterToNow(@Query() params: any) {
    try {
      const res = await this.qyOnlineDeclarationService.handleLastRosterToNow(params as { lastDataId: string, id: string });
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 更新用人单位危害因素接害统计 ng_hazard_exposed
   *
   */
  @Post('qyHazardExposed')
  async updateHazardExposed(@Body() body: any) {
    try {
      const { id, formData } = body;

      // 查询旧数据
      const oldData = await this.qyOnlineDeclarationService.getHazardExposed({
        id,
      });
      // 对比，找出需要删除的
      const needDelList = oldData
        .filter(
          (e) => !formData.find((n) => n.hazard_factor === e.hazard_factor),
        )
        .map((e) => e.id);
      if (needDelList && needDelList.length) {
        await this.qyOnlineDeclarationService.deleteHazardExposedByIds(
          needDelList,
        );
      }

      const res =
        await this.qyOnlineDeclarationService.updateHazardExposed(body);

      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 获取用人单位危害因素接害统计 ng_hazard_exposed
   *
   */
  @Get('qyHazardExposed')
  async getHazardExposed(@Query() query: any) {
    try {
      const res = await this.qyOnlineDeclarationService.getHazardExposed(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 更新用人单位危害因素检测统计 ng_hazard_detect
   *
   */
  @Post('qyHazardDetect')
  async updateHazardDetect(@Body() body: any) {
    try {
      const { id, formData } = body;

      // 查询旧数据
      const oldData = await this.qyOnlineDeclarationService.getHazardDetect({
        id,
      });
      // 对比，找出需要删除的
      const needDelList = oldData
        .filter(
          (e) => !formData.find((n) => n.hazard_factor === e.hazard_factor),
        )
        .map((e) => e.id);
      if (needDelList && needDelList.length) {
        await this.qyOnlineDeclarationService.deleteHazardDetectByIds(
          needDelList,
        );
      }

      const res =
        await this.qyOnlineDeclarationService.updateHazardDetect(body);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 获取用人单位危害因素检测统计 ng_hazard_detect
   *
   */
  @Get('qyHazardDetect')
  async getHazardDetect(@Query() query: any) {
    try {
      const res = await this.qyOnlineDeclarationService.getHazardDetect(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 更新用人单位危害因素体检统计 ng_hazard_exam
   *
   */
  @Post('qyHazardExam')
  async updateHazardExam(@Body() body: any) {
    try {
      const { id, formData } = body;

      // 查询旧数据
      const oldData = await this.qyOnlineDeclarationService.getHazardExam({
        id,
      });
      // 对比，找出需要删除的
      const needDelList = oldData
        .filter(
          (e) => !formData.find((n) => n.hazard_factor === e.hazard_factor),
        )
        .map((e) => e.id);
      if (needDelList && needDelList.length) {
        await this.qyOnlineDeclarationService.deleteHazardExamByIds(
          needDelList,
        );
      }

      const res = await this.qyOnlineDeclarationService.updateHazardExam(body);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 获取用人单位危害因素体检统计 ng_hazard_exam
   *
   */
  @Get('qyHazardExam')
  async getHazardExam(@Query() query: any) {
    try {
      const res = await this.qyOnlineDeclarationService.getHazardExam(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  // 获取企业最近的一次申报大于11个月的数据
  @Get('declarationList')
  async getDeclarationList() {
    try {
      const res = await this.employerService.getLatestDeclarations();
      return success(res, '申报列表查询成功');
    } catch (err) {
      this.logger.error('申报列表查询失败: ' + err.message);
      return error(err.message);
    }
  }

  // 监管 - 申报查询 xxn
  @Get('declarationStatistics')
  async getDeclarationStatistics(@Query() query: any) {
    try {
      const res = await this.employerService.getDeclarationStatistics(query);
      return success(res, '申报查询成功');
    } catch (err) {
      console.log(44444, err);
      return error(err.message);
    }
  }
  /**
   * 获取用人单位危害因素
   *
   */
  @Post('qyHarmFactors')
  async getHarmFactors(@Body() query: any) {
    try {
      const res = await this.qyOnlineDeclarationService.getHarmFactors(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }
  /**
   * 定期检测 tj_technicalquality_item
   *  查询条件
   */
  @Get('getPeriodicTestList')
  async getPeriodicTestList(@Query() query: QueryQykDto) {
    try {
      const res = await this.qykService.getPeriodicTestList(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }
  /**
   * 现状评价 tj_technicalquality_item
   *  查询条件
   */
  @Get('getcurrentEvaluationList')
  async getcurrentEvaluationList(@Query() query: QueryQykDto) {
    try {
      const res = await this.qykService.getcurrentEvaluationList(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  // 监管端接口
  // 通过申报审核
  @Post('jgOnlineDeclarationRecords')
  async jgOnlineDeclarationRecords(@Body() body: any) {
    try {
      const res =
        await this.qyOnlineDeclarationService.agreeJgOnlineDeclaration(body);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 获取职业健康培训列表 ng_roster
   *
   */
  @Get('getHealthTraintList')
  async getHealthTraintList(@Query() query: any) {
    try {
      const res =
        await this.qyOnlineDeclarationService.getHealthTraintList(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  @Get('qyOnlineDeclarationLast')
  async qyOnlineDeclarationLast(@Query() query: any) {
    try {
      const res =
        await this.qyOnlineDeclarationService.qyOnlineDeclarationLast(query);

      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 获取职业健康培训人员详细列表 ng_roster
   *
   */
  @Get('getHealthTraintListDetail')
  async getHealthTraintListDetail(@Query() query: any) {
    try {
      const res =
        await this.qyOnlineDeclarationService.getHealthTraintListDetail(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  // 获取填报人的district_id
  @Get('getDistrictId')
  async getDistrictId() {
    try {
      // const res = await this.qyOnlineDeclarationService.getDistrictId(query);
      const data = await this.qyOnlineDeclarationService.getMaxReceiptNum();
      return success(
        {
          maxOrderCode: data,
        },
        '查询成功',
      );
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  @Get('qyOnlineDeclarationEmployer')
  async qyOnlineDeclarationEmployer(@Query() query: any) {
    try {
      const res =
        await this.qyOnlineDeclarationService.qyOnlineDeclarationEmployer(
          query,
        );

      console.log(444, res);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  // 保存用人单位数据 NgEmployer
  @Post('qyOnlineDeclarationEmployer')
  async updateEmployer(@Body() data: any) {
    try {
      const { unified_code, updateParams } = data
      const res =
        await this.qyOnlineDeclarationService.updateEmployer({ unified_code, updateParams });
      return success({}, '更新成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 获取职业病诊断与鉴定列表 wj_occupational_disease
   *
   */
  @Get('getoccupationalDiseasetList')
  async getoccupationalDiseasetList(@Query() query: any) {
    try {
      const res = await this.qykService.getoccupationalDiseasetList(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  // 存储回执编号
  @Post('saveReceiptNum')
  async saveReceiptNum(@Body() body: any) {
    try {
      const res = await this.qyOnlineDeclarationService.saveReceiptNum(body);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 获取职业病诊断人数列表 wj_occupational_disease
   *
   */
  @Get('getDiagnosisList')
  async getDiagnosisList(@Query() query: any) {
    try {
      const res = await this.qykService.getDiagnosisList(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  // 驳回申报审核
  @Post('jgOnlineRefuseDeclarationRecords')
  async jgOnlineRefuseDeclarationRecords(@Body() body: any) {
    try {
      const res =
        await this.qyOnlineDeclarationService.updateJgOnlineDeclarationRefuseRecords(
          body,
        );
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 获取职业病确诊人数列表 wj_occupational_disease
   *
   */
  @Get('getDiagnosisTrueList')
  async getDiagnosisTrueList(@Query() query: any) {
    try {
      const res = await this.qykService.getDiagnosisTrueList(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  // 获取驳回原因
  @Get('getRejectReason')
  async getRejectReason(@Query() query: any) {
    try {
      const res = await this.qyOnlineDeclarationService.getRejectReason(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  // 获取申报详情basicInfo
  @Post('getDeclarationDetailBasicInfo')
  async getDeclarationDetailBasicInfo(@Body() body: any) {
    try {
      const res =
        await this.qyOnlineDeclarationService.getDeclarationDetailBasicInfo(
          body,
        );
      console.log(res, '=========简化数据结构');
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 获取职业病鉴定市级人数列表 wj_occupational_disease
   *
   */
  @Get('getAppraisalCityList')
  async getAppraisalCityList(@Query() query: any) {
    try {
      const res = await this.qykService.getAppraisalCityList(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  @Get('fetchDeclarationStatus')
  async fetchDeclarationStatus(@Query() query: any) {
    try {
      const res =
        await this.qyOnlineDeclarationService.fetchDeclarationStatus(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 获取职业病鉴定省级人数列表 wj_occupational_disease
   *
   */
  @Get('getAppraisalProvinceList')
  async getAppraisalProvinceList(@Query() query: any) {
    try {
      const res = await this.qykService.getAppraisalProvinceList(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  @Get('getAuditReceiptUrl')
  async getAuditReceiptUrl(@Query() query: any) {
    try {
      const res =
        await this.qyOnlineDeclarationService.getAuditReceiptUrl(query);
      return success(res, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  // 监管端获取申报列表
  @Post('jgOnlineDeclarationList')
  async jgOnlineDeclarationList(@Body() body: any) {
    try {
      const { docs, count } =
        await this.qyOnlineDeclarationService.jgOnlineDeclarationList(body);
      const data = docs.map((e) => {
        const item = {};
        Object.keys(e).forEach((key) => {
          if (key.startsWith('ng_declaration_')) {
            item[key.replace('ng_declaration_', '')] = e[key];
          } else {
            item[key] = e[key];
          }
        });
        return item;
      });
      return success(
        {
          docs: data,
          count,
          // declarationStatistics,
        },
        '查询成功',
      );
    } catch (err) {
      this.logger.error('申报列表查询失败: ' + err.message);
      return error(err.message);
    }
  }

  @Post('qyOnlineDeclarationJcProject')
  async qyOnlineDeclarationJcProject(@Body() data: any) {
    try {
      const harmFactors = await this.qyOnlineDeclarationService.getHarmFactors(
        {},
      );

      const res =
        await this.qyOnlineDeclarationService.qyOnlineDeclarationJcProject(
          data,
        );
      const newData: any = [];
      res.forEach((e) => {
        const JCJG = e.JCJG.split('::');
        const JCJG_EXCEED = e.JCJG_EXCEED.split('::');
        const JCJG_JCD = e.JCJG_JCD.split('::');
        const JCJG_WHYS = e.JCJG_WHYS.split('::');

        const result = [];
        for (let i = 0; i < JCJG_WHYS.length; i++) {
          result.push({
            whyss: JCJG_WHYS[i].split('、'),
            whys: JCJG_WHYS[i],
            jcd: JCJG_JCD[i],
            exceed: JCJG_EXCEED[i],
            value: JCJG[i],
          });
        }

        const detect_summary_map = {
          1: 'Dust',
          2: 'Chemica',
          3: 'Physical',
          4: 'Radiation',
          5: 'BioOther',
          6: 'BioOther',
        };
        const detect_summary = {
          totalDust: 0,
          overDust: 0,

          totalChemica: 0,
          overChemica: 0,

          totalPhysical: 0,
          overPhysical: 0,

          totalRadiation: 0,
          overRadiation: 0,

          totalBioOther: 0,
          overBioOther: 0,
        };
        const detect_info = {};
        for (let i = 0; i < result.length; i++) {
          const item = result[i];

          for (let j = 0; j < item.whyss.length; j++) {
            const whys = item.whyss[j]
              .replace('（总尘）', '')
              .replace('（呼尘）', '')
              .replace('-总尘', '')
              .replace('-呼尘', '');

            if (!detect_info[whys]) {
              let targetHarmFactor = harmFactors.find((e) => {
                if (e.hazard_name === whys || e.hazard_name_employer === whys) {
                  return true;
                }
              });

              if (!targetHarmFactor) {
                // 再次模糊搜索
                const targetHarmFactor_vague = harmFactors.find((e) => {
                  if (e.hazard_name_employer.includes(whys)) {
                    return true;
                  }
                });

                if (
                  !targetHarmFactor_vague ||
                  !targetHarmFactor_vague.hazard_code_employer
                ) {
                  continue;
                }

                targetHarmFactor = targetHarmFactor_vague;
              } else if (!targetHarmFactor.hazard_code_employer) {
                continue;
              }

              detect_info[whys] = {
                hazard_type: targetHarmFactor
                  ? targetHarmFactor.hazard_dec_type
                  : '', // 大类
                hazard_code: targetHarmFactor
                  ? targetHarmFactor.hazard_code_employer
                  : '', // 危害因素编码
                whys: targetHarmFactor.hazard_name_employer,
                points: [],
                pointNumber: 0,
                exceedNumber: 0,
              };
            }
            if (!detect_info[whys].points.includes(item.jcd)) {
              detect_info[whys].pointNumber += 1;
              if (item.exceed === '1') {
                detect_info[whys].exceedNumber += 1;
              }
              detect_info[whys].points.push(item.jcd);
            } else {
              if (item.exceed === '1') {
                detect_info[whys].exceedNumber += 1;
              }
            }
          }
        }

        const detect_harmFactors = [];
        Object.keys(detect_info).forEach((key) => {
          const item = detect_info[key];
          detect_harmFactors.push(item);

          const mapKey = detect_summary_map[item.hazard_type];
          if (mapKey) {
            detect_summary['total' + mapKey] += item.pointNumber;
            detect_summary['over' + mapKey] += item.exceedNumber;
          }
        });

        newData.push({
          ...e,
          detect_harmFactors,
          detect_summary,
        });
      });

      return success(newData, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 获取体检机构和检测机构 tj_technicalquality_company tj_health_company
   *
   */
  @Get('tjAndJcOrgs')
  async tjAndJcOrgs() {
    try {
      const tjHealthCompany =
        await this.qyOnlineDeclarationService.getTjHealthCompany();
      const tjTechnicalqualityCompany =
        await this.qyOnlineDeclarationService.getTjTechnicalqualityCompany();
      return success(
        {
          tjHealthCompany,
          tjTechnicalqualityCompany,
        },
        '查询成功',
      );
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  /**
   * 获取监管单位 ng_supervisor
   *
   */
  @Get('supervisorList')
  async supervisorList() {
    try {
      const supervisorList =
        await this.qyOnlineDeclarationService.supervisorList();
      return success(supervisorList, '查询成功');
    } catch (err) {
      this.logger.error('查询失败: ' + err.message);
      return error(err.message);
    }
  }

  // 获取签发单位
  @Get('getSignGroupName')
  async getSignGroupName(@Query() query: any) {
    try {
      const res = await this.qyOnlineDeclarationService.getSignGroupName(query);
      return success(res, '查询成功');
    } catch (err) {
      return error(err.message);
    }
  }

  @Get('getReceiptPdfData')
  async getReceiptPdfData(@Query() query:any){
    try {
      const res = await this.qyOnlineDeclarationService.getMaxReceiptNum()
      return success(res)
    } catch (err) {
      return error(err.message);
    }
  }

  @Get('jgOnlineDeclarationStatistics')
  async jgOnlineDeclarationStatistics(@Query() query:any){
    try {
      const res = await this.qyOnlineDeclarationService.jgOnlineDeclarationStatistics(query)
      return success(res)
    } catch (err) {
      return error(err.message);
    }
  }

 @Get('getHasAuditData')
 async getHasAuditData(@Query() query:any){
  try {
    const res = await this.qyOnlineDeclarationService.getHasAuditData(query)
    return success(res)
  } catch (error) {
    return error(error.message);
  }
 }

 @Get('getOnlineDeclarationInfo')
 async getOnlineDeclarationInfo(@Query() query:any){
  try {
    const res = await this.qyOnlineDeclarationService.getOnlineDeclarationInfo(query)
    return success(res)
  } catch (err) {
    return error(err.message);
  }
 }
}
