import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Approval } from './approval.entity';
import { Todo } from '../../todo/mysql/todo.entity';

@Entity('approval_todo')
export class ApprovalTodo {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'approval_id' })
  approvalId: number;

  @Column({ name: 'todo_id' })
  todoId: number;

  @ManyToOne(() => Approval, approval => approval.todos)
  @JoinColumn({ name: 'approval_id' })
  approval: Approval;

  @ManyToOne(() => Todo)
  @JoinColumn({ name: 'todo_id' })
  todo: Todo;
}
