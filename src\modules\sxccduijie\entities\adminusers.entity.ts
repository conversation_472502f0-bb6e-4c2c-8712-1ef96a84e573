import { Entity, ObjectIdColumn, Column, BeforeInsert } from 'typeorm';
import * as shortid from 'shortid';

@Entity('adminusers')
export class Adminusers {
  @ObjectIdColumn()
  _id: string;

  @Column({ type: 'varchar', default: '', comment: '编码' })
  unitCode: string;

  @Column('varchar', { comment: '企业id' })
  EnterpriseID: string;

  @Column('varchar', { comment: '姓名' })
  name: string; // 姓名

  @Column('varchar', { comment: '用户名' })
  userName: string; // 用户名

  @Column('varchar', { comment: '角色' })
  group: string; // 角色id

  @Column({ type: 'string' })
  newAddEnterpriseID: string;

  @Column()
  password: string;

  @BeforeInsert()
  setDefaultId() {
    if (!this._id) {
      this._id = shortid.generate();
    }
  }
}
