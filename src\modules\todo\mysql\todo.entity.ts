import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne, OneToOne, JoinColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { TodoType } from './todo-type.entity';
import { TodoMeta } from './todo-meta.entity';

@Entity('todo')
export class Todo {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 100, name: 'type_code' })
  typeCode: string;

  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ 
    type: 'tinyint',
    nullable: true,
    comment: '状态：0-未读，1-已读'
  })
  status: number;

  @Column({ type: 'varchar', length: 255, name: 'user_id' })
  userId: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ManyToOne(() => TodoType, type => type.todos)
  @JoinColumn({ name: 'type_code', referencedColumnName: 'code' })
  type: TodoType;

  @OneToOne(() => TodoMeta, meta => meta.todo)
  meta: TodoMeta;
}
