import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ExpertWork } from './mysql/expert_work.entity';
import { ExpertService } from './expert.service';
import * as moment from 'moment';
import { Expert } from './mysql/expert.entity';

@Injectable()
export class ExpertWorkService {
  constructor(
    @InjectRepository(ExpertWork, 'mysqlConnectionIservice')
    private readonly expertWorkRepository: Repository<ExpertWork>,
    private readonly expertService: ExpertService,
  ) {}

  // 新增专家工作任务
  async create(work: Partial<ExpertWork>): Promise<ExpertWork> {
    if (work.completion_time) {
      work.completion_time = new Date(work.completion_time);
    }
    if (typeof work.publisher_id === 'number') {
      work.publisher_id = work.publisher_id + '';
    }
    const newWork = this.expertWorkRepository.create(work);
    return this.expertWorkRepository.save(newWork);
  }

  // 删除专家工作任务
  async delete(id: number): Promise<void> {
    const result = await this.expertWorkRepository.delete(id);
    if (result.affected === 0) {
      throw new HttpException(
        '删除失败，未找到对应的记录',
        HttpStatus.NOT_FOUND,
      );
    }
  }

  // 更新专家工作任务
  async update(
    id: number,
    updateData: Partial<ExpertWork>,
  ): Promise<ExpertWork> {
    const allowedFields = ['status', 'work_report'];
    const updateFields = Object.keys(updateData).filter((key) =>
      allowedFields.includes(key),
    );
    const updatePayload: Partial<ExpertWork> = updateFields.reduce(
      (obj, key) => {
        obj[key] = updateData[key];
        return obj;
      },
      {},
    );

    if (updatePayload.work_report) {
      updatePayload.report_time = new Date();
    } else {
      delete updatePayload.work_report;
    }
    if (updatePayload.status) {
      updatePayload.status = +updatePayload.status;
    } else {
      delete updatePayload.status;
    }

    await this.expertWorkRepository.update(id, updatePayload);
    const updatedWork = await this.expertWorkRepository.findOne({
      where: { id },
    });
    if (!updatedWork) {
      throw new HttpException(
        '更新失败，未找到对应的记录',
        HttpStatus.NOT_FOUND,
      );
    }
    return updatedWork;
  }

  // 查询专家工作任务（分页及条件查询）
  async findAll(
    query: {
      pageSize?: number | string;
      curPage?: number | string;
      keyWord?: string;
      status?: number;
      expert_id?: number;
      publisher_id?: string | number;
    } = {},
  ): Promise<{ total: number; list: any[] }> {
    const limit = Number(query.pageSize) || 10;
    const curPage = Number(query.curPage) || 1;

    const queryBuilder = this.expertWorkRepository
      .createQueryBuilder('work')
      .leftJoinAndMapOne(
        'work.expert',
        Expert,
        'expert',
        'work.expert_id = expert.id',
      )
      .select(['work', 'expert.name', 'expert.level']);

    if (query.status + '') {
      queryBuilder.andWhere('work.status = :status', { status: query.status });
    }

    if (query.expert_id) {
      queryBuilder.andWhere('work.expert_id = :expert_id', {
        expert_id: query.expert_id,
      });
    }
    if (query.publisher_id) {
      queryBuilder.andWhere('work.publisher_id = :publisher_id', {
        publisher_id: query.publisher_id,
      });
    }

    if (query.keyWord) {
      queryBuilder.andWhere(
        `(
          work.management_element LIKE :keyWord 
          OR work.work_item LIKE :keyWord 
          OR work.publisher_name LIKE :keyWord 
          OR work.item_requirements LIKE :keyWord 
          OR expert.name LIKE :keyWord
        )`,
        {
          keyWord: `%${query.keyWord}%`,
        },
      );
    }

    const [list, total] = await queryBuilder
      .skip((curPage - 1) * limit)
      .take(limit)
      .getManyAndCount();

    // 日期格式化
    const formatList = list.map((work) => ({
      ...work,
      completion_time: work.completion_time
        ? moment(work.completion_time).format('YYYY-MM-DD HH:mm')
        : '',
      report_time: work.report_time
        ? moment(work.report_time).format('YYYY-MM-DD HH:mm')
        : '',
      created_at: moment(work.created_at).format('YYYY-MM-DD HH:mm'),
      updated_at: moment(work.updated_at).format('YYYY-MM-DD HH:mm'),
    }));
    return { total, list: formatList };
  }

  // 查询单个专家工作任务
  async findOne(id: number): Promise<ExpertWork> {
    const work = await this.expertWorkRepository.findOne({
      where: { id },
    });
    if (!work) {
      throw new HttpException('未找到对应的id', HttpStatus.NOT_FOUND);
    }
    return work;
  }
}
