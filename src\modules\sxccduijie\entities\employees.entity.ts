import {
  <PERSON>tity,
  ObjectIdColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
  ManyToOne,
  ManyToMany,
  JoinTable,
} from 'typeorm';
// import { User } from './users.entity';
import { Dingtrees } from './dingtrees.entity';
import { Adminorgs } from '../../adminorgs/adminorgs.entity';

@Entity('employees')
// @Index('index_departs', ['departs'])
// @Index('index_name', ['name'])
// @Index('index_phoneNum', ['phoneNum'], { unique: true })
// @Index('index_userName', ['userName'], { unique: true })
export class Employees {
  @ObjectIdColumn()
  _id: string;

  @Column({ type: 'varchar', default: '' })
  unitCode: string;

  @Column({ type: 'varchar' })
  IDNum: string;

  // 年龄
  @Column({ type: 'int' })
  age: number;

  // 工龄
  @Column({ type: 'varchar' })
  workYears: string;

  @Column({ type: 'varchar', default: 'iservice2' })
  source: string;

  @Column({ type: 'boolean', default: false })
  hasRecocrd: boolean;

  @ManyToMany(() => Dingtrees)
  @JoinTable()
  departs: Dingtrees[];

  @ManyToOne(() => Adminorgs)
  @JoinColumn()
  EnterpriseID: Adminorgs;

  @Column({ type: 'int', default: 1 })
  status: number;

  // 文化程度
  @Column({ type: 'varchar' })
  education: string;

  @Column()
  userId: string;

  // 员工性别
  @Column({ type: 'varchar' })
  gender: string;

  // 手机号
  @Column({ type: 'varchar' })
  phoneNum: string;

  // 开始工作日期
  @Column({ type: 'date' })
  workStart: Date;

  // 工种
  @Column({ type: 'varchar' })
  workType: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar' })
  hobby: String; // 嗜好
  
  @Column({ type: 'varchar' })
  marriage: String; // 婚姻

  @Column({ type: 'varchar' })
  nativePlace: String; // 籍贯

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
