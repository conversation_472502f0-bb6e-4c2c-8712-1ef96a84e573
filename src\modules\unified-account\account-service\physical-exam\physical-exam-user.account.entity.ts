import { <PERSON><PERSON><PERSON>, Column, CreateDateColumn, ObjectIdColumn } from 'typeorm';
import {
  IsEmail,
  IsOptional,
  IsString,
  IsBoolean,
  IsNumber,
  ValidateIf,
  Min,
} from 'class-validator';
import { nanoid } from 'nanoid';

@Entity('physicalExamUsers')
export class PhysicalExamUser {
  @ObjectIdColumn()
  _id?: string;

  @Column({ default: '' })
  @IsString()
  userName: string;

  @Column({ default: '' })
  @IsString()
  name: string;

  @Column({ nullable: true })
  @IsString()
  @ValidateIf((o) => o.phoneNum !== null && o.phoneNum !== undefined)
  phoneNum: string;

  @Column({ default: '' })
  @IsEmail()
  email: string;

  @Column()
  @IsString()
  password: string;

  @Column({ nullable: true })
  @IsOptional()
  passwordExpiresAt: Date;

  @Column({ default: 0 })
  @IsNumber()
  @Min(0)
  loginAttempts: number;

  @Column()
  loginAttemptsTimestamp: Date[];

  @Column({ default: '' })
  @IsString()
  org_id: string;

  @Column({ default: '' })
  @IsString()
  department_id: string;

  @Column({ default: '' })
  @IsString()
  department: string;

  @Column({ default: '' })
  @IsString()
  group: string;

  @Column({ default: '86' })
  @IsString()
  countryCode: string;

  @CreateDateColumn()
  ctime: Date;

  @Column({ default: '/static/upload/images/defaultlogo.png' })
  @IsString()
  logo: string;

  @Column({ default: true })
  @IsBoolean()
  state: boolean;

  @Column({ default: true })
  @IsBoolean()
  enable: boolean;

  @Column({ nullable: true })
  @IsOptional()
  @IsString()
  comments: string;

  constructor() {
    this._id = nanoid();
    this.loginAttempts = 0;
    this.logo = '/static/upload/images/defaultlogo.png';
    this.countryCode = '86';
    this.enable = this.enable === false ? false : true;
    this.state = this.state === false ? false : true;
  }
}
