import { Entity, Column, PrimaryColumn } from 'typeorm';

@Entity('tj_qyk_new')
export class OdsEntTjQykNew {
  @PrimaryColumn({ type: 'varchar', length: 64, comment: 'id' })
  id: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '所属区划id',
  })
  districtId: string;

  @Column({
    type: 'varchar',
    length: 128,
    nullable: true,
    comment: '所属地区名称',
  })
  ssdqmc: string;

  @Column({ type: 'varchar', length: 128, nullable: true, comment: '机构名称' })
  name: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '社会统一信用代码',
    name: 'unified_code',
  })
  unifiedCode: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '上报类型' })
  sblx: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '申报日期' })
  sbrq: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '行业类别' })
  hylb: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '经济类型' })
  jjlx: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '法人' })
  fr: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '审核状态' })
  shzt: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '申报年份' })
  sbnf: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '机构注册地址',
  })
  jgzcdz: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '工作场所地址',
  })
  gzcsdz: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '企业规模0-大；1-中；2-小；3-微',
  })
  qygm: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '填报人' })
  tbr: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '联系人' })
  lxr: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '总工作人数',
  })
  zgzrs: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '外委人员总数',
  })
  wwryzs: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '接害总人数',
  })
  jhzrs: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '职业病累计人数',
  })
  zybljrs: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '回执编号' })
  hzbh: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '是否军工等涉密产品',
  })
  sfjgdsmcp: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '有无粉尘_危害因素',
  })
  fcwhysYw: string;

  @Column({
    type: 'varchar',
    length: 11,
    nullable: true,
    comment: '接尘总人数',
  })
  fcwhysNum: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '有无化学物质_危害因素',
  })
  hxwzwhysYw: string;

  @Column({
    type: 'varchar',
    length: 11,
    nullable: true,
    comment: '化学物质_接触总人数',
  })
  hxwzwhysNum: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '有无物理因素_危害因素',
  })
  wlwhysYw: string;

  @Column({
    type: 'varchar',
    length: 11,
    nullable: true,
    comment: '物理因素_接触总人数',
  })
  wlwhysNum: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '有无放射性因素_危害因素',
  })
  fswhysYw: string;

  @Column({
    type: 'varchar',
    length: 11,
    nullable: true,
    comment: '放射性因素_接触总人数',
  })
  fswhysNum: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '有无生物因素_危害因素',
  })
  swwhysYw: string;

  @Column({
    type: 'varchar',
    length: 11,
    nullable: true,
    comment: '生物因素_接触总人数',
  })
  swwhysNum: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '有无其他因素_危害因素',
  })
  qtwhysYw: string;

  @Column({
    type: 'varchar',
    length: 11,
    nullable: true,
    comment: '其他因素_接触总人数',
  })
  qtwhysNum: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '行业类别代码',
  })
  hylbdm: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '经济类型代码',
  })
  jjlxdm: string;

  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
    comment: '是否是严重类型行业1是0否',
  })
  hylx: string;

  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
    comment: '来源于普瑞的企业00与申报关联不上11与申报系统关联上',
  })
  purui: string;

  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
    comment: '来源于网星的企业00与申报关联不上11与申报系统关联上',
  })
  wangxing: string;

  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
    comment: '监管情况00无11有',
  })
  sbzt: string;

  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
    comment: '三同时,非null代表已做',
  })
  sts: string;

  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
    comment: '定期检测,非null代表已做',
  })
  dqjc: string;

  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
    comment: '现状评价,非null代表已做',
  })
  xzpj: string;
}
