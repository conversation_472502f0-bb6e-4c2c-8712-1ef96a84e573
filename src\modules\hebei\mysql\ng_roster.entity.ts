import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  Join<PERSON>olumn,
} from 'typeorm';
import { ngDeclaration } from './ng_declaration.entity';

// 用人单位花名册
@Entity('ng_roster')
export class ngRoster {
  @PrimaryGeneratedColumn({ type: 'int', comment: 'roster_id' })
  id: number;

  @Column({ type: 'int', nullable: true, comment: '申报表id' })
  declaration_id: number | null;

  @Column({ type: 'varchar', length: 50, nullable: true, comment: '姓名' })
  emp_name: string | null;

  @Column({ type: 'varchar', length: 50, nullable: true, comment: '所属部门' })
  dept: string | null;

  @Column({ type: 'varchar', length: 18, nullable: true, comment: '身份证号' })
  id_card_no: string | null;

  @Column({ type: 'datetime', nullable: true, comment: '培训时间' })
  train_time: Date | null;

  @Column({ type: 'varchar', length: 50, nullable: true, comment: '培训类型' })
  train_type: string | null;

  @Column({ type: 'varchar', length: 50, nullable: true, comment: '培训方式' })
  train_method: string | null;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '委培培训机构名称',
  })
  comm_train_inst: string | null;

  @Column({ type: 'varchar', length: 50, nullable: true, comment: '人员类别' })
  category: string | null;

  @ManyToOne(() => ngDeclaration)
  @JoinColumn({ name: 'declaration_id' })
  declaration: ngDeclaration;
}
