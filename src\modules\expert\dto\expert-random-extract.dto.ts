import {
  IsOptional,
  IsString,
  IsEnum,
  IsInt,
  Length,
  Min,
  IsArray,
} from 'class-validator';

export enum Education {
  HIGH = 3,
  ASSOCIATE = 4,
  BACHELOR = 5,
  MASTER = 6,
  DOCTOR = 7,
}

export enum Gender {
  FEMALE = 'F',
  MALE = 'M',
}

export class RandomExtractDto {
  @IsInt()
  @Min(1)
  quantity: number;

  @IsOptional()
  @IsArray()
  @IsString({ each: true }) // 确保数组中的每个元素都是字符串
  @Length(9, 9, { each: true }) // 确保数组中的每个字符串长度为 9
  // @ArrayMinSize(1) // 至少包含 1 个元素
  area_code?: string[];
  
  @IsOptional()
  @IsArray()
  @IsInt({ each: true }) // 确保数组中的每个元素都是整数
  exclude_expert_id?:number[];

  @IsOptional()
  @IsArray()
  @IsInt({ each: true }) // 确保数组中的每个元素都是整数
  // @ArrayMinSize(1) // 至少包含 1 个元素
  category_id?: number[];

  @IsOptional()
  @IsEnum(Education)
  education?: Education;

  @IsOptional()
  @IsEnum(Gender)
  gender?: Gender;

  @IsOptional()
  @IsString()
  keyWord?: string;

  @IsOptional()
  @IsEnum([1, 2, 3])
  level?: 1 | 2 | 3;

  @IsString()
  management_element: string;

  @IsString()
  work_item: string;

  @IsOptional()
  @IsString()
  item_requirements?: string;

  @IsOptional()
  @IsEnum(['0', '1'])
  first_type?: '0' | '1';

}
