import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { ngRoster } from './mysql/ng_roster.entity';
import { DataSource } from 'typeorm';
import { groupBy } from 'lodash';
import { ngDeclaration } from './mysql/ng_declaration.entity';

@Injectable()
export class RosterService {
  private readonly logger = new Logger(RosterService.name);
  constructor(
    @InjectDataSource('mysqlConnectionHebei')
    private readonly dataSource: DataSource,
  ) {}

  async getRosterTrainRates(rosters: ngRoster[]): Promise<
    {
      category: string;
      totalCount: number;
      trainedCount: number;
      trainingRate: number;
    }[]
  > {
    // 按人员类别分组
    const groupedRoster: Record<string, ngRoster[]> = groupBy(
      rosters,
      (item) => item.category || '未分类',
    );

    // 计算每个分类的培训统计
    const trainingStats = Object.entries(groupedRoster).map(
      ([category, items]) => {
        // 总人数
        const totalCount = items.length;

        // 已培训人数（train_time 不为 null）
        const trainedCount = items.filter((item) => item.train_time).length;

        // 培训率
        const trainingRate =
          totalCount > 0
            ? Number(((trainedCount / totalCount) * 100).toFixed(2))
            : 0;

        return {
          category,
          totalCount,
          trainedCount,
          trainingRate,
        };
      },
    );

    return trainingStats;
  }

  async getRosterYearsTrend(
    yearsDeclarations: {
      year: string;
      items: ngDeclaration[];
    }[],
  ): Promise<
    {
      year: string;
      totalCount: number;
    }[]
  > {
    return yearsDeclarations.map((item) => ({
      year: item.year,
      totalCount: item.items[0]?.roster?.length || 0,
    }));
  }

  async findRostersByDeclarationId(declarationId: number): Promise<ngRoster[]> {
    const rosterRepository = this.dataSource.getRepository(ngRoster);
    return rosterRepository.find({
      where: {
        declaration_id: declarationId,
      },
    });
  }
}
