openapi: 3.0.0
info:
  title: 新疆兵团三门诊区 API
  description: 新疆兵团三门诊区预约和康复指导申请接口
  version: 1.0.0
  
servers:
  - url: http://localhost:3000/xjbt-smzq
    description: 开发环境

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Error:
      type: object
      properties:
        code:
          type: integer
          description: 错误码
        message:
          type: string
          description: 错误信息
        data:
          type: object
          nullable: true
          description: 响应数据

    AppointmentBase:
      type: object
      required:
        - id_card
        - name
        - inst_id
        - appt_date
      properties:
        id_card:
          type: string
          description: 身份证号
          minLength: 18
          maxLength: 18
        name:
          type: string
          description: 姓名
        inst_id:
          type: integer
          description: 机构ID
        doctor_id:
          type: integer
          description: 医师ID（可为空表示不指定医师）
          nullable: true
        appt_date:
          type: string
          format: date-time
          description: 预约日期时间（ISO8601格式）
        disease_category:
          type: string
          description: 职业病病人分类编码
          minLength: 7
          maxLength: 7
          nullable: true
        service_type:
          type: string
          description: 预约服务类型描述
          nullable: true
        requirement:
          type: string
          description: 预约需求
          nullable: true

    AppointmentResponse:
      allOf:
        - $ref: '#/components/schemas/AppointmentBase'
        - type: object
          properties:
            id:
              type: integer
              description: 预约记录ID（主键）
            status:
              type: integer
              description: 状态：0-待审核，1-通过，2-拒绝，3-已取消，4-已完成
              enum: [0, 1, 2, 3, 4]

    RehabGuideApplicationBase:
      type: object
      required:
        - id_card
        - name
        - user_id
        - create_date
        - guide_type
        - content
      properties:
        id_card:
          type: string
          description: 身份证号
          minLength: 18
          maxLength: 18
        name:
          type: string
          description: 姓名
        user_id:
          type: integer
          description: 用户ID
        create_date:
          type: string
          format: date-time
          description: 创建日期（ISO8601格式）
        disease_category:
          type: string
          description: 职业病病人分类编码
          minLength: 7
          maxLength: 7
          nullable: true
        service_type:
          type: string
          description: 服务类型描述
          nullable: true
        guide_type:
          type: string
          description: 指导类型：online-线上，offline-线下
          enum: [online, offline]
        content:
          type: string
          description: 申请内容
        requirements:
          type: string
          description: 需求说明
          nullable: true
        attachment_urls:
          type: string
          description: 附件URL，多个用逗号分隔
          nullable: true

    RehabGuideApplicationResponse:
      allOf:
        - $ref: '#/components/schemas/RehabGuideApplicationBase'
        - type: object
          properties:
            id:
              type: integer
              description: 康复指导申请记录ID（主键）
            status:
              type: integer
              description: 状态：0-待处理，1-已接受，2-已拒绝，3-已完成
              enum: [0, 1, 2, 3]

paths:
  /appointment:
    post:
      summary: 创建预约
      description: 创建新的就诊预约记录
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AppointmentBase'
            example:
              id_card: "110101199001011234"
              name: "张三"
              inst_id: 1001
              appt_date: "2024-03-20T09:30:00Z"
              doctor_id: 2001
              disease_category: "0100000"
              service_type: "职业病诊断"
              requirement: "需要进行职业病诊断评估"
      responses:
        '200':
          description: 预约创建成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    $ref: '#/components/schemas/AppointmentResponse'
              example:
                code: 0
                message: "成功"
                data:
                  id: 123
                  id_card: "110101199001011234"
                  name: "张三"
                  inst_id: 1001
                  doctor_id: 2001
                  appt_date: "2024-03-20T09:30:00Z"
                  disease_category: "0100000"
                  service_type: "职业病诊断"
                  requirement: "需要进行职业病诊断评估"
                  status: 0
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                code: 400
                message: "请求参数错误：身份证号格式不正确"
                data: null
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                code: 401
                message: "未授权访问"
                data: null
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                code: 500
                message: "服务器内部错误"
                data: null

  /rehab-guide-application:
    post:
      summary: 创建康复指导申请
      description: 创建新的康复指导申请记录
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RehabGuideApplicationBase'
            example:
              id_card: "110101199001011234"
              name: "张三"
              user_id: 1001
              create_date: "2024-03-15T10:30:00Z"
              disease_category: "0100000"
              guide_type: "online"
              content: "需要进行远程康复指导"
              requirements: "希望能够提供详细的康复方案"
              attachment_urls: "http://example.com/file1.pdf,http://example.com/file2.pdf"
      responses:
        '200':
          description: 康复指导申请创建成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    $ref: '#/components/schemas/RehabGuideApplicationResponse'
              example:
                code: 0
                message: "成功"
                data:
                  id: 456
                  id_card: "110101199001011234"
                  name: "张三"
                  user_id: 1001
                  create_date: "2024-03-15T10:30:00Z"
                  disease_category: "0100000"
                  guide_type: "online"
                  content: "需要进行远程康复指导"
                  requirements: "希望能够提供详细的康复方案"
                  attachment_urls: "http://example.com/file1.pdf,http://example.com/file2.pdf"
                  status: 0
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                code: 400
                message: "请求参数错误：指导类型必须为'online'或'offline'"
                data: null
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                code: 401
                message: "未授权访问"
                data: null
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                code: 500
                message: "服务器内部错误"
                data: null
