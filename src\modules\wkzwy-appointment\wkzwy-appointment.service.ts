import { Injectable, Logger } from '@nestjs/common';
import { Appointment } from './sqlserver/appointment.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { HealthCheckAppointmentService } from '../health-check-appointment/health-check-appointment.service';
import { AdminorgsService } from '../adminorgs/adminorgs.service';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class WkzwyAppointmentService {
  constructor(
    @InjectRepository(Appointment, 'sqlserverConnection')
    private readonly appointmentRepository: Repository<Appointment>,
    private healthCheckAppointmentService: HealthCheckAppointmentService,
    private adminorgsService: AdminorgsService,
    private configService: ConfigService,
  ) {}
  private readonly logger = new Logger(WkzwyAppointmentService.name);

  // 同步mongodb插入/更新 预约单
  async insert(appointmentId: string) {
    try {
      const healthCheckAppointment =
        await this.healthCheckAppointmentService.findOne({
          _id: appointmentId,
        });
      if (!healthCheckAppointment) {
        throw new Error('预约单不存在');
      }
      const adminorgs = await this.adminorgsService.findOne({
        _id: healthCheckAppointment.EnterpriseID,
      });
      if (!adminorgs) throw new Error('EnterpriseID不存在');
      const newAppointment = new Appointment();
      newAppointment.appointmentId = appointmentId;
      newAppointment.EnterpriseID = healthCheckAppointment.EnterpriseID;
      newAppointment.EnterpriseCode = healthCheckAppointment.EnterpriseCode;
      newAppointment.EnterpriseName = adminorgs.cname;
      newAppointment.contact = adminorgs.contract || '';
      newAppointment.phoneNum = adminorgs.phoneNum || '';
      newAppointment.physicalExamOrgId =
        healthCheckAppointment.physicalExamOrgId;
      newAppointment.checkType = healthCheckAppointment.checkType;
      newAppointment.isReview = healthCheckAppointment.isReview;
      newAppointment.peopleNum = healthCheckAppointment.peopleNum;
      newAppointment.startTime = healthCheckAppointment.startTime;
      newAppointment.endTime = healthCheckAppointment.endTime;
      newAppointment.createdAt = healthCheckAppointment.createdAt;
      newAppointment.updatedAt = healthCheckAppointment.updatedAt;
      if (healthCheckAppointment.healthcheckId)
        newAppointment.healthcheckId = healthCheckAppointment.healthcheckId;
      newAppointment.employeeIds = healthCheckAppointment.employeeIds || [];
      newAppointment.status = healthCheckAppointment.status;
      if (healthCheckAppointment.files) {
        const httpPath = this.configService.get('enterprise_http_path');
        const outPutPrefix = `/static${httpPath}/${healthCheckAppointment.EnterpriseID}/`;
        newAppointment.files_authorization = healthCheckAppointment.files
          .authorization
          ? outPutPrefix + healthCheckAppointment.files.authorization
          : '';
        newAppointment.files_enterpriseInfo = healthCheckAppointment.files
          .enterpriseInfo
          ? outPutPrefix + healthCheckAppointment.files.enterpriseInfo
          : '';
        newAppointment.files_healthExamRecord = healthCheckAppointment.files
          .healthExamRecord
          ? outPutPrefix + healthCheckAppointment.files.healthExamRecord
          : '';
      }
      const rawResult = await this.appointmentRepository.query(
        `
  SELECT TOP 1 * 
  FROM Appointment 
  WHERE Appointment.appointmentId = @0
`,
        [appointmentId],
      );
      const wkzwyAppointment = rawResult[0];
      if (wkzwyAppointment) {
        const res = await this.appointmentRepository.update(
          { appointmentId },
          newAppointment,
        );
        if (res && res.affected) {
          return newAppointment.status === 7
            ? '预约单取消成功'
            : '预约单更新成功';
        } else {
          return '预约单更新失败';
        }
      } else {
        const res2 = await this.appointmentRepository.save(newAppointment);
        if (res2 && res2.appointmentId) {
          return '预约单插入成功';
        } else {
          return '预约单插入失败';
        }
      }
    } catch (e) {
      this.logger.error(e);
      throw new Error(e);
    }
  }
  // 查询所有预约单
  async findAll(query = {}): Promise<object> {
    if (query['status']) query['status'] = +query['status'];
    const [list, count] = await this.appointmentRepository.findAndCount({
      where: query,
      order: { createdAt: 'DESC' },
    });
    return { count, list };
  }
  // 查询单个预约单
  async findById(id: number): Promise<Appointment> {
    const rawResult = await this.appointmentRepository.query(
      `
  SELECT * 
  FROM Appointment 
  WHERE Appointment.id = @0
`,
      [id],
    );
    return rawResult[0];
  }
  // 删除预约单
  async delete(query = {}): Promise<any> {
    return await this.appointmentRepository.delete(query);
  }
}
