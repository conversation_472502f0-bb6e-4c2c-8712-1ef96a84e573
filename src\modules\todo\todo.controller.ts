import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  Query,
  ValidationPipe,
  Version,
  UseGuards,
  ParseIntPipe,
  BadRequestException,
} from '@nestjs/common';
import { TodoService } from './todo.service';
import { CreateTodoDto } from './dto/create-todo.dto';
import { UpdateTodoDto } from './dto/update-todo.dto';
import { FindTodoDto } from './dto/find-todo.dto';
import { Todo } from './mysql/todo.entity';
import { OpenidGuard } from 'src/common/guards/openid.guard';
import { User } from 'src/common/decorators/user.decorator';
import { OnEvent } from '@nestjs/event-emitter';
import { TodoEventType, TodoEventPayload, TodoItem, TodoPagination } from './constants';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';

@Controller('todos')
@UseGuards(OpenidGuard)
export class TodoController {
  constructor(private readonly todoService: TodoService) {}

  @Post()
  @Version('1')
  create(@Body(new ValidationPipe({ transform: true })) createTodoDto: CreateTodoDto, @User() user,): Promise<Todo> {
    return this.todoService.create(createTodoDto, user);
  }

  @Get()
  @Version('1')
  async findAll(
    @Query(new ValidationPipe({ transform: true })) findTodoDto: FindTodoDto,
    @User() user,
  ): Promise<{ items: TodoItem[]; meta: TodoPagination }> {
    const [items, meta] = await this.todoService.findAll(findTodoDto, user);
    return { items, meta };
  }

  @Get(':id')
  @Version('1')
  findOne(@Param('id') id: string, @User() user): Promise<Todo> {
    return this.todoService.findOne(+id, user);
  }

  @Put(':id')
  @Version('1')
  update(
    @Param('id') id: string,
    @Body(new ValidationPipe({ transform: true })) updateTodoDto: UpdateTodoDto,
    @User() user,
  ): Promise<Todo> {
    return this.todoService.update(+id, updateTodoDto, user);
  }

  @Put(':id/complete')
  @Version('1')
  completeOne(
    @User() user,
    @Param('id', new ParseIntPipe({ 
      errorHttpStatusCode: 400,
      exceptionFactory: (error) => new BadRequestException('Invalid todo ID format'),
    })) id: number,
  ): Promise<Todo> {
    return this.todoService.completeOne(id, user);
  }

  @Delete(':id')
  @Version('1')
  remove(@Param('id') id: string, @User() user): Promise<void> {
    return this.todoService.remove(+id, user);
  }

  @OnEvent(TodoEventType.CREATE)
  async handleTodoCreateEvent(payload: TodoEventPayload) {
    // Transform plain object to DTO instance for validation
    const createTodoDto = plainToClass(CreateTodoDto, {
      type: payload.type,
      title: payload.title,
      description: payload.description,
      meta: payload.meta,
    });

    // Validate the DTO
    const errors = await validate(createTodoDto);
    if (errors.length > 0) {
      throw new BadRequestException({
        message: 'Todo event validation failed',
        errors: errors.map(error => ({
          property: error.property,
          constraints: error.constraints,
        })),
      });
    }

    // Create todo if validation passes
    const user = { id: payload.userId };
    return this.todoService.create(createTodoDto, user);
  }
}
