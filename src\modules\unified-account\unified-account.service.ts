import { Injectable, Logger } from '@nestjs/common';
import { END_POINT, END_POINT_TYPE } from 'src/constants/endPoint';
import { PhysicalExamAccountService } from './account-service/physical-exam/physical-exam-account.service';
import { SuperUserAccountService } from './account-service/super-user/super-user.account.service';
import { AdminOrgsAccountService } from './account-service/admin-orgs/admin-orgs.account.service';
import { AdminOrgsOrg } from './account-service/admin-orgs/admin-orgs-org.account.entity';
import { ServiceOrgAccountService } from './account-service/service-orgs/service-orgs.account.service';

type EndPointServices = {
  [END_POINT.qy]: AdminOrgsAccountService;
  [END_POINT.jc]: ServiceOrgAccountService;
  [END_POINT.jg]: SuperUserAccountService;
  [END_POINT.tj]: PhysicalExamAccountService;
};

@Injectable({})
export class UnifiedAccountService {
  private readonly logger = new Logger(UnifiedAccountService.name);
  constructor(
    private readonly physicalExamAccountService: PhysicalExamAccountService,
    private readonly adminOrgsAccountService: AdminOrgsAccountService,
    private readonly superUserAccountService: SuperUserAccountService,
    private readonly serviceOrgAccountService: ServiceOrgAccountService,
  ) {}

  private readonly endPointServices: EndPointServices = {
    [END_POINT.tj]: this.physicalExamAccountService,
    [END_POINT.qy]: this.adminOrgsAccountService,
    [END_POINT.jg]: this.superUserAccountService,
    [END_POINT.jc]: this.serviceOrgAccountService,
  } as EndPointServices;

  getEndPointService<T extends END_POINT_TYPE>(
    endPoint: T,
  ): EndPointServices[T] {
    return this.endPointServices[endPoint];
  }

  async getAccountById(endPoint: END_POINT_TYPE, id: string) {
    return await this.getEndPointService(endPoint).getAccount(id);
  }

  async getAccountByQuery(endPoint: END_POINT_TYPE, query) {
    this.logger.log(
      `开始在 ${endPoint} 端查询用户信息 query: ${JSON.stringify(query)}`,
    );

    const account =
      await this.getEndPointService(endPoint).getAccountByQuery(query);

    if (!account) {
      this.logger.log(
        `${endPoint} 端未查询到用户信息 query: ${JSON.stringify(query)}`,
      );
    } else {
      this.logger.log(
        `${endPoint} 端查询用户信息成功 account: ${JSON.stringify(account)}`,
      );
    }

    return account;
  }

  async upsertSubAccounts(endPoint: END_POINT_TYPE, accountInfo) {
    this.logger.log(
      `${endPoint} 端开始创建用户 data: ${JSON.stringify(accountInfo)}`,
    );
    const endPointService = this.getEndPointService(endPoint);
    return await endPointService.upsertSubAccounts(accountInfo);
  }

  async findOrCreate<T extends END_POINT_TYPE>(query: {
    endPoint: T;
    accountInfo: any;
  }): Promise<any> {
    const account = await this.getAccountByQuery(
      query.endPoint,
      query.accountInfo.query,
    );
    if (!account) {
      return await this.upsertSubAccounts(query.endPoint, query.accountInfo);
    }
    return await this.authExistAccount(
      query.endPoint,
      query.accountInfo,
      account,
    );
  }

  async updateQyOrgAccount(orgId: string, data: AdminOrgsOrg) {
    return await this.adminOrgsAccountService.updateOrgAccount(orgId, data);
  }

  // async getOrgAccountById(endPoint: END_POINT_TYPE, id: string) {
  //   return await this.getEndPointService(endPoint).getOrgAccount(id);
  // }

  async validateAccount(endPoint: END_POINT_TYPE, id: string) {
    return await this.getEndPointService(endPoint).validateAccount(id);
  }

  private async authExistAccount(endPoint, accountInfo, account) {
    this.logger.log(
      `${endPoint} 端授权已存在用户 data: ${JSON.stringify(accountInfo)}`,
    );
    const endPointService = this.getEndPointService(endPoint);
    if (!endPointService.authExistAccount) {
      return account;
    }
    return await endPointService.authExistAccount(accountInfo);
  }

  async deregisterAccount(endPoint, data: any) {
    const endPointService = this.getEndPointService(endPoint);
    if (!endPointService.deregisterAccount) {
      return;
    }
    return await endPointService.deregisterAccount(data);
  }
}
