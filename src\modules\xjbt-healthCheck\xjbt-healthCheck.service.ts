import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { MongoRepository } from 'typeorm';
import { EmployeeBasicInfo } from '../dak/entities/employeeBasicInfo.entity';
import { CreateEmployeeBasicInfoDto } from './dto/create-employee-basic-info.dto';
import { HealthSurvArchives } from '../dak/entities/HealthSurvArchives.entity';
import { Users } from '../sxccduijie/entities/users.entity';
import { Employees } from '../sxccduijie/entities/employees.entity';
import { Adminorgs } from '../adminorgs/adminorgs.entity';
import moment from 'moment';
@Injectable()
export class XjbtHealthCheckService {
  private readonly logger = new Logger(XjbtHealthCheckService.name);

  constructor(
    @InjectRepository(EmployeeBasicInfo, 'mongodbConnection')
    private employeeBasicInfoRepository: MongoRepository<EmployeeBasicInfo>,
    @InjectRepository(HealthSurvArchives, 'mongodbConnection')
    private healthSurvArchivesRepository: MongoRepository<HealthSurvArchives>,
    @InjectRepository(Users, 'mongodbConnection')
    private usersRepository: MongoRepository<Users>,
    @InjectRepository(Employees, 'mongodbConnection')
    private employeesRepository: MongoRepository<Employees>,
    @InjectRepository(Adminorgs, 'mongodbConnection')
    private adminorgsRepository: MongoRepository<Adminorgs>,
  ) { }

  // 职业健康监护档案抽取
  // （1）劳动者姓名、性别、年龄、籍贯、婚姻、嗜好等一般概况；
  // （2）劳动者职业史、既往史和职业病危害接触史；
  // （3）相应工作场所职业病危害因素监测结果；
  // （4）职业健康检查结果及处理情况；
  /**
   * 根据身份证号抽取职业健康监护档案
   * @param idNumber 身份证号
   * @returns 创建的职业健康监护档案
   */
  async extractHealthArchive(idNumber: string): Promise<HealthSurvArchives> {
    try {

      // 1. 一般概况
      let basicData = <any>{
        idNumber,
      }
      let user = await this.usersRepository.findOne({
        where: { idNo: idNumber },
      })
      if (!user) {
        throw new Error(`未找到身份证号为 ${idNumber} 的用户信息`);
      }
      // 企业信息
      // EnterpriseID企业id // companyId所在企业ID合集，默认最后一个是当前绑定的企业
      const EnterpriseID = user.companyId.length ? user.companyId[user.companyId.length - 1] : '';
      let adminorg = await this.adminorgsRepository.findOne({ where: { _id: EnterpriseID } }) || null;
      if (adminorg) {
        basicData.cname = adminorg.cname; // 企业名称
        basicData.cCode = adminorg.code; // 企业编码
      }
      // 最近一条员工信息
      let employee = await this.employeesRepository.aggregate([
        { $match: { IDNum: idNumber } },
        { $sort: { createdAt: -1 } }
      ]).toArray();
      const employeeInfo = employee.length ? employee[0] : null
      const { name, gender, phoneNum, nativePlace, hobby, marriage } = employeeInfo
      basicData = Object.assign(basicData, {
        name,
        gender,
        phoneNum,
        nativePlace,
        hobby,
        marriage
      });

      // 根据身份证号计算年龄
      const birthDate = idNumber.substring(6, 14);
      const birthYear = parseInt(birthDate.substring(0, 4), 10);
      const birthMonth = parseInt(birthDate.substring(4, 6), 10);
      const birthDay = parseInt(birthDate.substring(6, 8), 10);
      const today = moment();
      basicData.age = today.diff(moment(`${birthYear}-${birthMonth}-${birthDay}`, 'YYYY-MM-DD'), 'years').toString();

      // 

      // 创建职业健康监护档案
      const healthArchive = this.healthSurvArchivesRepository.create({
        // idNumber: idNumber,
        employeeId: employeeInfo._id,
        userId: user._id,
        EnterpriseID,
        // cname: basicData.cname || '',
        // cCode: basicData.cCode || '',
        // name: employeeInfo.name,
        // age: basicData.age,
        // gender: employeeInfo.gender,
        // phoneNum: employeeInfo.phoneNum,
        // nativePlace: employeeInfo.nativePlace,
        // hobby: employeeInfo.hobby,
        // marriage: employeeInfo.marriage,
        ...basicData,

        pastHistory: [],
        workHistory: [],
        exposureHistory: [],
        workspaceHarmResult: [],
        healthCheckResult: []
      });
      // 保存档案
      const result = await this.healthSurvArchivesRepository.save(healthArchive);

      this.logger.log(`成功创建职业健康监护档案: ${idNumber}`);
      // save() may return an array if input is an array, but here it's a single object
      return Array.isArray(result) ? result[0] : result;
    } catch (error) {
      this.logger.error(
        `创建职业健康监护档案失败: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * 创建或更新员工基本信息
   * @param createEmployeeBasicInfoDto 员工基本信息DTO
   * @returns 创建或更新的员工基本信息
   */
  async createOrUpdateEmployeeBasicInfo(
    createEmployeeBasicInfoDto: CreateEmployeeBasicInfoDto,
  ): Promise<EmployeeBasicInfo> {
    try {
      // 查询是否存在相同身份证的记录
      const existingRecord = await this.employeeBasicInfoRepository.findOne({
        where: {
          IDNum: createEmployeeBasicInfoDto.IDNum,
        },
      });

      let result: EmployeeBasicInfo;

      if (existingRecord) {
        // 如果存在，则更新记录
        this.logger.log(
          `更新员工基本信息: ${createEmployeeBasicInfoDto.IDNum}`,
        );

        // 更新时间
        createEmployeeBasicInfoDto.updatedAt = new Date();

        // 更新记录
        await this.employeeBasicInfoRepository.update(
          { _id: existingRecord._id },
          createEmployeeBasicInfoDto,
        );

        // 返回更新后的记录
        result = await this.employeeBasicInfoRepository.findOne({
          where: { _id: existingRecord._id },
        });
      } else {
        // 如果不存在，则创建新记录
        this.logger.log(
          `创建员工基本信息: ${createEmployeeBasicInfoDto.IDNum}`,
        );

        // 设置创建和更新时间
        createEmployeeBasicInfoDto.createdAt = new Date();
        createEmployeeBasicInfoDto.updatedAt = new Date();

        // 创建并保存新记录
        const newRecord = this.employeeBasicInfoRepository.create(
          createEmployeeBasicInfoDto,
        );
        result = await this.employeeBasicInfoRepository.save(newRecord);
      }

      return result;
    } catch (error) {
      this.logger.error(
        `创建或更新员工基本信息失败: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * 根据身份证号查询员工基本信息
   * @param idNumber 身份证号
   * @returns 员工基本信息
   */
  async findEmployeeBasicInfoByIdNumber(idNumber: string): Promise<EmployeeBasicInfo> {
    try {
      return await this.employeeBasicInfoRepository.findOne({
        where: { IDNum: idNumber },
      });
    } catch (error) {
      this.logger.error(
        `查询员工基本信息失败: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }


}