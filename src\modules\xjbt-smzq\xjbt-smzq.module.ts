import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { XjbtSmzqService } from './xjbt-smzq.service';
import { XjbtSmzqController } from './xjbt-smzq.controller';
import { Appointment } from './mysql/appointment.entity';
import { RehabGuideApplication } from './mysql/rehab-guide-application.entity';
import { DiseaseCategoryDict } from './mysql/disease-category-dict.entity';

@Module({
  imports: [
    // 不指定数据源，使用默认数据源
    TypeOrmModule.forFeature([Appointment], 'mysqlConnectionXjbt'),
    TypeOrmModule.forFeature([RehabGuideApplication], 'mysqlConnectionXjbt'),
    TypeOrmModule.forFeature([DiseaseCategoryDict], 'mysqlConnectionXjbt'),
  ],
  controllers: [XjbtSmzqController],
  providers: [XjbtSmzqService],
})
export class XjbtSmzqModule {}
