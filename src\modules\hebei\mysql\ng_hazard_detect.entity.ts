import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

// 用人单位危害因素检测统计
@Entity('ng_hazard_detect')
export class ngHazardDetect {
  @PrimaryGeneratedColumn({ type: 'int', comment: 'hazard_detect_id' })
  id: number;

  @Column({ type: 'int', nullable: true, comment: '申报表id' })
  declaration_id: number | null;

  @Column({
    type: 'tinyint',
    nullable: true,
    comment:
      '危害大类(1粉尘,2化学物质,3物理因素,4放射性因素,5生物因素,6其它因素)',
  })
  hazard_category: number | null;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '危害因素' })
  hazard_factor: string | null;

  @Column({ type: 'int', nullable: true, comment: '检测点数' })
  detection_points: number | null;

  @Column({ type: 'int', nullable: true, comment: '超标点数' })
  overlimit_points: number | null;

  @Column({ type: 'int', nullable: true, comment: '危害因素编码' })
  hazard_code: number | null;

  @Column({ type: 'boolean', nullable: true, comment: '是否监管要求' })
  is_required: boolean | null;
}
