import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';
// 职业卫生大屏-监测用人单位职业健康培训统计
@Entity({
  name: 'DP_ZYWSDP_JCYRDWZYJKPXTJ',
  comment: '职业卫生大屏-监测用人单位职业健康培训统计',
})
export class DP_ZYWSDP_JCYRDWZYJKPXTJ {
  @PrimaryGeneratedColumn()
  ID: number;

  @Column({ type: 'varchar2', length: 255, comment: '类型' })
  DQ: string;

  @Column({ type: 'varchar2', length: 255, comment: '用人单位数' })
  YRDWS: string;

  @Column({ type: 'varchar2', length: 255, comment: '负责人培训企业数' })
  FZRPXQYS: string;

  @Column({ type: 'varchar2', length: 255, comment: '管理人员培训企业数' })
  GLRYPXQYS: string;

  @Column({ type: 'varchar2', length: 255, comment: '劳动者总人数' })
  LDZZRS: string;

  @Column({ type: 'varchar2', length: 255, comment: '劳动者培训人数' })
  LDZPXRS: string;

  @Column({ type: 'varchar2', length: 255, comment: '劳动者培训率' })
  LDZPXL: string;

  @Column({ type: 'varchar2', length: 255, comment: '行业类型' })
  HYLX: string;

  @Column({ type: 'varchar2', length: 255, comment: '企业规模' })
  QYGM: string;

  @Column({ type: 'varchar2', length: 255, comment: '年度' })
  ND: string;
}
