import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { MongoRepository } from 'typeorm';
import { AccountService } from '../account-service.interface';
import { SuperUser, Member } from './super-user.account.entity';
import { plainToClass } from 'class-transformer';
import { ConfigService } from '@nestjs/config';
import {
  CreateSuperAccountData,
  CreateSuperUserData,
  QuerySuperAccountData,
} from '../../types';
import { nanoid } from 'nanoid';

@Injectable()
export class SuperUserAccountService
  implements AccountService<CreateSuperAccountData, Member>
{
  constructor(
    @InjectRepository(SuperUser, 'mongodbConnection')
    private readonly superUserRepository: MongoRepository<SuperUser>,

    private readonly configService: ConfigService,
  ) {}

  async upsertSubAccounts(data: CreateSuperAccountData): Promise<Member> {
    const org = await this.upsertOrgAccount(data);
    const user = await this.upsertUserAccount(
      {
        _id: nanoid(),
        phoneNum: data.query.phoneNum,
        name: data.query.phoneNum,
        userName: data.query.phoneNum,
        roles: [],
        jobTitle: '',
      },
      org,
    );
    return user;
  }
  async upsertOrgAccount(data: CreateSuperAccountData): Promise<SuperUser> {
    const org = await this.getOrgAccountByQuery(data.query);
    if (!org) {
      return this.createOrgAccount(data.org);
    }
    return org;
  }

  async upsertUserAccount(data: Member, org: SuperUser): Promise<Member> {
    const user = await this.getUserAccountByQuery(
      {
        phoneNum: data.phoneNum,
        unifiedCode: org.unifiedCode,
        areaCode: org.area_code,
      },
      org,
    );
    if (!user) {
      return this.createUserAccount(data, org);
    } else {
      // return this.updateUserAccount(user._id, data);
    }
    return user;
  }

  async getAccount(id: string): Promise<SuperUser> {
    const user = await this.superUserRepository.findOne({
      where: { _id: id },
    });
    return user;
  }

  async getOrgAccountByQuery(query: QuerySuperAccountData): Promise<SuperUser> {
    const org = await this.superUserRepository.findOne({
      where: {
        unifiedCode: query.unifiedCode,
        area_code: query.areaCode,
      },
    });
    return org;
  }

  async getUserAccountByQuery(
    query: QuerySuperAccountData,
    org: SuperUser,
  ): Promise<Member> {
    return Promise.resolve(
      org.members.find((member) => {
        if (member.phoneNum === query.phoneNum) {
          return member;
        }
      }),
    );
  }

  async getAccountByQuery(query: QuerySuperAccountData): Promise<Member> {
    const org = await this.getOrgAccountByQuery(query);
    if (!org) {
      return null;
    }
    const user = await this.getUserAccountByQuery(query, org);
    return user;
  }

  async createOrgAccount(data: CreateSuperUserData): Promise<SuperUser> {
    const org = await this.superUserRepository.save(
      plainToClass(SuperUser, {
        group: this.configService.get('groupID').superGroupID,
        ...data,
      }),
    );
    return org;
  }

  async createUserAccount(user: Member, org: SuperUser): Promise<Member> {
    org.members.push(user);
    await this.superUserRepository.updateOne(
      { _id: org._id },
      { $set: { members: org.members } },
    );
    return user;
  }

  async validateAccount(/*id: string*/): Promise<boolean> {
    return Promise.resolve(true);
  }
}
