import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Brackets } from 'typeorm';
import { Expert } from './mysql/expert.entity';
import * as moment from 'moment';
import { CertificateService } from '../certificate/certificate.service';
import { ExpertClassifierService } from '../dictionary/expert_classifier.service'; // 专家分类
import { DictionaryService } from '../dictionary/dictionary.service';
import { ExpertExtractionRecordsService } from './expert-extraction-records.service'; // 专家抽取记录
import { RandomExtractDto } from './dto/expert-random-extract.dto';

@Injectable()
export class ExpertService {
  constructor(
    @InjectRepository(Expert, 'mysqlConnectionIservice')
    private readonly expertRepository: Repository<Expert>,
    private readonly certificateService: CertificateService,
    private readonly expertClassifierService: ExpertClassifierService,
    private readonly dictionaryService: DictionaryService,
    private readonly expertExtractionRecordsService: ExpertExtractionRecordsService,
  ) {}
  private readonly logger = new Logger(ExpertService.name);
  // 获取专家列表
  async findAll(
    query: {
      pageSize?: number | string;
      curPage?: number | string;
      keyWord?: string;
      [key: string]: any;
    } = {},
  ): Promise<any> {
    const limit = Number(query.pageSize) || 10;
    const curPage = Number(query.curPage) || 1;

    const queryBuilder = this.expertRepository
      .createQueryBuilder('expert')
      .where('expert.declaration_status != "0"');

    if (query.keyWord) {
      queryBuilder.andWhere(
        'expert.name LIKE :keyWord OR expert.phone LIKE :keyWord',
        {
          keyWord: `%${query.keyWord}%`,
        },
      );
    }

    for (const key in query) {
      if (!query[key]) continue;
      if (
        query.hasOwnProperty(key) &&
        !['pageSize', 'curPage', 'keyWord', 'area_code', 'quantity'].includes(
          key,
        )
      ) {
        queryBuilder.andWhere(`expert.${key} = :${key}`, { [key]: query[key] });
      }
      if (key === 'area_code') {
        let match = query[key].replace(/0+$/, '');
        if ([1, 3, 5].includes(match.length)) match = match + '0';
        queryBuilder.andWhere('expert.area_code like :area_code', {
          area_code: `${match}%`,
        });
      }
    }
    if (!query.state) {
      queryBuilder.andWhere('expert.state != "4"');
    }

    // 返回的数据处理
    const expertClassifiers = await this.expertClassifierService.findAll();
    const expertClassifierObj = {};
    expertClassifiers.forEach((item) => {
      expertClassifierObj[item.id] = item.classification_name;
    });

    const education = await this.dictionaryService.education();

    const formatExpert = (expert: Expert) => ({
      ...expert,
      birthday: expert.birthday
        ? moment(expert.birthday).format('YYYY-MM-DD')
        : '',
      age: expert.birthday ? moment().diff(expert.birthday, 'years') : '',
      gender: expert.gender ? (expert.gender === 'M' ? '男' : '女') : '',
      category: expertClassifierObj[expert.category_id],
      education:
        education.find((item) => item.id === expert.education)?.name || '',
      level: expert.level
        ? ['兵团级', '师市级', '团镇级'][expert.level - 1]
        : '',
      id_type: expert.id_type ? (expert.id_type == 1 ? '身份证' : '其他') : '',
    });

    // 随机抽取
    // if (query.quantity) {
    //   queryBuilder.andWhere('expert.declaration_status != 0');
    //   queryBuilder.andWhere('expert.declaration_status != 2');
    //   queryBuilder.orderBy('RAND()');
    //   const experts = await queryBuilder.take(+query.quantity).getMany();
    //   return { total: experts.length, list: experts.map(formatExpert) };
    // }

    // 分页获取
    const [experts, total] = await queryBuilder
      .skip((curPage - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      total,
      list: experts.map(formatExpert),
    };
  }

  // 随机抽取专家
  async randomExtract(query: RandomExtractDto): Promise<any> {
    const {
      quantity,
      management_element,
      work_item,
      item_requirements = '',
      exclude_expert_id,
      area_code,
      category_id,
      education,
      gender,
      keyWord,
      level,
      first_type,
    } = query;

    // 校验必填参数
    if (!quantity || !management_element || !work_item) {
      throw new HttpException('必填参数错误', HttpStatus.BAD_REQUEST);
    }

    try {
      const queryBuilder = this.expertRepository
        .createQueryBuilder('expert')
        .where('expert.declaration_status = "1"')
        .andWhere('expert.state != "4"');

      // 处理关键字查询
      if (keyWord) {
        queryBuilder.andWhere(
          'expert.name LIKE :keyWord OR expert.phone LIKE :keyWord',
          {
            keyWord: `%${keyWord}%`,
          },
        );
      }

      // 处理 area_code 数组
      if (area_code && area_code.length > 0) {
        queryBuilder.andWhere(
          new Brackets((qb) => {
            area_code.forEach((code, index) => {
              let match = code.replace(/0+$/, '');
              if ([1, 3, 5].includes(match.length)) match = match + '0';
              qb.orWhere(`expert.area_code LIKE :area_code_${index}`, {
                [`area_code_${index}`]: `${match}%`,
              });
            });
          }),
        );
      }

      //处理排除的专家
      if (exclude_expert_id && exclude_expert_id.length > 0) {
        queryBuilder.andWhere('expert.id NOT IN (:...exclude_expert_id)', {
          exclude_expert_id,
        });
      }

      // 处理 category_id 数组
      if (category_id && category_id.length > 0) {
        queryBuilder.andWhere('expert.category_id IN (:...category_id)', {
          category_id,
        });
      }

      // 处理其他筛选条件
      if (education) {
        queryBuilder.andWhere('expert.education = :education', { education });
      }

      if (gender) {
        queryBuilder.andWhere('expert.gender = :gender', { gender });
      }

      if (level) {
        queryBuilder.andWhere('expert.level = :level', { level });
      }

      if (first_type) {
        queryBuilder.andWhere('expert.first_type = :first_type', { first_type });
      }

      // 随机排序
      queryBuilder.orderBy('RAND()');

      // 返回的数据处理
      const expertClassifiers = await this.expertClassifierService.findAll();
      const expertClassifierObj = {};
      expertClassifiers.forEach((item) => {
        expertClassifierObj[item.id] = item.classification_name;
      });

      const educationDict = await this.dictionaryService.education();

      const formatExpert = (expert: Expert) => ({
        ...expert,
        birthday: expert.birthday
          ? moment(expert.birthday).format('YYYY-MM-DD')
          : '',
        age: expert.birthday ? moment().diff(expert.birthday, 'years') : '',
        gender: expert.gender ? (expert.gender === 'M' ? '男' : '女') : '',
        category: expertClassifierObj[expert.category_id],
        education:
          educationDict.find((item) => item.id === expert.education)?.name ||
          '',
        level: expert.level
          ? ['兵团级', '师市级', '团镇级'][expert.level - 1]
          : '',
        id_type: expert.id_type
          ? expert.id_type == 1
            ? '身份证'
            : '其他'
          : '',
      });

      // 获取随机抽取的专家
      const experts = await queryBuilder.take(+quantity).getMany();

      // 保存抽取记录
      await this.expertExtractionRecordsService.create({
        management_element,
        work_item,
        expert_list: experts.map((item) => item.name).join('、'),
        item_requirements,
      });

      // 返回抽取的专家
      return { total: experts.length, list: experts.map(formatExpert) };
    } catch (e) {
      this.logger.error('随机抽取专家失败: ' + e.message);
      throw new HttpException(
        '随机抽取专家失败: ' + e.message,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  // 获取单个专家信息详情
  async findOne(id: number): Promise<any> {
    const res: any = await this.expertRepository.findOne({
      where: { id },
    });
    const area_code = res.area_code;
    if (area_code) {
      const area = await this.dictionaryService.getAreaInfo(area_code);
      res.area_name = area;
    }
    if (res) {
      // 获取专家的资质证书
      const certificates = await this.certificateService.findAll({
        user_id: id,
      });
      return { ...res, certificates };
    }
    throw new HttpException('未找到该专家信息', HttpStatus.BAD_REQUEST);
  }

  // 获取单个专家信息 专家端登录页登录
  async findOne2(body: { number: string }): Promise<any> {
    try {
      this.logger.log('专家端登录' + JSON.stringify(body));
      console.log('专家端登录', body);
      const { number } = body;
      if (!number) {
        throw new HttpException(
          '请输入证件号或者手机号',
          HttpStatus.BAD_REQUEST,
        );
      }
      const res = await this.expertRepository
        .createQueryBuilder('expert')
        .where('expert.id_number = :number OR expert.phone = :number', {
          number,
        })
        .getOne();
      return res;
    } catch (e) {
      this.logger.error('专家端单点登录失败' + e.message);
      console.log(44444, e);
      throw new HttpException('未找到该专家信息', HttpStatus.BAD_REQUEST);
    }
  }

  // 统一门户 单点登录
  async sso(userInfo: any) {
    try {
      const id_number = userInfo.idNo ? userInfo.idNo + '' : '';
      const phone = userInfo.phoneNum || '';
      // const user_id = userInfo.id || '';
      if (!id_number && !phone) {
        throw new HttpException(
          '请传入证件号或者手机号',
          HttpStatus.BAD_REQUEST,
        );
      }

      const res = await this.expertRepository
        .createQueryBuilder('expert')
        .where('expert.id_number = :id_number OR expert.phone = :phone', {
          // user_id,
          id_number,
          phone,
        })
        .getOne();
      if (res && res.id) {
        // if (!res.user_id) {
        //   await this.expertRepository.update({ id: res.id }, { user_id });
        // }
        return res;
      }
      // 创建专家
      const expert = this.expertRepository.create({
        name: userInfo.name,
        id_number,
        phone,
        // user_id,
        email: userInfo.email || '',
        work_unit: userInfo.orgname || '',
      });
      const newExpert = await this.expertRepository.save(expert);
      this.logger.log('sso专家创建成功, id = ' + newExpert.id);
      return newExpert;
    } catch (e) {
      this.logger.error('统一门户单点登录失败' + e.message);
      throw new HttpException(
        '未找到该专家信息:' + e.message,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  // 获取专家基础/简单信息
  async findInfo(id: number): Promise<any> {
    return await this.expertRepository.findOne({
      where: { id },
      select: [
        'id',
        'name',
        'phone',
        'id_number',
        'category_id',
        'level',
        'state',
      ],
    });
  }

  async create(createDto: Expert): Promise<Expert> {
    const { id_number } = createDto;
    // 判断是否已存在
    const isExist: any = await this.expertRepository.findOne({
      where: { id_number },
    });
    if (isExist) {
      throw new HttpException(
        '该专家证件号码已存在，请勿重复添加',
        HttpStatus.BAD_REQUEST,
      );
    } else {
      if (createDto.level) createDto.level = +createDto.level;
      if (createDto.declaration_status)
        createDto.declaration_status = +createDto.declaration_status;
      if (createDto.work_years) createDto.work_years = +createDto.work_years;
      if (createDto.state) createDto.state = +createDto.state;
      if (createDto.education) createDto.education = +createDto.education;
      const Expert = this.expertRepository.create(createDto);
      if (createDto.id_type) createDto.id_type = +createDto.id_type;
      return this.expertRepository.save(Expert);
    }
  }

  // 更新
  async update(id: number, updateDto: Expert): Promise<any> {
    if (updateDto.level) updateDto.level = +updateDto.level;
    if (updateDto.declaration_status)
      updateDto.declaration_status = +updateDto.declaration_status;
    if (updateDto.work_years) updateDto.work_years = +updateDto.work_years;
    if (updateDto.state) updateDto.state = +updateDto.state;
    const res = await this.expertRepository.update(id, updateDto);
    if (res.affected === 0) {
      throw new Error('删除失败, 请检查id是否正确');
    }
    return await this.findOne(id);
  }

  // iservice内部调用
  async updateInner(
    id: number,
    data: {
      declaration_status?: number;
      state?: number;
      level?: number;
      dismissal_date?: Date;
      category_id?: number;
    },
  ): Promise<any> {
    const res = await this.expertRepository.update(id, data);
    if (res.affected === 0) {
      throw new Error('更新失败, 请检查id或data参数是否正确');
    }
  }

  // 删除
  async remove(id: number): Promise<any> {
    const res = await this.expertRepository.delete(id);
    if (res.affected === 0) {
      throw new Error('删除失败, 请检查id是否正确');
    }
    return '操作成功';
  }

  async assignDeclaration({
    declarationId,
    expertIds,
  }: {
    declarationId: string;
    expertIds: string[];
  }) {
    if (typeof expertIds === 'string') {
      expertIds = [expertIds];
    }

    for (const userId of expertIds) {
      await this.expertRepository
        .createQueryBuilder()
        .update(Expert)
        .set({
          declaration_ids: () =>
            `JSON_ARRAY_APPEND(COALESCE(declaration_ids, '[]'), '$', :declarationId)`,
        })
        .where('id = :userId', { userId })
        .setParameter('declarationId', declarationId)
        .execute();
    }
  }
}
