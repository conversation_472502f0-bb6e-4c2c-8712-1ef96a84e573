# 新疆兵团三门诊区接口文档

## 目录
- [新疆兵团三门诊区接口文档](#新疆兵团三门诊区接口文档)
  - [目录](#目录)
  - [通用说明](#通用说明)
    - [接口域名](#接口域名)
    - [公共响应格式](#公共响应格式)
    - [通用错误码](#通用错误码)
  - [接口列表](#接口列表)
    - [1. 创建预约](#1-创建预约)
    - [2. 创建康复指导申请](#2-创建康复指导申请)
  - [数据字典](#数据字典)
    - [预约状态](#预约状态)
    - [康复指导申请状态](#康复指导申请状态)
    - [职业病分类](#职业病分类)

## 通用说明

### 接口域名
```
http://localhost/xjbt-smzq
```

### 公共响应格式
所有接口均使用统一的响应格式，通过wrapperResponse进行包装：
```json
{
  "code": 200,           // 错误码，200表示成功
  "message": "成功",    // 错误信息
  "data": {            // 响应数据
    // 具体接口的返回数据
  },
  "success": true      // 是否成功标志
}
```

### 通用错误码
| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 接口列表

### 1. 创建预约

**接口地址**：`POST /appointments`

**请求参数**：
```typescript
{
  id_card: string;      // 身份证号（必填，18位）
  name: string;         // 姓名（必填）
  inst_id: number;      // 机构ID（必填）
  doctor_id?: number;   // 医师ID（可选）
  appt_date: string;    // 预约日期时间（必填，ISO8601格式）
  disease_category?: string;  // 职业病病人分类编码（可选，7位）
  service_type?: string;      // 预约服务类型描述（可选）
  requirement?: string;       // 预约需求（可选）
}
```

**响应数据**：
```json
{
  "code": 200,
  "message": "创建预约",
  "data": {
    "appt_id": 1001,       // 预约ID
    "id_card": "110101199001011234",  // 身份证号
    "name": "张三",         // 姓名
    "inst_id": 1001,       // 机构ID
    "doctor_id": 2001,     // 医师ID
    "appt_date": "2024-03-15T10:30:00Z",  // 预约日期时间
    "status": 0,           // 状态：0-待审核
    "disease_category": "0100000",  // 职业病分类
    "service_type": "常规体检",     // 服务类型
    "requirement": "需要进行肺功能检查"  // 需求
  },
  "success": true
}
```

**请求示例**：
```json
{
  "id_card": "110101199001011234",
  "name": "张三",
  "inst_id": 1001,
  "appt_date": "2024-03-15T10:30:00Z",
  "disease_category": "0100000",
  "service_type": "常规体检",
  "requirement": "需要进行肺功能检查"
}
```

**curl示例**：
```bash
curl -X POST 'http://localhost:3000/xjbt-smzq/appointments' \
-H 'Content-Type: application/json' \
-H 'Authorization: Bearer your_token_here' \
-d '{
  "id_card": "110101199001011234",
  "name": "张三",
  "inst_id": 1001,
  "appt_date": "2024-03-15T10:30:00Z",
  "disease_category": "0100000",
  "service_type": "常规体检",
  "requirement": "需要进行肺功能检查"
}'
```

### 2. 创建康复指导申请

**接口地址**：`POST /rehab-guide-application`

**请求参数**：
```typescript
{
  id_card: string;      // 身份证号（必填，18位）
  name: string;         // 姓名（必填）
  create_date: string;  // 创建日期（必填，ISO8601格式）
  disease_category?: string;  // 职业病病人分类编码（可选，7位）
  service_type?: string;      // 服务类型描述（可选）
  guide_type: 'online' | 'offline';  // 指导类型（必填）：online-线上，offline-线下
  content: string;      // 申请内容（必填）
  requirements?: string;      // 需求说明（可选）
  attachment_urls?: string;   // 附件URL，多个用逗号分隔（可选）
}
```

**响应数据**：
```json
{
  "code": 200,
  "message": "创建康复指导申请",
  "data": {
    "application_id": 2001,  // 申请ID
    "id_card": "110101199001011234",  // 身份证号
    "name": "张三",           // 姓名
    "create_date": "2024-03-15T10:30:00Z",  // 创建日期
    "status": 0,             // 状态：0-待处理
    "disease_category": "0100000",  // 职业病分类
    "guide_type": "online",  // 指导类型
    "content": "需要进行远程康复指导",  // 申请内容
    "requirements": "希望能够提供详细的康复方案",  // 需求说明
    "attachment_urls": "http://example.com/file1.pdf,http://example.com/file2.pdf"  // 附件URL
  },
  "success": true
}
```

**请求示例**：
```json
{
  "id_card": "110101199001011234",
  "name": "张三",
  "create_date": "2024-03-15T10:30:00Z",
  "disease_category": "0100000",
  "guide_type": "online",
  "content": "需要进行远程康复指导",
  "requirements": "希望能够提供详细的康复方案",
  "attachment_urls": "http://example.com/file1.pdf,http://example.com/file2.pdf"
}
```

**curl示例**：
```bash
curl -X POST 'http://localhost:3000/xjbt-smzq/rehab-guide-application' \
-H 'Content-Type: application/json' \
-H 'Authorization: Bearer your_token_here' \
-d '{
  "id_card": "110101199001011234",
  "name": "张三",
  "create_date": "2024-03-15T10:30:00Z",
  "disease_category": "0100000",
  "guide_type": "online",
  "content": "需要进行远程康复指导",
  "requirements": "希望能够提供详细的康复方案",
  "attachment_urls": "http://example.com/file1.pdf,http://example.com/file2.pdf"
}'
```

## 数据字典

### 预约状态
| 状态码 | 说明 |
|--------|------|
| 0 | 待审核 |
| 1 | 通过 |
| 2 | 拒绝 |
| 3 | 已取消 |
| 4 | 已完成 |

### 康复指导申请状态
| 状态码 | 说明 |
|--------|------|
| 0 | 待处理 |
| 1 | 已接受 |
| 2 | 已拒绝 |
| 3 | 已完成 |

### 职业病分类
| 分类编码 | 分类名称 |
|----------|----------|
| 0100000 | 职业性尘肺病及其他呼吸系统疾病 |
| 0200000 | 职业性皮肤病 |
| 0300000 | 职业性眼病 |
| 0400000 | 职业性耳鼻喉口腔疾病 |
| 0500000 | 职业性化学中毒 |
| 0600000 | 物理因素所致职业病 |
| 0700000 | 职业性放射性疾病 |
| 0800000 | 职业性传染病 |
| 0900000 | 职业性肿瘤 |
| 1000000 | 职业性肌肉骨骼疾病 |
| 1100000 | 职业性精神和行为障碍 |
| 1200000 | 其他职业病 |
