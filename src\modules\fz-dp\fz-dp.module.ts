import { Module } from '@nestjs/common';
import { FzDpController } from './fz-dp.controller';
import { FzDpService } from './fz-dp.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DpStatisticsModule } from '../dp-statistics/dp-statistics.module';
import { ScheduleModule } from '@nestjs/schedule';
import { DP_ZYWSDP_GDQZYWSFGTJ_LB } from './oracle/DP_ZYWSDP_GDQZYWSFGTJ_LB.entity';
import { DP_ZYWSDP_JCYRDWSLTJ } from './oracle/DP_ZYWSDP_JCYRDWSLTJ.entity';
import { DP_ZYWSDP_ZYWSKSGZL_LB } from './oracle/DP_ZYWSDP_ZYWSKSGZL_LB.entity';
import { DP_ZYWSDP_ZYJKJCTJ } from './oracle/DP_ZYWSDP_ZYJKJCTJ.entity';
import { DP_ZYWSDP_ZDWHYSJCTJ } from './oracle/DP_ZYWSDP_ZDWHYSJCTJ.entity';
import { DP_ZYWSDP_JCYRDWZYJKPXTJ } from './oracle/DP_ZYWSDP_JCYRDWZYJKPXTJ.entity';
import { DP_ZYWSDP_YRDWKZSTSTJ } from './oracle/DP_ZYWSDP_YRDWKZSTSTJ.entity';
import { DP_ZYWSDP_LDZZYBWHYSJCTJ } from './oracle/DP_ZYWSDP_LDZZYBWHYSJCTJ.entity';
// import { DP_ZYWSDP_JCYRDWFB } from './oracle/DP_ZYWSDP_JCYRDWFB.entity';
// import { DP_ZYWSDP_ZYWHYSFHTJ } from './oracle/DP_ZYWSDP_ZYWHYSFHTJ.entity';
// import { DP_X_ZYWSDP_ZYWSJCTJ } from './oracle/DP_X_ZYWSDP_ZYWSJCTJ.entity';
import { DP_ZYWSDP_YRDWZYWHYSSBTJ } from './oracle/DP_ZYWSDP_YRDWZYWHYSSBTJ.entity';
import { DP_ZYWSDP_JCYRDWHYTJ } from './oracle/DP_ZYWSDP_JCYRDWHYTJ.entity';
import { DP_ZYWSDP_JCYRDWGMTJ } from './oracle/DP_ZYWSDP_JCYRDWGMTJ.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature(
      [
        DP_ZYWSDP_GDQZYWSFGTJ_LB,
        DP_ZYWSDP_JCYRDWSLTJ,
        DP_ZYWSDP_ZYWSKSGZL_LB,
        DP_ZYWSDP_ZYJKJCTJ,
        DP_ZYWSDP_ZDWHYSJCTJ,
        DP_ZYWSDP_JCYRDWZYJKPXTJ,
        DP_ZYWSDP_YRDWKZSTSTJ,
        DP_ZYWSDP_LDZZYBWHYSJCTJ,
        // DP_ZYWSDP_JCYRDWFB,
        // DP_ZYWSDP_ZYWHYSFHTJ,
        // DP_X_ZYWSDP_ZYWSJCTJ,
        DP_ZYWSDP_YRDWZYWHYSSBTJ,
        DP_ZYWSDP_JCYRDWHYTJ,
        DP_ZYWSDP_JCYRDWGMTJ,
      ],
      'oracleConnection',
    ),
    DpStatisticsModule,
    ScheduleModule.forRoot(), // 定时任务
  ],
  controllers: [FzDpController],
  providers: [FzDpService],
})
export class FzDpModule {}
