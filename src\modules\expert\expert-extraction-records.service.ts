import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ExpertExtractionRecords } from './mysql/expert_extraction_records.entity';
import * as moment from 'moment';

@Injectable()
export class ExpertExtractionRecordsService {
  constructor(
    @InjectRepository(ExpertExtractionRecords, 'mysqlConnectionIservice')
    private readonly expertExtractionRecordsRepository: Repository<ExpertExtractionRecords>,
  ) {}

  // 新增专家抽取记录
  async create(
    record: Partial<ExpertExtractionRecords>,
  ): Promise<ExpertExtractionRecords> {
    const newRecord = this.expertExtractionRecordsRepository.create(record);
    return this.expertExtractionRecordsRepository.save(newRecord);
  }

  // 分页及关键词查询专家抽取记录
  async list(
    query: {
      pageSize?: number | string;
      curPage?: number | string;
      keyWord?: string;
    } = {},
  ): Promise<{ total: number; list: any[] }> {
    const limit = Number(query.pageSize) || 10;
    const curPage = Number(query.curPage) || 1;

    const queryBuilder =
      this.expertExtractionRecordsRepository.createQueryBuilder('record');

    if (query.keyWord) {
      queryBuilder.where(
        'record.management_element LIKE :keyWord OR record.work_item LIKE :keyWord OR record.expert_list LIKE :keyWord OR record.item_requirements LIKE :keyWord',
        {
          keyWord: `%${query.keyWord}%`,
        },
      );
    }

    const [list, total] = await queryBuilder
      .skip((curPage - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      total,
      list: list.map((ele) => {
        const { created_at, ...rest } = ele;
        return {
          ...rest,
          created_at: moment(created_at).format('YYYY-MM-DD HH:mm'),
        };
      }),
    };
  }
}
