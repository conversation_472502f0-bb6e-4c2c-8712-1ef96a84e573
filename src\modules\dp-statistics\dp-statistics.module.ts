import { Module } from '@nestjs/common';
import { DpStatisticsController } from './dp-statistics.controller';
import { DpStatisticsService } from './dp-statistics.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DpStatistical } from './dp-statistics.entity';

@Module({
  imports: [TypeOrmModule.forFeature([DpStatistical], 'mongodbConnection')],
  controllers: [DpStatisticsController],
  providers: [DpStatisticsService],
  exports: [DpStatisticsService],
})
export class DpStatisticsModule {}
