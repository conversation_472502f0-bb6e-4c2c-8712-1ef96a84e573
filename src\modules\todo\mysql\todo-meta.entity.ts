import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, OneToOne, JoinColumn } from 'typeorm';
import { Todo } from './todo.entity';

@Entity('todo_meta')
export class TodoMeta {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'todo_id' })
  todoId: number;

  @Column({ type: 'json', name: 'meta_data', nullable: true })
  metaData: any;

  @OneToOne(() => Todo, todo => todo.meta)
  @JoinColumn({ name: 'todo_id' })
  todo: Todo;
}
