import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

// 申报审核记录
@Entity('ng_monitor')
export class ngMonitor {
  @PrimaryGeneratedColumn({ type: 'int', comment: 'monitor_id' })
  id: number;

  @Column({ type: 'varchar', nullable: true, comment: '单位名称' })
  monitorName: string | null;

  @Column({ type: 'bigint', nullable: true, comment: '所属地区' })
  district_id: number | null;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '联系人' })
  contactor: string | null;

  @Column({ type: 'varchar', nullable: true, comment: '联系人电话' })
  contactorPhone: string | null;
}
