import { Entity, Column, PrimaryColumn } from 'typeorm';

@Entity('tj_health_check')
export class TjHealthCheck {
  @PrimaryColumn({ type: 'varchar', length: 64, comment: 'id' })
  id: string;

  @Column({
    type: 'varchar',
    length: 64,
    nullable: true,
    comment: '姓名',
  })
  xm: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '证件类型',
  })
  zjlx: string;

  @Column({ type: 'varchar', length: 18, nullable: true, comment: '证件号码' })
  zjhm: string;

  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
    comment: '出生日期',
  })
  csrq: string;

  @Column({ type: 'varchar', length: 2, nullable: true, comment: '性别' })
  xb: string;

  @Column({ type: 'varchar', length: 50, nullable: true, comment: '年龄' })
  nl: string;

  @Column({ type: 'varchar', length: 50, nullable: true, comment: '联系电话' })
  lxdh: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '用人单位社会信用代码',
  })
  yrdwshxydm: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '用工单位社会信用代码',
  })
  ygdwshxydm: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '是否复查：初检、复检',
  })
  sffc: string;

  @Column({ type: 'varchar', length: 50, nullable: true, comment: '监测种类' })
  jczl: string;

  @Column({ type: 'varchar', length: 50, nullable: true, comment: '工种' })
  gz: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '其他工种名称',
  })
  qtgzmc: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '接触危害因素',
  })
  jcwhys: string;

  @Column({ type: 'varchar', length: 50, nullable: true, comment: '监护种类' })
  jhzl: string;

  @Column({ type: 'date', comment: '体检日期' })
  tjrq: string;

  @Column({ type: 'varchar', length: 50, comment: '报告卡编号' })
  bgkbh: string;

  @Column({ type: 'varchar', length: 50, comment: '删除标志0-正常2-删除' })
  del_flag: string;

  @Column({ type: 'datetime', comment: '上传时间' })
  report_time: string;

  @Column({ type: 'varchar', length: 12, comment: '用人单位区划12位' })
  yrdwqh: string;

  @Column({ type: 'varchar', length: 255, comment: '用人单位名称' })
  yrdwmc: string;

  @Column({ type: 'varchar', length: 255, comment: '用人单位地址' })
  yrdwdz: string;

  @Column({ type: 'varchar', length: 255, comment: '用人单位区划名称' })
  yrdwqhmc: string;

  @Column({ type: 'varchar', length: 12, comment: '用工单位区划' })
  ygdwqh: string;

  @Column({ type: 'varchar', length: 255, comment: '用工单位名称' })
  ygdwmc: string;

  @Column({ type: 'varchar', length: 255, comment: '用工单位区划名称' })
  ygdwqhmc: string;

  @Column({ type: 'varchar', length: 255, comment: '检测机构' })
  jcjg: string;

  @Column({ type: 'varchar', length: 12, comment: '检测机构区划' })
  jcjgqh: string;

  @Column({ type: 'text', comment: '备注' })
  remark: string;
}
