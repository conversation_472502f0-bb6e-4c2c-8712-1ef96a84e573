import { END_POINT } from 'src/constants/endPoint';

export interface CreateSuperUserData {
  unifiedCode: string;
  userName: string;
  name: string;
  cname: string;
  phoneNum: string;
  landline: string;
  type: 1 | 3;
  area_code: string;
  group?: string;
}
export interface QuerySuperAccountData {
  unifiedCode: string;
  phoneNum: string;
  areaCode: string;
}

export interface CreateSuperAccountData {
  query: QuerySuperAccountData;
  org?: any;
  user: CreateSuperUserData;
}

export interface CreateAdminOrgData {
  cname: string;
  code: string;
  unitCode: string;
  phoneNum: string;
  adminArray: string[];
}

export interface CreateAdminUserData {
  phoneNum: string;
  name: string;
  userName: string;
  group?: string;
}

export interface CreateAdminAccountData {
  query: QueryAdminOrgData;
  org: CreateAdminOrgData;
  user: CreateAdminUserData;
}

export interface QueryAdminOrgData {
  code: string;
  phoneNum: string;
}

export interface CreatePhysicalExamOrgData {
  name: string;
  organization: string;
  phoneNum: string;
  status: 3;
}

export interface CreatePhysicalExamUserData {
  phoneNum: string;
  name: string;
  userName: string;
  org: string;
  org_id: string;
  group?: string;
}

export interface CreatePhysicalExamAccountData {
  query: QueryPhysicalExamOrgData;
  org: CreatePhysicalExamOrgData;
  user: CreatePhysicalExamUserData;
}

export interface QueryPhysicalExamOrgData {
  organization: string;
}

export interface CreateServiceOrgData {
  name: string;
  organization: string;
}

export interface CreateServiceUserData {
  phoneNum: string;
  name: string;
  userName: string;
  org?: string;
  org_id?: string;
  group?: string;
}

export interface QueryServiceOrgData {
  organization: string;
}

export interface CreateServiceAccountData {
  query: QueryServiceOrgData;
  org: CreateServiceOrgData;
  user: CreateServiceUserData;
}

export interface FindOrCreateMap {
  readonly [END_POINT.qy]: CreateAdminAccountData;
  readonly [END_POINT.jc]: CreateServiceAccountData;
  readonly [END_POINT.jg]: CreateSuperAccountData;
  readonly [END_POINT.tj]: CreatePhysicalExamAccountData;
}
