import { Entity, Column, PrimaryColumn } from 'typeorm';

// 行政区域编码
@Entity('dict_area')
export class DictArea {
  @PrimaryColumn({ type: 'varchar', length: 9, comment: '编码' })
  code: string;

  @Column({ type: 'varchar', length: 80, comment: '名称' })
  name: string;

  @Column({ type: 'varchar', length: 128, nullable: true, comment: '简称' })
  abbr_name?: string;

  @Column({ type: 'varchar', length: 9, nullable: true, comment: '父级编码' })
  parent_code?: string;
}
