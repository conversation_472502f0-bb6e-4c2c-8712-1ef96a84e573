import { Entity, Column, ManyTo<PERSON>ne, PrimaryColumn, Join<PERSON><PERSON>umn } from 'typeorm';
import { LIMS_ZYWS_SAMPLE } from './LIMS_ZYWS_SAMPLE.entity';
import { GlobalModule } from '../../../global.module';

const schema = (
  GlobalModule?.config['database_lab_oracle']?.database || 'SYSTEM'
).toUpperCase();

@Entity({
  name: 'LIMS_ZYWS_SAMPCHECKITEM',
  schema,
  comment: '职业卫生实验室-福州检测项目-检测数据表',
})
export class LIMS_ZYWS_SAMPCHECKITEM {
  @PrimaryColumn({
    type: 'varchar2',
    comment: '检测项目编号',
  })
  ITEMNO: string;

  @Column({ type: 'varchar2', primary: true, comment: '样品序号' })
  SAMPNO: string;

  // @Column({ type: 'varchar2', primary: true, comment: '实验序号' })
  // ResultSeq: string;

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 100,
    comment: '检验项目名称',
  })
  ITEMNAME: string;

  @Column({ type: 'number', nullable: true, comment: '检测结果值序号' })
  RESULTSEQ: number;

  @Column({ type: 'varchar2', nullable: true, length: 100, comment: '备注' })
  REMARK: string;

  @Column({
    type: 'date',
    nullable: true,
    comment: '操作时间',
  })
  OPERTIME: Date; //数据修改时间

  // fz 提供的参数
  @Column({
    type: 'varchar2',
    nullable: true,
    length: 800,
    comment: '检测结果',
  })
  CHECKRESULT: string; // 检测结果

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 100,
    comment: '结果单位',
  })
  MEASUREWORD: string; // 结果单位

  @Column({ type: 'varchar2', nullable: true, length: 8, comment: '是否合格' })
  IFOK: string; // 是否合格

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 200,
    comment: '检验依据',
  })
  CHECKBY: string; // 检验依据

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 100,
    comment: '检验方法',
  })
  WAYNAME: string; // 检验方法

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 1,
    comment: '是否现场检验结果',
  })
  IFSCENE: string; // 是否现场检验结果

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 100,
    comment: '检验项目状态',
  })
  STATE: string; // 检验项目状态

  @Column({ type: 'varchar2', nullable: true, length: 100, comment: '检验人' })
  REPMANID: string; // 检验人

  @Column({ type: 'date', nullable: true, comment: '检验时间' })
  REPDT: Date; // 检验时间

  @Column({ type: 'varchar2', nullable: true, length: 100, comment: '校核人' })
  QCEMPID: string; // 校核人

  @Column({ type: 'date', nullable: true, comment: '校核时间' })
  QCSIGNDT: Date; // 校核时间

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 100,
    comment: '校核结果',
  })
  QCRSLT: string; // 校核结果

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 100,
    comment: '校核描述',
  })
  QCDESC: string; // 校核描述

  @Column({ type: 'varchar2', nullable: true, length: 100, comment: '签发人' })
  CHIEFEMPID: string; // 签发人

  @Column({ type: 'date', nullable: true, comment: '签发时间' })
  CHIEFDT: Date; // 签发时间

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 100,
    comment: '签发结果',
  })
  CHIEFRSLT: string; // 签发结果

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 100,
    comment: '签发人意见',
  })
  CHIEFDESC: string; // 签发人意见

  @ManyToOne(
    () => LIMS_ZYWS_SAMPLE,
    (LIMS_ZYWS_SAMPLE) => LIMS_ZYWS_SAMPLE.LIMS_ZYWS_SAMPCHECKITEM,
  )
  @JoinColumn([{ name: 'SAMPNO', referencedColumnName: 'SAMPNO' }])
  SAMPNO2: LIMS_ZYWS_SAMPLE;
}
