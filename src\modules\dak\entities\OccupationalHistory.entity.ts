import { Entity, Column, ObjectIdColumn, CreateDateColumn, UpdateDateColumn, BeforeInsert } from 'typeorm';
import * as shortid from 'shortid';

// 危害因素接口
interface HarmFactor {
  _id: string;
  name: string; // 危害因素名称
  code: string; // 危害因素代码
  category: string; // 危害因素分类
}

/**
 * 职业病史实体
 */
@Entity('occupationalHistories')
export class OccupationalHistory {
  @ObjectIdColumn()
  _id: string;

  @Column({ comment: '员工ID', nullable: true })
  employeeId?: string;

  @Column({ comment: '入职时间', nullable: true })
  entryTime?: string;

  @Column({ comment: '离职时间', nullable: true })
  leaveTime?: string;

  @Column({ comment: '工作单位', nullable: true })
  workUnit?: string;

  @Column({ comment: '车间', nullable: true })
  workshop?: string;

  @Column({ comment: '岗位', nullable: true })
  station?: string;

  @Column({ comment: '工种', nullable: true })
  workType?: string;

  @Column({ comment: '危害因素' })
  harmFactors: HarmFactor[] = [];

  // 以下字段未使用

  @Column({ comment: '接触有害因素', nullable: true })
  exposureToHarmfulFactors?: string;

  @Column({ comment: '职业史类型：1.放射/2.非放射', nullable: true })
  hisType?: '1' | '2';

  // 放射相关字段（当 hisType 为 '1' 时必填）
  @Column({ comment: '(放射)每日工作时数或工作量', nullable: true })
  prfwrklod?: string;

  @Column({ comment: '(放射)职业史累积受照剂量', nullable: true })
  prfshnvlu?: string;

  @Column({ comment: '(放射)职业史过量照射史', nullable: true })
  prfexcshn?: string;

  @Column({ comment: '(放射)职业照射种类', nullable: true })
  prfraysrt2?: string;

  @Column({ comment: '(放射)职业照射种类代码', nullable: true })
  prfraysrtcods?: string;

  @Column({ comment: '(放射)放射线种类', nullable: true })
  fsszl?: string;

  // 非放射相关字段（当 hisType 为 '2' 时必填）
  @Column({ comment: '(非放射)防护措施', nullable: true })
  defendStep?: string;

  @Column({ comment: '检查日期' })
  chkdat: string;

  @Column({ comment: '检查医生', nullable: true })
  chkdoct?: string;

  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date;

  @BeforeInsert()
  setDefaults() {
    if (!this._id) {
      this._id = shortid.generate();
    }
    const now = new Date();
    this.createdAt = now;
    this.updatedAt = now;
  }
}
