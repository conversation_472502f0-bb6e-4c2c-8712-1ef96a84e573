import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { DiseaseCategoryDict } from './disease-category-dict.entity';

@Entity('appointment')
export class Appointment {
  @PrimaryGeneratedColumn({ name: 'id', type: 'int', comment: 'ID' })
  id: number;

  @Column({ type: 'varchar', length: 18, comment: '身份证号' })
  @Index('idx_id_card')
  id_card: string;
  // name
  @Column({ type: 'varchar', length: 50, comment: '姓名' })
  name: string;

  @Column({ type: 'bigint', comment: '机构ID' })
  @Index('idx_inst_id')
  inst_id: number;

  @Column({
    type: 'bigint',
    nullable: true,
    comment: '医师ID（可为空表示不指定医师）',
  })
  @Index('idx_doctor_id')
  doctor_id: number;

  @Column({ type: 'datetime', comment: '预约日期时间' })
  @Index('idx_appt_date')
  appt_date: Date;

  @Column({
    type: 'tinyint',
    default: 0,
    comment: '状态：0-待审核，1-通过，2-拒绝，3-已取消，4-已完成',
  })
  @Index('idx_status')
  status: number;

  @Column({
    type: 'char',
    length: 7,
    nullable: true,
    comment: '职业病病人分类编码',
  })
  @Index('idx_disease_category')
  disease_category: string;

  @ManyToOne(() => DiseaseCategoryDict)
  @JoinColumn({
    name: 'disease_category',
    referencedColumnName: 'category_code',
  })
  disease_category_relation: DiseaseCategoryDict;

  @Column({ type: 'text', nullable: true, comment: '预约服务类型描述' })
  service_type: string;

  @Column({ type: 'varchar', length: 50, nullable: true, comment: '预约科室' })
  department: string;

  @Column({ type: 'text', nullable: true, comment: '预约需求' })
  requirement: string;

  @Column({ type: 'varchar', length: 500, nullable: true, comment: '拒绝原因' })
  reject_reason: string;

  @Column({
    type: 'tinyint',
    width: 1,
    default: 0,
    comment: '是否删除：0-否，1-是',
  })
  @Index('idx_is_deleted')
  is_deleted: boolean;

  @Column({
    type: 'datetime',
    default: () => 'CURRENT_TIMESTAMP',
    comment: '创建时间',
  })
  create_time: Date;

  @Column({
    type: 'datetime',
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
    comment: '更新时间',
  })
  update_time: Date;
}
