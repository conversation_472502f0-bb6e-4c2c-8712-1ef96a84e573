import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { LIMS_ZYWS_SAMPCHECKITEM } from './LIMS_ZYWS_SAMPCHECKITEM.entity';
import { LIMS_ZYWS_MREGIST } from './LIMS_ZYWS_MREGIST.entity';
import { GlobalModule } from '../../../global.module';

const schema = (
  GlobalModule?.config['database_lab_oracle']?.database || 'SYSTEM'
).toUpperCase();

@Entity({
  name: 'LIMS_ZYWS_SAMPLE',
  schema,
  comment: '职业卫生实验室-福州检测项目-样品数据表',
})
export class LIMS_ZYWS_SAMPLE {
  @PrimaryColumn({ type: 'varchar2', comment: '样品序号' })
  SAMPNO: string;

  @Column({
    type: 'number',
    precision: 12,
    comment: '登记序号',
  })
  REGGUID: number;

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 100,
    comment: '自编号',
  })
  SELFNO: string;

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 100,
    comment: '样品类别名称',
  })
  SAMPCLASSSEQNM: string;

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 200,
    comment: '样品名称',
  })
  SAMPNAME: string;

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 200,
    comment: '样品性状',
  })
  FIGURE: string;

  @Column({ type: 'varchar2', nullable: true, length: 200, comment: '批号' })
  BATNO: string;

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 200,
    comment: '生产厂家',
  })
  FACTORY: string;

  @Column({
    type: 'number',
    nullable: true,
    precision: 12,
    comment: '样品数量',
  })
  AMOUNT: number;

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 200,
    comment: '计量单位',
  })
  MEASUREWORD: string;

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 200,
    comment: '采样地点',
  })
  GETPOS: string;

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 200,
    comment: '样品颜色',
  })
  SAMPCOLOR: string;

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 200,
    comment: '任务编号',
  })
  SAMPCOUNNO: string;

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 200,
    comment: '样品规格',
  })
  SPECS: string;

  @Column({ type: 'varchar2', nullable: true, length: 200, comment: '包装' })
  PACKING: string;

  @Column({ type: 'varchar2', nullable: true, length: 200, comment: '商标' })
  TRADEMARK: string;

  @Column({ type: 'varchar2', nullable: true, length: 200, comment: '备注' })
  REMARK: string;

  @Column({ type: 'date', nullable: true, comment: '采样开始时间' })
  BEGINTIME: Date;

  @Column({ type: 'date', nullable: true, comment: '采样结束时间' })
  ENDTIME: Date;

  @Column({ type: 'varchar2', nullable: true, length: 200, comment: '温度' })
  TEMPER: string;

  @Column({ type: 'varchar2', nullable: true, length: 200, comment: '湿度' })
  HUMIDITY: string;

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 200,
    comment: '风速',
  })
  FWINDSPEED: string;

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 200,
    comment: '采样流量',
  })
  INSTFLUX: string;

  @Column({
    type: 'varchar2',
    nullable: true,
    length: 200,
    comment: '采样体积',
  })
  SAMPVOL: string;

  @ManyToOne(() => LIMS_ZYWS_MREGIST, (mRegist) => mRegist.LIMS_ZYWS_SAMPLE)
  @JoinColumn({ name: 'REGGUID', referencedColumnName: 'REGGUID' })
  REGGUID2: LIMS_ZYWS_MREGIST;

  @OneToMany(
    () => LIMS_ZYWS_SAMPCHECKITEM,
    (samCheckItem) => samCheckItem.SAMPNO2,
  )
  LIMS_ZYWS_SAMPCHECKITEM: LIMS_ZYWS_SAMPCHECKITEM[];
}
