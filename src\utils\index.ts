import * as moment from 'moment';

export function success(data: any, msg?: string) {
  return {
    code: 200,
    message: msg || '请求成功',
    data,
  };
}

export function error(msg?: string, code = 400, data = null) {
  return {
    code,
    message: msg || '请求失败',
    data,
  };
}

export function wrapperResponse(p, msg) {
  return p
    .then((data: any) => success(data, msg))
    .catch((err: any) => error(err ? err.message : '请求失败'));
}

// 时间格式化数据
export function timeFormatter(data: any) {
  if (Array.isArray(data)) {
    // 数组
    data.forEach((ele) => {
      if (ele.createdAt) {
        ele.createdAt = moment(ele.createdAt).format('YYYY-MM-DD HH:mm');
      }
      if (ele.updatedAt) {
        ele.updatedAt = moment(ele.updatedAt).format('YYYY-MM-DD HH:mm');
      }
      if (ele.releaseTime) {
        ele.releaseTime = moment(ele.releaseTime).format('YYYY-MM-DD HH:mm');
      }
      if (ele.readTime) {
        ele.readTime = moment(ele.readTime).format('YYYY-MM-DD HH:mm');
      }
      if (ele.time) {
        if (typeof ele.time === 'number' && ele.time.toString().length === 10) {
          ele.time = ele.time * 1000;
        }
        ele.time = moment(ele.time).format('YYYY-MM-DD HH:mm:ss');
      }
    });
    return data;
  } else if (typeof data === 'object' && data !== null) {
    // 对象
    if (data.createdAt) {
      data.createdAt = moment(data.createdAt).format('YYYY-MM-DD HH:mm');
    }
    if (data.updatedAt) {
      data.updatedAt = moment(data.updatedAt).format('YYYY-MM-DD HH:mm');
    }
    if (data.readTime) {
      data.readTime = moment(data.readTime).format('YYYY-MM-DD HH:mm');
    }
    if (data.startTime) {
      data.startTime = moment(data.startTime).format('YYYY-MM-DD HH:mm');
    }
    if (data.endTime) {
      data.endTime = moment(data.endTime).format('YYYY-MM-DD HH:mm');
    }
    return data;
  } else {
    // 其他
    return data;
  }
}

// 验证手机号
export function isValidPhone(phone: string): boolean {
  const regex = /^1[3-9]\d{9}$/;
  return regex.test(phone);
}
// 生成6位随机数
export function generateCode(): string {
  const code = Math.floor(Math.random() * 900000) + 100000;
  return code.toString();
}
