import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';
@Entity({
  name: 'DP_X_ZYWSDP_ZYWSJCTJ', // 没有统计数据，跟DP_ZYWSDP_ZYJKJCTJ一样
  comment: '新-职业卫生大屏-职业健康检查统计',
})
export class DP_X_ZYWSDP_ZYWSJCTJ {
  @PrimaryGeneratedColumn()
  ID: number;

  @Column({ type: 'varchar2', length: 255, comment: '地区' })
  DQ: string;

  @Column({ type: 'varchar2', length: 255, comment: '年份' })
  NF: string;

  @Column({ type: 'varchar2', length: 255, comment: '应检人数' })
  YJRS: string;

  @Column({ type: 'varchar2', length: 255, comment: '体检率' })
  TJL: string;

  @Column({ type: 'varchar2', length: 255, comment: '应复查人数' })
  YFCRS: string;

  @Column({ type: 'varchar2', length: 255, comment: '复查完成率' })
  FCWWCL: string;

  @Column({ type: 'varchar2', length: 255, comment: '体检异常率' })
  TJYCL: string;
}
