import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { AssessmentModel } from './assessment-model.entity';

// 考核指标表 mysql
// 该表用于存储考核指标的相关信息
@Entity('assessment_info')
export class AssessmentInfo {
  @PrimaryGeneratedColumn({ name: 'id', type: 'int', comment: '自增ID' })
  id: number;

  @Column({ comment: '评估模型id', type: 'int', default: null, nullable: true })
  assessment_model_id: number;

  @Column({ comment: '父级id', type: 'int', default: null, nullable: true })
  indicator_id: number;

  @Column({ comment: '分值', type: 'int' })
  score: number;

  @Column({
    type: 'json',
    // enum: ['0', '1', '2'],
    comment: '复核方式 资料审查 现场勘察 访谈',
  })
  review_mode: object;

  @Column({ comment: '赋分标准', type: 'text' })
  score_criteria: string;

  @Column({
    type: 'enum',
    enum: [
      '普通指标',
      '存在职业危害因素企业的特有指标',
      '企业内部设置食堂或餐厅',
    ],
    comment: '指标类别',
  })
  indicator_type: string;

  @Column({ comment: '排序值', type: 'int' })
  order: number;

  // @ManyToOne(
  //   () => AssessmentModel,
  //   (assessmentModel) => assessmentModel.assessment_info,
  // )
  // @JoinColumn({ name: 'assessment_model_id' })
  // assessmentModel: AssessmentModel;
}
