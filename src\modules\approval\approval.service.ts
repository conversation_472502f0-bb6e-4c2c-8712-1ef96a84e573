import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Approval } from './mysql/approval.entity';
import { ApprovalApprovers } from './mysql/approval-approvers.entity';
import { CreateApprovalDto } from './dto/create-approval.dto';
import { UpdateApprovalDto } from './dto/update-approval.dto';
import { FindApprovalDto } from './dto/find-approval.dto';
import { DATA_SOURCE_NAME } from './constants';
import { NotFoundException } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { TodoEventType } from '../todo/constants';
import { TodoService } from '../todo/todo.service';
import { ApprovalTodo } from './mysql/approval-todo.entity';

@Injectable()
export class ApprovalService {
  constructor(
    @InjectRepository(Approval, DATA_SOURCE_NAME)
    private readonly approvalRepository: Repository<Approval>,
    @InjectRepository(ApprovalApprovers, DATA_SOURCE_NAME)
    private readonly approvalApproversRepository: Repository<ApprovalApprovers>,
    @InjectRepository(ApprovalTodo, DATA_SOURCE_NAME)
    private readonly approvalTodoRepository: Repository<ApprovalTodo>,
    private readonly todoService: TodoService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async create(createApprovalDto: CreateApprovalDto, userId: string): Promise<Approval> {
    // 开启事务
    const queryRunner = this.approvalRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 创建审批记录
      const { approvers, ...approvalData } = createApprovalDto;
      const approval = queryRunner.manager.create(Approval, {
        ...approvalData,
        status: 0,
        created_by: userId,
        created_at: new Date(),
        updated_at: new Date(),
      });
      const savedApproval = await queryRunner.manager.save(approval);

      // 创建审批人记录
      const approverEntities = approvers.map(approver => {
        const approverEntity = new ApprovalApprovers();
        approverEntity.approval = savedApproval;
        approverEntity.approver_id = approver.id;
        approverEntity.approver_name = approver.name;
        return approverEntity;
      });
      await queryRunner.manager.save(approverEntities);

      await queryRunner.commitTransaction();

      // 为每个审批人创建待办
      const savedApprovalWithApprovers = await this.findOne(savedApproval.id, userId);
      // await this.emitTodoCreateEvent(savedApprovalWithApprovers);
      await this.createTodo(savedApprovalWithApprovers);

      return savedApprovalWithApprovers;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async findAll(query: FindApprovalDto, userId: string): Promise<any> {
    const { page = 1, pageSize = 10, title, description } = query;
    const skip = (page - 1) * pageSize;

    const queryBuilder = this.approvalRepository.createQueryBuilder('approval')
      .leftJoinAndSelect('approval.approvers', 'approvers')
      .where('approval.created_by = :userId', { userId })
      .skip(skip)
      .take(pageSize);

    if (title) {
      queryBuilder.andWhere('approval.title LIKE :title', { title: `%${title}%` });
    }

    if (description) {
      queryBuilder.andWhere('approval.description LIKE :description', { description: `%${description}%` });
    }

    if (query.status) {
      queryBuilder.andWhere('approval.status = :status', { status: query.status });
    }

    const [items, total] = await queryBuilder.getManyAndCount();

    return {
      items: items.map((item) => ({
        id: item.id,
        title: item.title,
        description: item.description,
        link: item.link,
        status: item.status,
        approvers: item.approvers.map((approver) => ({
          id: approver.id,
          name: approver.approver_name,
        })),
        createdAt: item.created_at,
        updatedAt: item.updated_at,
      })),
      meta: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize),
      }
    };
  }

  async findOne(id: number, userId: string): Promise<Approval> {
    const approval = await this.approvalRepository.findOne({
      where: { 
        id,
        created_by: userId 
      },
      relations: ['approvers'],
    });

    if (!approval) {
      throw new NotFoundException(`未找到ID为${id}的审批记录或无权限查看`);
    }

    return approval;
  }

  async update(id: number, updateApprovalDto: UpdateApprovalDto, userId: string): Promise<Approval> {
    // 检查记录是否存在且属于当前用户
    const approval = await this.findOne(id, userId);

    const queryRunner = this.approvalRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const { approvers, ...approvalData } = updateApprovalDto;
      
      // 更新审批基本信息
      if (Object.keys(approvalData).length > 0) {
        await queryRunner.manager.update(Approval, id, {
          ...approvalData,
          updated_at: new Date(),
        });
      }

      // 如果提供了approvers，则更新审批人
      if (approvers) {
        // 删除原有审批人
        await queryRunner.manager.delete(ApprovalApprovers, { approval_id: id });

        // 创建新的审批人记录
        const approverEntities = approvers.map(approver => {
          const approverEntity = new ApprovalApprovers();
          approverEntity.approval = approval;
          approverEntity.approver_id = approver.id;
          approverEntity.approver_name = approver.name;
          return approverEntity;
        });
        await queryRunner.manager.save(approverEntities);
      }

      await queryRunner.commitTransaction();
      return this.findOne(id, userId);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async remove(id: number, userId: string): Promise<void> {
    // 检查记录是否存在且属于当前用户
    await this.findOne(id, userId);

    const queryRunner = this.approvalRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 先删除审批人记录
      await queryRunner.manager.delete(ApprovalApprovers, { approval_id: id });
      // 再删除审批记录
      await queryRunner.manager.delete(Approval, id);

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // async approveByRelatedId(relatedId: string): Promise<Approval> {
  //   const approval = await this.approvalRepository.findOne({
  //     where: { relatedId },
  //   });

  //   if (!approval) {
  //     throw new NotFoundException(`Approval with related ID ${relatedId} not found`);
  //   }

  //   approval.status = 1;
  //   approval.updated_at = new Date();
  //   return this.approvalRepository.save(approval);
  // }

  // async rejectByRelatedId(relatedId: string): Promise<Approval> {
  //   const approval = await this.approvalRepository.findOne({
  //     where: { relatedId },
  //   });

  //   if (!approval) {
  //     throw new NotFoundException(`Approval with related ID ${relatedId} not found`);
  //   }

  //   approval.status = 2;
  //   approval.updated_at = new Date();
  //   return this.approvalRepository.save(approval);
  // }

  async approve(id: number): Promise<Approval> {
    return this.updateApprovalStatus(id, 1);
  }

  async reject(id: number): Promise<Approval> {
    return this.updateApprovalStatus(id, 2);
  }

  private async updateApprovalStatus(id: number, status: number): Promise<Approval> {
    const queryRunner = this.approvalRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const approval = await this.approvalRepository.findOne({
        where: { id },
      });

      if (!approval) {
        throw new NotFoundException(`Approval with ID ${id} not found`);
      }

      // Find associated todos
      const approvalTodos = await this.approvalTodoRepository.find({
        where: { approvalId: id },
        relations: ['todo'],
      });

      // Complete each todo
      for (const approvalTodo of approvalTodos) {
        await this.todoService.completeOne(approvalTodo.todoId, { id: approvalTodo.todo.userId });
      }

      // Update approval status
      approval.status = status;
      approval.updated_at = new Date();
      await this.approvalRepository.save(approval);

      await queryRunner.commitTransaction();
      return approval;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  private async createTodo(approval: Approval): Promise<void> {
    const queryRunner = this.approvalRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create todos for each approver
      const todoPromises = approval.approvers.map(async approver => {
        // Create todo
        const todo = await this.todoService.create({
          type: 'APPROVE',
          title: approval.title,
          description: approval.description,
          meta: {
            link: approval.link
          }
        }, {
          id: approver.approver_id
        });

        // Create approval_todo record
        const approvalTodo = this.approvalTodoRepository.create({
          approvalId: approval.id,
          todoId: todo.id,
        });
        await this.approvalTodoRepository.save(approvalTodo);

        return todo;
      });

      await Promise.all(todoPromises);
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  private async emitTodoCreateEvent(approval: Approval): Promise<void> {
    // 为每个审批人创建待办
    const promises = approval.approvers.map(approver => {
      return this.eventEmitter.emit(TodoEventType.CREATE, {
        type: 'APPROVE',
        title:  approval.title,
        description: approval.description,
        userId: approver.approver_id,
        meta: {
          link: approval.link
        }
      });
    });

    await Promise.all(promises);
  }
}