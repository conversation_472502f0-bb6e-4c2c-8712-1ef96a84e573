import {
  Entity,
  Column,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
// 用户角色关联
@Entity('t_user_role')
@Index('idx_t_user_role', ['user_type', 'user_id'])
export class TUserRole {
  @PrimaryColumn({ type: 'varchar', length: 64, comment: 'ID' })
  id: string;

  @Column({
    type: 'varchar',
    length: 100,
    default: 'SysUser',
    comment: '用户类型',
  })
  user_type: string;

  @Column({ type: 'varchar', length: 64, comment: '用户ID' })
  user_id: string;

  @Column({ type: 'varchar', length: 64, comment: '角色ID' })
  role_id: string;

  @CreateDateColumn({ type: 'timestamp', comment: '创建时间' })
  create_at: Date;

  @Column({ type: 'varchar', length: 256, nullable: true })
  create_by: string;

  @UpdateDateColumn({ type: 'timestamp', comment: '更新时间' })
  update_at: Date;

  @Column({ type: 'varchar', length: 256, nullable: true })
  update_by: string;
}
