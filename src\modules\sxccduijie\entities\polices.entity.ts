import { Entity, ObjectIdColumn, Column, BeforeInsert } from 'typeorm';
import * as shortid from 'shortid';
@Entity('policies')
export class Policy {
  @ObjectIdColumn()
  _id: string;

  @Column({ type: 'int', default: 0 })
  sortId: number;

  @Column({ type: 'array', default: [] })
  user_ids: string[];

  @Column({ type: 'array', default: [] })
  enterprise_ids: string[];

  @Column({ type: 'array', default: [] })
  dingtree_ids: string[];

  @Column({ type: 'array', default: [] })
  millConstruction_ids: string[];

  @Column({ type: 'boolean', default: true })
  enable: boolean;

  @Column('varchar')
  name: string;

  @Column('varchar')
  scope_type: string;

  @Column('varchar')
  lastOperator: string;

  @Column({ type: 'date', default: new Date() })
  createdAt: Date;

  @Column({ type: 'date', default: new Date() })
  updatedAt: Date;

  @Column({ type: 'boolean', default: false })
  isSuper: boolean;

  @Column({ type: 'array', default: [] })
  group_ids: string[];

  @BeforeInsert()
  setDefaultId() {
    if (!this._id) {
      this._id = shortid.generate();
    }
  }
}
