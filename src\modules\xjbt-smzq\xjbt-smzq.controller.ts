import { Controller, Post, Body } from '@nestjs/common';
import { XjbtSmzqService } from './xjbt-smzq.service';
import { CreateAppointmentDto } from './dto/create-appointment.dto';
import { CreateRehabGuideApplicationDto } from './dto/create-rehab-guide-application.dto';
import { wrapperResponse } from 'src/utils';

@Controller('xjbt-smzq')
export class XjbtSmzqController {
  constructor(private readonly xjbtSmzqService: XjbtSmzqService) {}

  @Post('appointments')
  createAppointment(@Body() createAppointmentDto: CreateAppointmentDto) {
    return wrapperResponse(
      this.xjbtSmzqService.createAppointment(createAppointmentDto),
      '创建预约',
    );
  }

  @Post('rehab-guide-application')
  createRehabGuideApplication(
    @Body() createRehabGuideApplicationDto: CreateRehabGuideApplicationDto,
  ) {
    return wrapperResponse(
      this.xjbtSmzqService.createRehabGuideApplication(
        createRehabGuideApplicationDto,
      ),
      '创建康复指导申请',
    );
  }
}
