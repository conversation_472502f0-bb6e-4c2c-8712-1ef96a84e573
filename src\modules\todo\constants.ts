export const DATA_SOURCE_NAME = 'mysqlConnectionIservice';

export enum TodoEventType {
  CREATE = 'todo.create',
  CREATED = 'todo.created',
  COMPLETED = 'todo.completed',
}

export interface TodoEventPayload {
  type: string;
  title: string;
  description?: string;
  meta?: any;
  userId: string;
}

export interface TodoItem {
  id: number;
  type: string;
  title: string;
  status: number;
  createdAt: Date;
  description?: string;
  meta?: any;
}

export interface TodoPagination {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}
