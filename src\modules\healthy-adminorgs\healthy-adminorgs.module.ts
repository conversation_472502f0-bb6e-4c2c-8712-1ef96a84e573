import { Module } from '@nestjs/common';
import { HealthyAdminorgsService } from './healthy-adminorgs.service';
import { HealthyAdminorgsController } from './healthy-adminorgs.controller';
import { TypeOrmModule } from '@nestjs/typeorm';

import { IndicatorClassify } from './mysql/indicator-classify.entity';
import { Indicator } from './mysql/indicator.entity';
import { AssessmentModel } from './mysql/assessment-model.entity';
import { BasicConditions } from './mysql/basic-conditions.entity';
import { AssessmentInfo } from './mysql/assessment-info.entity';
import { Declaration } from './mysql/declaration.entity';
import { Attachment } from './mysql/attachment.entity';
import { DeclarationBasicCondition } from './mysql/declaration_basic_condition.entity';
import { DeclarationIndicator } from './mysql/declaration_indicator.entity';

@Module({
  imports: [
    // 不指定数据源，使用默认数据源
    TypeOrmModule.forFeature(
      [
        IndicatorClassify,
        Indicator,
        AssessmentModel,
        BasicConditions,
        AssessmentInfo,
        Declaration,
        Attachment,
        DeclarationBasicCondition,
        DeclarationIndicator,
      ],
      'mysqlConnectionXjbtJkqy',
    ),
  ],
  controllers: [HealthyAdminorgsController],
  providers: [HealthyAdminorgsService],
})
export class HealthyAdminorgsModule {}
