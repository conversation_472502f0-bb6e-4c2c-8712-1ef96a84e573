import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ngDeclaration } from './mysql/ng_declaration.entity';
import { NgEmployer } from './mysql/ng_employer.entity';
import { ngAudit } from './mysql/ng_audit.entity';
import { DimEmployerIndustry } from './mysql/dim_employer_industry.entity'; // 行业分类
import { DimDistrict } from './mysql/dim_district.entity';

// 用人单位表
@Injectable()
export class EmployerService {
  constructor(
    @InjectRepository(NgEmployer, 'mysqlConnectionHebei')
    private readonly ngEmployerRepository: Repository<NgEmployer>,
    @InjectRepository(ngDeclaration, 'mysqlConnectionHebei')
    private readonly ngDeclarationRepository: Repository<ngDeclaration>,
    @InjectRepository(ngAudit, 'mysqlConnectionHebei')
    private readonly ngAuditRepository: Repository<ngAudit>,
    @InjectRepository(DimEmployerIndustry, 'mysqlConnectionHebei')
    private readonly dimEmployerIndustryRepository: Repository<DimEmployerIndustry>,
    @InjectRepository(DimDistrict, 'mysqlConnectionHebei')
    private readonly dimDistrictRepository: Repository<DimDistrict>,
  ) {}

  // 获取企业最近的一次申报大于11个月和12个月的数据
  async getLatestDeclarations(): Promise<any> {
    const employerList = await this.ngEmployerRepository
      .createQueryBuilder('ng_employer')
      .where('ng_employer.last_declared_id IS NOT NULL')
      .getMany();

    const sbOver11MonthEmployers = [],
      sbOver12MonthEmployers = [];
    const len = 10;
    for (let i = 0; i < len; i++) {
      const employer = employerList[i];
      if (employer.employerStatus === 0) continue; // 注销的企业不算预警
      const ngAudit = await this.ngAuditRepository.findOne({
        where: { declaration_id: employer.lastDeclaredId, audit_status: 1 },
      });
      if (!ngAudit) continue; // 未审核通过的不算预警
      const latestDeclaration = await this.ngDeclarationRepository.findOne({
        where: { id: employer.lastDeclaredId },
        select: ['declaration_date'],
      });
      if (latestDeclaration && latestDeclaration.declaration_date) {
        const lastDeclaredDate = new Date(latestDeclaration.declaration_date);
        const comparisonTime1 = new Date().setMonth(new Date().getMonth() - 11);
        const comparisonTime2 = new Date().setMonth(new Date().getMonth() - 12);
        if (lastDeclaredDate.getTime() < comparisonTime2) {
          sbOver12MonthEmployers.push({ ...employer, lastDeclaredDate });
        } else if (lastDeclaredDate.getTime() < comparisonTime1) {
          sbOver11MonthEmployers.push({ ...employer, lastDeclaredDate });
        }
      }
    }

    // 未申报的企业
    const notDeclaredEmployers = await this.ngEmployerRepository
      .createQueryBuilder('ng_employer')
      .where('ng_employer.last_declared_id IS NULL')
      .getMany();

    const allEmployers = await this.ngEmployerRepository.find();

    return {
      sbOver11MonthEmployers,
      sbOver12MonthEmployers,
      notDeclaredEmployers,
      allEmployers,
    };
  }

  // 监管端 - 申报查询统计
  async getDeclarationStatistics(query: {
    type: string;
    startTime?: string; // '2023-01-01'
    endTime?: string;
    industryId?: string;
    economicId?: string;
    hazardousFactors?: string;
    districtId?: string;
  }): Promise<any> {
    const {
      type,
      districtId,
      startTime,
      endTime,
      industryId,
      economicId,
      hazardousFactors,
    } = query;
    if (!type || !['1', '2', '3'].includes(type))
      throw new Error('type is required');

    const qb = this.ngDeclarationRepository
      .createQueryBuilder('declaration')
      .leftJoinAndSelect('declaration.latestAudit', 'ng_audit')
      .where('ng_audit.id IS NOT NULL')
      .andWhere('declaration.del_flag != :del_flag', { del_flag: 1 });

    if (districtId) {
      let district = districtId.replace(/0+$/, '');
      if ([3, 5].includes(district.length)) district = district + '0';
      qb.andWhere('CAST(declaration.district_id AS CHAR) LIKE :districtId', {
        districtId: `${district}%`,
      });
    }
    // if (type !== '1' && industryId) {
    //   const industryChildren = await this.findIndustries(industryId);
    //   const industryChildrenCodes = industryChildren
    //     .map((ele) => ele.childrenCodes)
    //     .flat();
    //   qb.andWhere('declaration.industry_id IN (:...industryChildrenCodes)', {
    //     industryChildrenCodes,
    //   });
    // }
    if (economicId) {
      qb.andWhere('declaration.economic_id = :economicId', { economicId });
    }
    if (hazardousFactors) {
      qb.andWhere(`declaration.${hazardousFactors} = 1`);
    }
    if (startTime && endTime) {
      qb.andWhere(
        'declaration.declaration_date BETWEEN :startTime AND :endTime',
        { startTime, endTime },
      );
    }
    if (type !== '1') {
      qb.orderBy('declaration.declaration_date', 'DESC'); // 按照 declaration_date 逆序排序
    }
    let declarationList = await qb
      .select([
        'declaration.id',
        'declaration.employer_name',
        'declaration.unified_code',
        'declaration.declaration_date',
        'declaration.declaration_type',
        'declaration.district_id', // 申报区域
        'declaration.industry_id', // 行业分类
        'declaration.employee_num', // 在册员工数
        'declaration.outsourced_num', // 外委人员数
        'declaration.exposed_num', // 接害人员总数
        'declaration.patient_num', // 职业病累计人数
        'ng_audit.audit_status',
      ])
      .getMany();

    // 1、按申报审核结果统计
    if (type === '1') {
      declarationList = declarationList.map((ele) => ({
        ...ele,
        audit_status: ele.latestAudit.audit_status,
      }));
      const approvedList = declarationList.filter(
        (ele) => ele.latestAudit.audit_status === 1,
      );
      const reviewFailed = declarationList.filter(
        (ele) => ele.latestAudit.audit_status === 2,
      );

      // 审核结果统计
      const reviewResults = {
        total: declarationList.length,
        approved: approvedList.length,
        reviewFailed: reviewFailed.length,
        notReviewed:
          declarationList.length - approvedList.length - reviewFailed.length,
      };

      return {
        reviewResults,
        declarationList,
      };
    }

    // 对declarationList中的企业去重，且只取最近一次的申报数据
    const declarationObj = declarationList.reduce((acc, item) => {
      if (!acc[item.unified_code]) {
        acc[item.unified_code] = item;
      }
      return acc;
    }, {});
    const declarationList2: ngDeclaration[] = Object.values(declarationObj);

    // 2、按行业分类统计
    if (type === '2') {
      const subIndustry = await this.findIndustries(industryId);
      let abnormalList2 = JSON.parse(JSON.stringify(declarationList2));
      const result = subIndustry.map((sub) => {
        const subList = abnormalList2.filter((ele) =>
          sub.childrenCodes.includes(ele.industry_id),
        );
        const subIds = subList.map((ele) => ele.id);
        abnormalList2 = abnormalList2.filter((ele) => !subIds.includes(ele.id));
        return {
          name: sub.name,
          employer_num: subList.length,
          employee_num: subList.reduce((acc, ele) => acc + ele.employee_num, 0),
          outsourced_num: subList.reduce(
            (acc, ele) => acc + ele.outsourced_num,
            0,
          ),
          exposed_num: subList.reduce((acc, ele) => acc + ele.exposed_num, 0),
          patient_num: subList.reduce((acc, ele) => acc + ele.patient_num, 0),
        };
      });
      if (!industryId && abnormalList2.length > 0) {
        result.push({
          name: '其他',
          employer_num: abnormalList2.length,
          employee_num: abnormalList2.reduce(
            (acc, ele) => acc + ele.employee_num,
            0,
          ),
          outsourced_num: abnormalList2.reduce(
            (acc, ele) => acc + ele.outsourced_num,
            0,
          ),
          exposed_num: abnormalList2.reduce(
            (acc, ele) => acc + ele.exposed_num,
            0,
          ),
          patient_num: abnormalList2.reduce(
            (acc, ele) => acc + ele.patient_num,
            0,
          ),
        });
        const abnormalIndustry = abnormalList2.map((ele) => ele.industry_id);
        return {
          declarationList: result,
          abnormalIndustry: [...new Set(abnormalIndustry)],
        };
      }
      return { declarationList: result };
    }
    // 3、按行政区域统计
    return { declarationList: declarationList2 };
  }

  // 查询 DimEmployerIndustry 行业分类表中的子集数据
  async findIndustries(industryId?: string): Promise<any[]> {
    const qb =
      this.dimEmployerIndustryRepository.createQueryBuilder('industry');
    let checkedIndustry = {};
    if (industryId) {
      const industry = await this.dimEmployerIndustryRepository
        .createQueryBuilder('industry')
        .where('industry.id = :industryId OR industry.code = :industryId', {
          industryId,
        })
        .getOne();
      if (!industry) return [];
      checkedIndustry = {
        name: industry.name,
        childrenCodes: [industry.code],
      };
      qb.where('industry.parent_id = :parentId', { parentId: industry.id });
    } else {
      qb.where('industry.industry_level = :level', { level: 0 }).orWhere(
        'LENGTH(industry.level) = :length',
        { length: 8 },
      );
    }
    const subList = await qb.getRawMany();
    // 查询所有子集
    const res = await Promise.all(
      subList.map(async (item) => {
        const children = await this.dimEmployerIndustryRepository
          .createQueryBuilder('industry')
          .where('industry.level LIKE :level', {
            level: `%${item.industry_level}%`,
          })
          .getRawMany();
        return {
          name: item.industry_name,
          childrenCodes: children.map((ele) => ele.industry_code),
        };
      }),
    );
    return res.length > 0 ? res : [checkedIndustry];
  }
}
