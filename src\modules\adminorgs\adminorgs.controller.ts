import { Controller, Get, Query } from '@nestjs/common';
import { AdminorgsService } from './adminorgs.service';

@Controller('adminorgs')
export class AdminorgsController {
  constructor(private readonly adminorgsService: AdminorgsService) {}

  @Get()
  async findlist(@Query() query: any) {
    const adminorgs = await this.adminorgsService.findList(query);
    return {
      code: 200,
      message: '获取成功',
      data: adminorgs,
    };
  }
}
