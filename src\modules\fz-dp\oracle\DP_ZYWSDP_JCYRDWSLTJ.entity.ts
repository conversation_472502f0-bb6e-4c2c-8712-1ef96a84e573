import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

@Entity({
  name: 'DP_ZYWSDP_JCYRDWSLTJ',
  comment: '职业卫生大屏-监测用人单位数量统计',
})
export class DP_ZYWSDP_JCYRDWSLTJ {
  @PrimaryGeneratedColumn()
  ID: number;

  @Column({ type: 'varchar2', length: 255, comment: '地区' })
  DQ: string;

  @Column({ type: 'varchar2', length: 255, comment: '年份' })
  NF: string;

  @Column({ type: 'varchar2', length: 255, comment: '应开展数量' })
  YKZSL: string;

  @Column({ type: 'varchar2', length: 255, comment: '实际开展数量' })
  SJKZSL: string;

  @Column({ type: 'varchar2', length: 255, comment: '开展率' })
  KZC: string;
}
