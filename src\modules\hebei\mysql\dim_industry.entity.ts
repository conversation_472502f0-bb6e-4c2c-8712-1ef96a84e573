import { Entity, Column } from 'typeorm';

@Entity('dim_industry')
export class dimIndustry {
  @Column({
    type: 'varchar',
    length: 12,
    // nullable: true,  // 📌
    comment: 'industry_id',
    primary: true,
  })
  id: string | null;

  @Column({ type: 'varchar', length: 1000, nullable: true, comment: '名称' })
  name: string | null;

  @Column({
    type: 'varchar',
    length: 12,
    nullable: true,
    comment: '父级行业编码',
  })
  parent_id: string | null;

  @Column({ type: 'tinyint', nullable: true, comment: '级别' })
  level: number | null;

  @Column({ type: 'varchar', length: 1000, nullable: true, comment: '描述' })
  description: string | null;
}
