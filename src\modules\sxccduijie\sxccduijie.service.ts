import { Injectable, Logger, Inject } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import axios from 'axios';
import * as fs from 'fs';
import { Dingtrees } from './entities/dingtrees.entity';
import { Adminorgs } from '../adminorgs/adminorgs.entity';
import { Employees } from './entities/employees.entity';
import { Adminusers } from './entities/adminusers.entity';
import { Policy } from './entities/polices.entity';
import { GroupEnterprisesRecords } from './entities/groupEnterprisesRecords.entity';
import { Users } from './entities/users.entity';
import { MongoRepository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { performance } from 'perf_hooks';
import { ConfigService } from '@nestjs/config';
import * as CryptoJS from 'crypto-js';
import * as moment from 'moment';
import { MongoFindOneOptions } from 'typeorm/find-options/mongodb/MongoFindOneOptions';
import { CronJob } from 'cron';
import { SchedulerRegistry } from '@nestjs/schedule';
import { Cache } from 'cache-manager';
const apis = {
  getDepartInfo: {
    url: '/service/api/dajiankang/zuzhi',
    method: 'get',
    name: '获取部门信息',
    query: {
      Starttimt: {
        type: 'string',
        name: 'Starttimt',
        reg: /^\d{4}-\d{2}-\d{2}$/,
        required: false,
      },
      Endtime: {
        type: 'string',
        name: 'Endtime',
        reg: /^\d{4}-\d{2}-\d{2}$/,
        required: false,
      },
      pageSize: {
        type: 'number',
        name: 'pageSize',
        required: true,
      },
      pageNum: {
        type: 'number',
        name: 'pageNum',
        required: true,
      },
    },
  },
  getEmployeeInfo: {
    url: '/service/api/dajiankang/renyuan',
    method: 'get',
    name: '获取人员信息',
    query: {
      Starttimt: {
        type: 'string',
        name: 'Starttimt',
        reg: /^\d{4}-\d{2}-\d{2}$/,
        required: false,
      },
      Endtime: {
        type: 'string',
        name: 'Endtime',
        reg: /^\d{4}-\d{2}-\d{2}$/,
        required: false,
      },
      pageSize: {
        type: 'number',
        name: 'pageSize',
        required: true,
      },
      pageNum: {
        type: 'number',
        name: 'pageNumber',
        required: true,
      },
    },
  },
};
@Injectable()
export class SxccduijieService {
  constructor(
    private configService: ConfigService,
    private schedulerRegistry: SchedulerRegistry,
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,
    @InjectRepository(Dingtrees, 'mongodbConnection')
    private dingtreesRepository: MongoRepository<Dingtrees>,
    @InjectRepository(Adminorgs, 'mongodbConnection')
    private adminorgsRepository: MongoRepository<Adminorgs>,
    @InjectRepository(Employees, 'mongodbConnection')
    private employeesRepository: MongoRepository<Employees>,
    @InjectRepository(GroupEnterprisesRecords, 'mongodbConnection')
    private groupEnterprisesRecordsRepository: MongoRepository<GroupEnterprisesRecords>,
    @InjectRepository(Adminusers, 'mongodbConnection')
    private adminusersRepository: MongoRepository<Adminusers>,
    @InjectRepository(Users, 'mongodbConnection')
    private usersRepository: MongoRepository<Users>,
    @InjectRepository(Policy, 'mongodbConnection')
    private policyRepository: MongoRepository<Policy>,
  ) {}
  private readonly auth = {
    username: 'ea6089563eb24347bcbeb735655ee56d',
    password: 'Q02V1Eg!',
  };
  private readonly baseUrl = this.configService.get<string>('sxccBaseUrl');
  private readonly adminGroup = this.configService.get<string>(
    'groupID.adminGroupID',
  );
  private readonly userGroup = this.configService.get<string>(
    'groupID.userGroupID',
  );
  private readonly salt_sha2_key =
    this.configService.get<string>('salt_sha2_key');
  private readonly logger = new Logger(SxccduijieService.name);

  async onModuleInit() {
    // 动态创建和启动Cron定时作业
    const isGetSxccData = this.configService.get('isGetSxccData');
    const isFullUpdateSxcc = this.configService.get('isFullUpdateSxcc');
    if (isGetSxccData === '1') {
      const interval = this.configService.get('sxccCornInterval');
      const params = {
        pageSize: 1000,
        pageNum: 1,
        Starttimt: '1970-01-01',
        Endtime: moment(new Date()).format('YYYY-MM-DD'),
      };
      const time = await this.cacheManager.get('sxccTime');
      params.Starttimt = (time as string) || '1970-01-01';
      if (isFullUpdateSxcc === '1') {
        params.Starttimt = '1970-01-01';
        this.logger.log('开启焦煤全量更新');
      }
      // 服务启动时立即执行一次
      this.sxccTask(params);
      const job = new CronJob(interval, () => {
        params.Endtime = moment(new Date()).format('YYYY-MM-DD');
        this.logger.log(`焦煤基础数据对接截止时间参数:${params.Endtime}`);
        this.sxccTask(params); // 通过闭包传递参数
      });
      this.schedulerRegistry.addCronJob('sxccBaseData', job as any);
      job.start();
    }
  }

  async onModuleDestroy() {
    // 停止和销毁Cron定时作业
    const job = this.schedulerRegistry.getCronJob('sxccBaseData');
    job.stop();
    this.schedulerRegistry.deleteCronJob('sxccBaseData');
  }

  async sxccTask(params: object) {
    const toParams = JSON.parse(JSON.stringify(params));
    this.logger.log(
      `同步基础数据sxcc${JSON.stringify(toParams)}-------${moment(new Date()).format('YYYY-MM-DD HH:mm:ss')}开始-------`,
    );
    await this.processSxccBaseData(toParams);
  }

  /**
   * @param apiName
   * @param queryObj
   * @description axios 封装方法
   */
  async sxccAxios(apiName: string, queryObj: any): Promise<any> {
    try {
      const { url, method, query } = apis[apiName];
      const validateFlg = this.validate(query, queryObj);
      if (!validateFlg) {
        this.logger.error(apiName + '参数校验失败');
        throw new Error(apiName + '参数校验失败');
      }
      let result: any = {};
      try {
        result = await axios({
          url: `${this.baseUrl}${url}`,
          method,
          params: queryObj,
          auth: this.auth,
        });
        return result.data;
      } catch (error) {
        if (
          JSON.stringify(error).indexOf('401') > -1 ||
          (result.code && result.code.indexOf('401') > -1)
        ) {
          this.logger.error(`请求失败${result.message}`);
          throw new Error(result.message || '请求失败');
        }
        this.logger.error(`请求失败${apiName}`);
        throw new Error(`请求失败${apiName}`);
      }
    } catch (error) {
      this.logger.error(error);
      return false;
    }
  }

  /**
   * 校验参数方法
   *
   * @param {Object} query 校验规则
   * @param {Object} params 要校验的参数
   * @return {boolean} 如果所有参数校验通过，返回true；否则返回false。
   */
  validate(query: object, params: object): boolean {
    try {
      const queryKeys = Object.keys(query);
      const isValid = queryKeys.every((key) => {
        const queryItem = query[key];
        if (queryItem.required && !params[key]) {
          this.logger.error(`缺少参数${key}`);
          throw new Error(`缺少参数${key}`);
        }
        if (!queryItem.required && !params[key]) {
          return true;
        }
        const paramsItem = params[key];
        if (typeof paramsItem !== queryItem.type) {
          this.logger.error(`参数${key}类型错误`);
          throw new Error(`参数${key}类型错误`);
        }
        if (queryItem.reg && !queryItem.reg.test(paramsItem)) {
          this.logger.error(`参数${key}格式错误`);
          throw new Error(`参数${key}格式错误`);
        }
        return true;
      });
      return isValid;
    } catch (error) {
      console.error(error);
      return false;
    }
  }

  /**
   * @description 获取部门信息
   * @param {Object} params
   */

  async processDepartInfo(
    params: {
      pageSize: number;
      pageNum: number;
      Starttimt?: string;
      Endtime?: string;
    } = {
      pageSize: 1000,
      pageNum: 1,
      Starttimt: '1970-01-01',
      Endtime: '2024-06-19',
    },
  ): Promise<any> {
    try {
      const toParams: {
        pageNum: number;
        pageSize: number;
        Starttimt?: string;
        Endtime?: string;
      } = {
        pageNum: 1,
        pageSize: 1000,
        Starttimt: '1970-01-01',
        Endtime: '2024-06-19',
      };
      if (params.Starttimt) {
        toParams.Starttimt = moment(params.Starttimt).format('YYYY-MM-DD');
      }
      if (params.Endtime) {
        toParams.Endtime = moment(params.Endtime).format('YYYY-MM-DD');
      }
      let pageNum = 1;
      const pageSize = +params.pageSize || 1000;
      let nowLength = pageSize;
      const departInfo = [];
      let isError = false;
      while (nowLength >= pageSize && !isError) {
        try {
          toParams.pageNum = pageNum++;
          toParams.pageSize = pageSize;
          const res = await this.sxccAxios('getDepartInfo', toParams);
          const departList = res;
          departInfo.push(...departList);
          nowLength = departList.length;
        } catch (error) {
          this.logger.error('获取部门信息失败', error);
          isError = true;
        }
      }
      // 存到根目录下
      // fs.writeFileSync('departInfo.json', JSON.stringify(departInfo));
      return departInfo;
      // const result = await this.sxccAxios('getDepartInfo', toParams);
    } catch (error) {}
  }

  /**
   * @description 获取员工信息
   * @param {Object} params
   */
  async processEmployeeInfo(
    params: {
      pageSize: number;
      pageNum: number;
      Starttimt?: string;
      Endtime?: string;
    } = {
      pageSize: 1000,
      pageNum: 1,
      Endtime: '2024-06-19',
      Starttimt: '1970-01-01',
    },
  ): Promise<any> {
    try {
      const toParams: {
        pageNum: number;
        pageSize: number;
        Starttimt?: string;
        Endtime?: string;
      } = {
        pageNum: 1,
        pageSize: 1000,
        Starttimt: '1970-01-01',
        Endtime: '2024-06-19',
      };
      if (params.Starttimt) {
        toParams.Starttimt = moment(params.Starttimt).format('YYYY-MM-DD');
      }
      if (params.Endtime) {
        toParams.Endtime = moment(params.Endtime).format('YYYY-MM-DD');
      }
      let pageNum = 1;
      const pageSize = +params.pageSize || 1000;
      let nowLength = pageSize;
      const employeeInfo = [];
      const start = performance.now();
      let isError = false;
      while (nowLength >= pageSize && !isError) {
        try {
          toParams.pageNum = pageNum++;
          toParams.pageSize = pageSize;
          const pageTime = performance.now() - start;
          console.log(
            `当前第${pageNum}页`,
            `用时${(pageTime / 1000).toFixed(2)}s`,
          );
          const res = await this.sxccAxios('getEmployeeInfo', toParams);
          const employeeList = res;
          employeeInfo.push(...employeeList);
          nowLength = employeeList.length;
        } catch (error) {
          this.logger.error('获取员工信息失败', error);
          isError = true;
        }
      }
      const end = performance.now();
      const totalTime = end - start;
      console.log(`总计用时${totalTime / 1000}s`);
      console.log(employeeInfo.length);
      // 存到根目录下
      // fs.writeFileSync('employeeInfo.json', JSON.stringify(employeeInfo));
      return employeeInfo;
    } catch (error) {}
  }

  /**
   * @description 获取sxcc基础数据
   * @param {*} params
   */
  async processSxccBaseData(
    params: {
      pageSize: number;
      pageNum: number;
      Starttimt?: string;
      Endtime?: string;
    } = {
      pageSize: 100,
      pageNum: 1,
    },
  ) {
    try {
      const departInfo = await this.processDepartInfo(params);
      // fs.writeFileSync('sxcc-bumen.json', JSON.stringify(departInfo, null, 2));
      // const departInfo = await import('../../../sxcc-bumen.json');
      this.logger.log('开始处理部门信息');
      await this.processNoTopCompany();
      const topDepart: string[] = [];
      for (const item of departInfo) {
        let productionStatus = '2';
        if (item.state === '1') {
          productionStatus = '2';
        } else if (item.state === '0') {
          productionStatus = '1';
        } else if (item.state === '7') {
          productionStatus = '0';
        }
        if (item.danwei_xingzhi === '01') {
          // const adminuser_id: string = await this.processNoAdminUser(
          //   item.code,
          //   item.danwei_jc,
          // );
          topDepart.push(item.code);
          await this.processSxccCompany(item, productionStatus);
        } else if (item.danwei_xingzhi === '02') {
          await this.processSxccDepart(item, productionStatus);
        }
      }
      this.logger.log(
        '部门信息处理结束，开始处理部门，公司数据，添加各部门对应的EnterpriseID',
      );
      await this.fetchDepartmentData();
      this.logger.log('处理部门，公司数据，添加各部门对应的EnterpriseID结束');
      if (topDepart.length) {
        this.logger.log(
          '开始处理公司上下层级，添加adminorgs里的parentId，childrenId',
        );
        await this.fetchCompanyData(topDepart);
        this.logger.log(
          '处理公司上下层级，添加adminorgs里的parentId，childrenId结束',
        );
      }
      this.logger.log('开始处理人员信息');
      try {
        await this.processEmployeeInfoIncrement(params);
        this.logger.log('处理人员信息结束');
      } catch (error) {
        this.logger.error('处理人员信息失败', error);
      }
      this.logger.log('sxcc基础数据对接结束');
      this.cacheManager.set('sxccTime', params.Endtime);
      this.logger.log(
        `同步基础数据sxcc-------${moment(new Date()).format('YYYY-MM-DD HH:mm:ss')}结束-------`,
      );
    } catch (error) {
      this.logger.error('sxcc基础数据对接失败', error);
    }
  }

  /**
   * @description 处理焦煤不存在顶级企业的情况
   */
  async processNoTopCompany() {
    try {
      const adminorgOptions: MongoFindOneOptions<Adminorgs> = {
        where: {
          _id: '10000000',
        },
      };
      const topSxccAdminorg =
        await this.adminorgsRepository.findOne(adminorgOptions);
      if (!topSxccAdminorg) {
        await this.adminorgsRepository.insertOne({
          _id: '10000000',
          cname: '山西焦煤集团有限责任公司',
          code: '91110000700000000A',
          regAdd: '山西省太原市小店区平阳路1号',
          phoneNum: '0351-6011111',
          createTime: new Date('2004-01-01'),
          shortName: '山西焦煤',
          productionStatus: '2',
          parentId: [],
          isactive: '1',
          companyCategory: 'group',
        });
      }
      const adminuserOptions: MongoFindOneOptions<Adminusers> = {
        where: {
          newAddEnterpriseID: '10000000',
        },
      };
      const sxccAdminUser =
        await this.adminusersRepository.findOne(adminuserOptions);
      if (!sxccAdminUser) {
        const sxccAdminuser = await this.adminusersRepository.create({
          name: '山西焦煤集团有限责任公司',
          userName: 'sxcc10000000',
          password: CryptoJS.SHA256(
            'Tc666888.' + this.salt_sha2_key,
          ).toString(),
          group: this.adminGroup,
          newAddEnterpriseID: '10000000',
          EnterpriseID: '10000000',
        });
        const res = await this.adminusersRepository.save(sxccAdminuser);
        await this.adminorgsRepository.updateOne(
          { _id: '10000000' },
          {
            $set: {
              adminUserId: res._id,
              adminArray: [res._id],
            },
          },
        );
        await this.processSuperAdmin(res._id);
      }
      const dingtreeOption: MongoFindOneOptions<Dingtrees> = {
        where: {
          _id: '10000000',
        },
      };
      const topSxccDingtree =
        await this.dingtreesRepository.findOne(dingtreeOption);
      if (!topSxccDingtree) {
        await this.dingtreesRepository.insertOne({
          _id: '10000000',
          EnterpriseID: '10000000',
          name: '山西焦煤集团有限责任公司',
          shortName: '山西焦煤',
          staff: [],
          isDelete: false,
          topLevelOfTheEnterprise: true,
          createTime: new Date('2004-01-01'),
        });
      }
      this.logger.log('处理焦煤不存在顶级企业的情况成功');
      return true;
    } catch (error) {
      this.logger.error('处理焦煤不存在顶级企业的情况失败', error);
      return false;
    }
  }

  /**
   * @description 处理超级管理员
   */
  async processSuperAdmin(suerUserId: string) {
    try {
      const policyOptions: MongoFindOneOptions<Policy> = {
        where: {
          isSuper: true,
          user_ids: suerUserId,
        },
      };
      const superUser = await this.policyRepository.findOne(policyOptions);
      if (!superUser) {
        const superUserInfo = await this.policyRepository.create({
          sortId: 0,
          user_ids: [suerUserId],
          enable: false,
          name: '超级管理员',
          scope_type: '',
          lastOperator: '系统',
          enterprise_ids: [],
          dingtree_ids: [],
          millConstruction_ids: [],
          createdAt: new Date(),
          updatedAt: new Date(),
          isSuper: true,
          group_ids: [],
        });
        await this.policyRepository.save(superUserInfo);
        this.logger.log('处理超级管理员成功');
      }
    } catch (error) {
      this.logger.error('处理超级管理员失败', error);
    }
  }

  /**
   * @description 处理焦煤企业，企业顶级部门
   * @param {*} enterpriseInfo 焦煤企业信息
   * @param {*} productionStatus 生产状态
   * @param {*} adminuser_id 管理员ID
   */
  async processSxccCompany(
    enterpriseInfo: any,
    productionStatus: string,
    // adminuser_id: string,
  ) {
    const {
      code,
      danwei_qc,
      tongyishenghuixinyongdaima,
      gongshangzhucedizhi,
      phone,
      publish_time,
      danwei_jc,
      renshishangjiguanlidanwei_code,
    } = enterpriseInfo;
    try {
      const adminorgOptions: MongoFindOneOptions<Adminorgs> = {
        where: {
          _id: code,
        },
      };
      const adminorgInfo =
        await this.adminorgsRepository.findOne(adminorgOptions);
      if (!adminorgInfo) {
        await this.adminorgsRepository.insertOne({
          _id: code,
          cname: danwei_qc,
          code: tongyishenghuixinyongdaima,
          regAdd: gongshangzhucedizhi,
          phoneNum: phone,
          createTime: new Date(publish_time),
          shortName: danwei_jc,
          productionStatus,
          isactive: '1',
          companyCategory: 'subsidiary',
          // adminUserId: adminuser_id,
          // adminArray: [adminuser_id],
        });
      } else {
        await this.adminorgsRepository.updateOne(
          { _id: code },
          {
            $set: {
              cname: danwei_qc,
              code: tongyishenghuixinyongdaima,
              regAdd: gongshangzhucedizhi,
              phoneNum: phone,
              createTime: new Date(publish_time),
              shortName: danwei_jc,
              productionStatus,
            },
          },
        );
      }
      let isDelete = false;
      if (productionStatus === '0' || productionStatus === '7') {
        isDelete = true;
      }
      const dingtreeOption: MongoFindOneOptions<Dingtrees> = {
        where: {
          _id: code,
        },
      };
      const dingtreeInfo =
        await this.dingtreesRepository.findOne(dingtreeOption);
      if (!dingtreeInfo) {
        await this.dingtreesRepository.insertOne({
          _id: code,
          EnterpriseID: code,
          name: danwei_qc,
          shortName: danwei_jc,
          isDelete,
          staff: [],
          parentid: renshishangjiguanlidanwei_code,
          topLevelOfTheEnterprise: true,
          createTime: new Date(publish_time),
        });
      } else {
        await this.dingtreesRepository.updateOne(
          {
            _id: code,
          },
          {
            $set: {
              EnterpriseID: code,
              name: danwei_qc,
              shortName: danwei_jc,
              isDelete,
              parentid: renshishangjiguanlidanwei_code,
              topLevelOfTheEnterprise: true,
              createTime: new Date(publish_time),
            },
          },
        );
      }
    } catch (error) {
      this.logger.error(
        `处理焦煤其他企业，其他企业顶级部门失败code:${code},qc:${danwei_qc}`,
        error,
      );
    }
  }

  /**
   * @description 处理焦煤部门信息
   * @param {*} departInfo 部门信息
   */
  async processSxccDepart(departInfo: any, status: string) {
    const {
      code,
      danwei_qc,
      danwei_jc,
      renshishangjiguanlidanwei_code,
      publish_time,
    } = departInfo;
    try {
      const dingtreeOption: MongoFindOneOptions<Dingtrees> = {
        where: {
          _id: code,
        },
      };
      const dingtreeInfo =
        await this.dingtreesRepository.findOne(dingtreeOption);
      let isDelete = false;
      if (status === '0' || status === '7') {
        isDelete = true;
      }
      if (!dingtreeInfo) {
        await this.dingtreesRepository.insertOne({
          _id: code,
          name: danwei_qc,
          shortName: danwei_jc,
          isDelete,
          staff: [],
          parentid: renshishangjiguanlidanwei_code,
          topLevelOfTheEnterprise: false,
          createTime: new Date(publish_time),
        });
      } else {
        await this.dingtreesRepository.updateOne(
          { _id: code },
          {
            $set: {
              name: danwei_qc,
              shortName: danwei_jc,
              isDelete,
              parentid: renshishangjiguanlidanwei_code,
              createTime: new Date(publish_time),
            },
          },
        );
      }
    } catch (error) {
      this.logger.error(
        `处理焦煤部门信息失败code:${code},qc:${danwei_qc}`,
        error,
      );
    }
  }

  /**
   * @description 处理焦煤当前企业不存在adminuser的情况
   * @param {*} code 焦煤企业code
   * @param {*} name 焦煤企业userName对应企业简称
   */
  async processNoAdminUser(code: string, name: string) {
    try {
      const adminuserOptions: MongoFindOneOptions<Adminusers> = {
        where: {
          newAddEnterpriseID: code,
        },
      };
      const adminUser =
        await this.adminusersRepository.findOne(adminuserOptions);
      if (!adminUser) {
        const adminuserInfo = await this.adminusersRepository.create({
          name,
          userName: 'sxcc' + code,
          password: CryptoJS.SHA256(
            'Tc666888.' + this.salt_sha2_key,
          ).toString(),
          group: this.adminGroup,
          newAddEnterpriseID: code,
        });
        const res = await this.adminusersRepository.save(adminuserInfo);
        return res._id;
      }
      return adminUser._id;
    } catch (error) {
      this.logger.error(
        `处理焦煤当前企业不存在adminuser的情况失败code:${code}`,
        error,
      );
    }
  }

  /**
   * @description 增量获取并处理员工信息
   * @param {*} params
   */
  async processEmployeeInfoIncrement(
    params: {
      pageSize: number;
      pageNum: number;
      Starttimt?: string;
      Endtime?: string;
    } = {
      pageSize: 1000,
      pageNum: 1,
      Endtime: '2024-06-19',
      Starttimt: '1970-01-01',
    },
  ) {
    try {
      // const addEmployeeInfo = [];
      // const addUserInfo = [];
      const employeeInfo = await this.processEmployeeInfo(params);
      // fs.writeFileSync(
      //   'sxcc-renyuan.json',
      //   JSON.stringify(employeeInfo, null, 2),
      // );
      // const employeeInfo = await import('../../../sxcc-renyuan.json');
      const start = performance.now();
      let lastTime = start;
      let counter = 0;
      for (const item of employeeInfo) {
        await this.processSxccEmployee(item);
        await this.processSxccUser(item);
        counter++;
        if (counter % 30000 === 0) {
          const currentTime = performance.now();
          console.log(
            `Processed 30000 items in ${((currentTime - lastTime) / 1000).toFixed(2)} seconds.`,
          );
          lastTime = currentTime;
        }
      }
      const end = performance.now();
      console.log(`Total time: ${((end - start) / 30000).toFixed(2)} seconds.`);
    } catch (error) {
      this.logger.error('增量获取并处理员工信息失败', error);
    }
  }

  /**
   * @description 处理焦煤员工信息employee表
   * @param {*} employeeInfo 员工信息
   */
  async processSxccEmployee(employeeInfo: any) {
    const {
      fk_mdnb_number,
      fk_mdnb_name,
      fk_mdnb_idcard,
      fk_mdnb_mdeptid,
      fk_mdnb_education,
      fk_mdnb_gender,
      fk_mdnb_postion,
      fk_mdnb_phone,
      fmodifytime,
      fcreatetime,
      fk_mdnb_joinjobdate,
      fk_mdnb_born,
    } = employeeInfo;
    try {
      const fields = [
        fk_mdnb_idcard,
        fk_mdnb_name,
        fk_mdnb_mdeptid,
        fk_mdnb_phone,
      ];
      if (fields.some((field) => !field || !field.trim())) {
        return;
      }
      const employeeOptions: MongoFindOneOptions<Employees> = {
        where: {
          _id: fk_mdnb_number,
        },
      };
      const peopleInfo =
        await this.employeesRepository.findOne(employeeOptions);
      const dingtreeOptions: MongoFindOneOptions<Dingtrees> = {
        where: {
          _id: fk_mdnb_mdeptid,
        },
      };
      const dingtreeInfo =
        await this.dingtreesRepository.findOne(dingtreeOptions);
      const employeeData = {
        _id: '',
        name: fk_mdnb_name,
        IDNum: fk_mdnb_idcard,
        departs: [fk_mdnb_mdeptid],
        education: fk_mdnb_education,
        gender: fk_mdnb_gender === '男' ? '0' : '1',
        age: fk_mdnb_born && moment().diff(fk_mdnb_born, 'years'),
        workType: fk_mdnb_postion,
        phoneNum: this.processPhone(fk_mdnb_phone),
        updatedAt: new Date(fmodifytime),
        createdAt: new Date(fcreatetime),
        workStart: new Date(fk_mdnb_joinjobdate),
        workYears:
          fk_mdnb_joinjobdate &&
          moment().diff(fk_mdnb_joinjobdate, 'years') + '',
        userId: fk_mdnb_number,
        unitCode: fk_mdnb_number,
        status: 1,
        enable: true,
        EnterpriseID: dingtreeInfo?.EnterpriseID,
      };
      if (!peopleInfo) {
        employeeData._id = fk_mdnb_number;
        await this.employeesRepository.insertOne(employeeData);
        await this.dingtreesRepository.updateOne(
          { _id: fk_mdnb_mdeptid },
          {
            $addToSet: { staff: fk_mdnb_number as never },
          },
        );
        return true;
      } else {
        delete employeeData._id;
        await this.employeesRepository.updateOne(
          { _id: fk_mdnb_number },
          {
            $set: employeeData,
          },
        );
        await this.dingtreesRepository.updateOne(
          { _id: fk_mdnb_mdeptid },
          {
            $addToSet: { staff: fk_mdnb_number as never },
          },
        );
        return false;
      }
    } catch (error) {
      this.logger.error(
        `处理焦煤员工信息employee表失败${fk_mdnb_number},name:${fk_mdnb_name}`,
        error,
      );
    }
  }

  /**
   * @description 处理焦煤员工信息user表
   * @param {*} userInfo 员工信息
   */
  async processSxccUser(userInfo: any) {
    const {
      fk_mdnb_number,
      fk_mdnb_name,
      fk_mdnb_idcard,
      fk_mdnb_mdeptid,
      fk_mdnb_gender,
      fk_mdnb_postion,
      fk_mdnb_phone,
      fmodifytime,
      fcreatetime,
      fk_mdnb_born,
    } = userInfo;
    try {
      const fields = [
        fk_mdnb_idcard,
        fk_mdnb_name,
        fk_mdnb_mdeptid,
        fk_mdnb_phone,
      ];
      if (fields.some((field) => !field || !field.trim())) {
        return;
      }
      const userOptions: MongoFindOneOptions<Users> = {
        where: {
          _id: fk_mdnb_number,
        },
      };
      const userData = await this.usersRepository.findOne(userOptions);
      const dingtreeOptions: MongoFindOneOptions<Dingtrees> = {
        where: {
          _id: fk_mdnb_mdeptid,
        },
      };
      const dingtreeInfo =
        await this.dingtreesRepository.findOne(dingtreeOptions);
      const userInfoData = {
        _id: '',
        enable: true,
        name: fk_mdnb_name,
        unitCode: fk_mdnb_number,
        userName: fk_mdnb_number,
        countryCode: '86',
        employeeId: fk_mdnb_number,
        phoneNum: this.processPhone(fk_mdnb_phone),
        position: fk_mdnb_postion,
        idNo: fk_mdnb_idcard,
        idType: '1',
        companyStatus: 2,
        birth: fk_mdnb_born ? new Date(fk_mdnb_born) : '',
        gender: fk_mdnb_gender === '男' ? '0' : '1',
        password: CryptoJS.SHA256('Tc666888.' + this.salt_sha2_key).toString(),
        companyId: [dingtreeInfo?.EnterpriseID],
        company: dingtreeInfo?.name,
        date: new Date(fcreatetime),
        updateTime: new Date(fmodifytime),
        group: this.userGroup,
        state: '1',
      };
      if (!userData) {
        userInfoData._id = fk_mdnb_number;
        await this.usersRepository.insertOne(userInfoData);
        return true;
      } else {
        delete userInfoData._id;
        delete userInfoData.password;
        await this.usersRepository.updateOne(
          { _id: fk_mdnb_number },
          {
            $set: userInfoData,
          },
        );
        return false;
      }
    } catch (error) {
      this.logger.error(
        `处理焦煤员工信息user表失败${fk_mdnb_number},name:${fk_mdnb_name}`,
      );
    }
  }

  // 处理手机号，可能存在-，去掉-
  processPhone(phone: string): string {
    if (phone.includes('-')) {
      return phone.split('-')[1];
    }
    return phone;
  }

  async fetchDataAndWriteToFile(): Promise<void> {
    const auth = {
      username: 'ea6089563eb24347bcbeb735655ee56d',
      password: 'Q02V1Eg!',
    };

    const pageSize = 1000;
    const pageNums = Array.from({ length: 194 }, (_, i) => i + 1);

    const data = [];

    for (const pageNum of pageNums) {
      const url = `http://172.16.67.199:8310/service/api/dajiankang/renyuan?yg_code=NULL&yg_idcard=NULL&pageSize=${pageSize}&pageNum=${pageNum}`;

      try {
        const response = await axios.get(url, { auth });
        if (pageNum === 194) {
          console.log('焦煤人员获取结束:');
        }
        data.push(...response.data);
      } catch (error) {
        console.error(`Failed to fetch data for pageNum ${pageNum}:`, error);
      }
    }

    try {
      fs.writeFileSync('sxcc-renyuan.json', JSON.stringify(data, null, 2));
      console.log('Data written to data.json file.');
    } catch (error) {
      console.error('Failed to write data to file:', error);
    }
  }

  /**
   * @description 第二次处理部门，公司数据，添加各部门对应的EnterpriseID
   */
  async fetchDepartmentData(): Promise<void> {
    try {
      const firstPipeline = [
        {
          $match: {
            EnterpriseID: { $exists: false },
          },
        },
        {
          $graphLookup: {
            from: 'dingtrees',
            startWith: '$_id',
            connectFromField: 'parentid',
            connectToField: '_id',
            as: 'bumeninfo',
            depthField: 'depth',
          },
        },
        {
          $unwind: '$bumeninfo',
        },
        {
          $match: {
            'bumeninfo.topLevelOfTheEnterprise': true,
          },
        },
        {
          $group: {
            _id: '$_id',
            minDepth: { $min: '$bumeninfo.depth' },
            bumeninfo: { $push: '$bumeninfo' },
          },
        },
        {
          $unwind: '$bumeninfo',
        },
        {
          $match: {
            $expr: {
              $eq: ['$bumeninfo.depth', '$minDepth'],
            },
          },
        },
        {
          $addFields: {
            EnterpriseID: '$bumeninfo._id',
          },
        },
        {
          $project: {
            _id: 1,
            EnterpriseID: '$bumeninfo._id',
          },
        },
      ];

      const firstResult = await this.dingtreesRepository
        .aggregate(firstPipeline)
        .toArray();
      if (firstResult.length) {
        for (const item of firstResult) {
          await this.dingtreesRepository.updateOne(
            { _id: item._id },
            { $set: { EnterpriseID: item.EnterpriseID } },
          );
        }
      }

      // 处理集团本部的情况
      const secondPipeline = [
        {
          $match: { EnterpriseID: { $exists: false } },
        },
        {
          $graphLookup: {
            from: 'dingtrees',
            startWith: '$_id',
            connectFromField: 'parentid',
            connectToField: '_id',
            as: 'bumeninfo',
            depthField: 'depth',
          },
        },
        {
          $unwind: '$bumeninfo',
        },
        {
          $group: {
            _id: '$_id',
            maxDepth: { $max: '$bumeninfo.depth' },
            bumeninfo: { $push: '$bumeninfo' },
          },
        },
        {
          $unwind: '$bumeninfo',
        },
        {
          $match: {
            $expr: {
              $eq: ['$bumeninfo.depth', '$maxDepth'],
            },
          },
        },
        {
          $match: {
            'bumeninfo._id': '10000001',
          },
        },
        {
          $addFields: {
            EnterpriseID: '10000000',
          },
        },
        {
          $project: {
            _id: 1,
            EnterpriseID: 1,
          },
        },
      ];

      const secondResult = await this.dingtreesRepository
        .aggregate(secondPipeline)
        .toArray();
      if (secondResult.length) {
        for (const item of secondResult) {
          await this.dingtreesRepository.updateOne(
            { _id: item._id },
            { $set: { EnterpriseID: item.EnterpriseID } },
          );
        }
      }
    } catch (error) {
      this.logger.error(
        '第二次处理部门，公司数据，添加各部门对应的EnterpriseID',
        error,
      );
    }
  }

  /*
  处理公司上下层级，添加adminorgs里的parentId，childrenId
  */
  async fetchCompanyData(topDepart: string[]): Promise<void> {
    try {
      // groupEnterprisesRecords
      topDepart.push('10000000');
      const res = await this.dingtreesRepository
        .aggregate([
          {
            $match: {
              _id: { $in: topDepart },
              topLevelOfTheEnterprise: true,
            },
          },
          {
            $graphLookup: {
              from: 'dingtrees',
              startWith: '$_id',
              connectFromField: 'parentid',
              connectToField: '_id',
              as: 'bumeninfo',
              depthField: 'depth',
            },
          },
          {
            $project: {
              bumeninfo: {
                $filter: {
                  input: '$bumeninfo',
                  as: 'item',
                  cond: { $ne: ['$$item._id', '$_id'] },
                },
              },
            },
          },
          {
            $unwind: '$bumeninfo',
          },
          {
            $match: {
              'bumeninfo.topLevelOfTheEnterprise': true,
            },
          },
          {
            $group: {
              _id: '$_id',
              minDepth: { $min: '$bumeninfo.depth' },
              bumeninfo: { $push: '$bumeninfo' },
            },
          },
          {
            $unwind: '$bumeninfo',
          },
          {
            $match: {
              $expr: {
                $eq: ['$bumeninfo.depth', '$minDepth'],
              },
            },
          },
          {
            $addFields: {
              targetEnterpriseID: '$bumeninfo.EnterpriseID',
              EnterpriseID: '$_id',
              messageStatus: 2,
              isUp: true,
            },
          },
          {
            $project: {
              _id: 0,
              minDepth: 0,
              bumeninfo: 0,
            },
          },
          // {
          //   $out: 'groupEnterprisesRecords',
          // },
        ])
        .toArray();
      // 创建groupEnterprisesRecords文档，用create
      // await this.groupEnterprisesRecordsRepository.insertMany(res);
      for (const item of res) {
        const groupEnterprisesRecordsOption: MongoFindOneOptions<GroupEnterprisesRecords> =
          {
            where: {
              EnterpriseID: item.EnterpriseID,
            },
          };
        const groupEnterprisesRecords =
          await this.groupEnterprisesRecordsRepository.findOne(
            groupEnterprisesRecordsOption,
          );
        if (!groupEnterprisesRecords) {
          const groupInfo =
            await this.groupEnterprisesRecordsRepository.create(item);
          await this.groupEnterprisesRecordsRepository.save(groupInfo);
        } else {
          delete item._id;
          delete item.EnterpriseID;
          await this.groupEnterprisesRecordsRepository.updateOne(
            { EnterpriseID: item.EnterpriseID },
            { $set: item },
          );
        }
      }
      // console.log(res);
      // parentId
      const parentIdLine = [
        {
          $match: {
            _id: { $in: topDepart },
            topLevelOfTheEnterprise: true,
          },
        },
        {
          $graphLookup: {
            from: 'dingtrees',
            startWith: '$_id',
            connectFromField: 'parentid',
            connectToField: '_id',
            as: 'bumeninfo',
            depthField: 'depth',
          },
        },
        {
          $project: {
            bumeninfo: {
              $filter: {
                input: '$bumeninfo',
                as: 'item',
                cond: { $ne: ['$$item._id', '$_id'] },
              },
            },
          },
        },
        {
          $unwind: '$bumeninfo',
        },
        {
          $match: {
            'bumeninfo.topLevelOfTheEnterprise': true,
          },
        },
        {
          $group: {
            _id: '$_id',
            minDepth: { $min: '$bumeninfo.depth' },
            bumeninfo: { $push: '$bumeninfo' },
          },
        },
        {
          $unwind: '$bumeninfo',
        },
        {
          $match: {
            $expr: {
              $eq: ['$bumeninfo.depth', '$minDepth'],
            },
          },
        },
        {
          $addFields: {
            targetEnterpriseID: '$bumeninfo.EnterpriseID',
            EnterpriseID: '$_id',
            messageStatus: 2,
            isUp: true,
            parentId: ['$bumeninfo.EnterpriseID'], // Ensure parentId is an array
          },
        },
        {
          $project: {
            _id: 0,
            minDepth: 0,
            bumeninfo: 0,
          },
        },
        {
          $lookup: {
            from: 'adminorgs',
            localField: 'EnterpriseID',
            foreignField: '_id',
            as: 'adminorgs',
          },
        },
        {
          $unwind: {
            path: '$adminorgs',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            'adminorgs.parentId': {
              $cond: {
                if: { $isArray: '$adminorgs.parentId' },
                then: '$adminorgs.parentId',
                else: {
                  $cond: {
                    if: { $ne: ['$adminorgs.parentId', null] },
                    then: ['$adminorgs.parentId'],
                    else: [],
                  },
                },
              },
            },
            parentId: {
              $cond: {
                if: { $isArray: '$parentId' },
                then: '$parentId',
                else: {
                  $cond: {
                    if: { $ne: ['$parentId', null] },
                    then: ['$parentId'],
                    else: [],
                  },
                },
              },
            },
          },
        },
        {
          $addFields: {
            'adminorgs.parentId': {
              $concatArrays: ['$parentId'],
            },
            _id: '$EnterpriseID',
            parentId: '$parentId',
          },
        },
        {
          $project: {
            _id: 1,
            parentId: 1,
          },
        },
      ];

      const parentIdResult = await this.dingtreesRepository
        .aggregate(parentIdLine)
        .toArray();

      for (const item of parentIdResult) {
        await this.adminorgsRepository.updateOne(
          { _id: item._id },
          { $set: { parentId: item.parentId } },
          { upsert: true },
        );
      }
      // childrenId
      const childrenIdLine = [
        {
          $match: {
            _id: { $in: topDepart },
          },
        },
        {
          $unwind: {
            path: '$parentId',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'adminorgs',
            localField: '_id',
            foreignField: 'parentId',
            as: 'adminorgChild',
          },
        },
        {
          $group: {
            _id: '$_id',
            adminorgChild: {
              $push: '$adminorgChild',
            },
            doc: {
              $first: '$$ROOT',
            },
          },
        },
        {
          $replaceRoot: {
            newRoot: {
              $mergeObjects: [
                '$doc',
                {
                  adminorgChild: {
                    $reduce: {
                      input: '$adminorgChild',
                      initialValue: [],
                      in: {
                        $concatArrays: ['$$value', '$$this'],
                      },
                    },
                  },
                },
              ],
            },
          },
        },
        {
          $addFields: {
            childrenId: {
              $cond: {
                if: {
                  $gt: [{ $size: '$adminorgChild' }, 0],
                },
                then: {
                  $map: {
                    input: '$adminorgChild',
                    as: 'child',
                    in: '$$child._id',
                  },
                },
                else: [],
              },
            },
          },
        },
        {
          $project: {
            adminorgChild: 0, // 可以移除不需要的字段
          },
        },
      ];
      const childrenIdResult = await this.adminorgsRepository
        .aggregate(childrenIdLine)
        .toArray();

      for (const item of childrenIdResult) {
        await this.adminorgsRepository.updateOne(
          { _id: item._id },
          { $set: { childrenId: item.childrenId } },
          { upsert: true },
        );
      }
    } catch (error) {
      this.logger.error(
        `处理公司上下层级，添加adminorgs里的parentId，childrenId失败:code:${topDepart}`,
        error,
      );
    }
  }
}
