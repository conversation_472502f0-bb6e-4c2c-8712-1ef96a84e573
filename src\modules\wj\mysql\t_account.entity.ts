import {
  Entity,
  Column,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
// 登录账号
@Entity('t_account')
export class TAccount {
  @PrimaryColumn({ type: 'varchar', length: 64, comment: 'ID' })
  id: string;

  @Column({
    type: 'varchar',
    length: 100,
    default: 'SysUser',
    comment: '用户类型',
  })
  user_type: string;

  @Column({ type: 'varchar', length: 64, comment: '用户ID' })
  user_id: string;

  @Column({ type: 'varchar', length: 20, default: 'PWD', comment: '认证方式' })
  auth_type: string;

  @Column({ type: 'varchar', length: 100, comment: '登录时的用户名' })
  auth_account: string;

  @Column({ type: 'varchar', length: 64, nullable: true, comment: '密码' })
  auth_secret: string;

  @Column({ type: 'varchar', length: 64, nullable: true, comment: '加密盐' })
  secret_salt: string;

  @Column({ type: 'int', default: 1, comment: '用户状态' })
  status: number;

  @Column({ type: 'tinyint', default: 0, comment: '是否删除' })
  is_deleted: number;

  @CreateDateColumn({ type: 'timestamp', comment: '创建时间' })
  create_at: Date;

  @Column({ type: 'varchar', length: 256, nullable: true })
  create_by: string;

  @UpdateDateColumn({ type: 'timestamp', comment: '更新时间' })
  update_at: Date;

  @Column({ type: 'varchar', length: 256, nullable: true })
  update_by: string;
}
