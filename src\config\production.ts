export default () => ({
  database_mongodb: {
    name: 'mongodbConnection',
    type: process.env.mdbScheme,
    replicaSet: process.env.mdbRs,
    // set host for none replica set, and hostReplicaSet for replica set
    hostReplicaSet: 'i11.zyws.cn,i12.zyws.cn,i13.zyws.cn',
    readPreference: 'nearest',
    ssl: false,
    useNewUrlParser: true,
    useUnifiedTopology: true,
    authSource: 'admin',
    username: process.env.mdbUser,
    password: process.env.mdbPass,
    database: process.env.mdbName,
    logging: 'info',
  },
  database_mysql: {
    name: 'mysqlConnection',
    type: 'mysql',
    host: process.env.mysqlHost || 'zyws.cn',
    port: +process.env.mysqlPort || 30366,
    username: process.env.mysqlUser || 'root',
    password: process.env.mysqlPass,
    database: process.env.mysqlDb || 'jkqy_survey',
    logging: 'info',
    synchronize: false,
    timezone: '-08:00',
  },
});
