import { Entity, Column, PrimaryColumn, Index, OneToMany } from 'typeorm';
import { Appointment } from './appointment.entity';
import { RehabGuideApplication } from './rehab-guide-application.entity';

@Entity('disease_category_dict')
export class DiseaseCategoryDict {
  @PrimaryColumn({ type: 'char', length: 7, comment: '职业病分类编码' })
  category_code: string;

  @Column({ type: 'varchar', length: 100, comment: '职业病分类名称' })
  category_name: string;

  @Column({ type: 'int', default: 0, comment: '排序顺序' })
  @Index('idx_sort_order')
  sort_order: number;

  @Column({
    type: 'tinyint',
    width: 1,
    default: 1,
    comment: '是否启用：1-启用，0-禁用',
  })
  @Index('idx_is_enabled')
  is_enabled: boolean;

  @Column({
    type: 'datetime',
    default: () => 'CURRENT_TIMESTAMP',
    comment: '创建时间',
  })
  create_time: Date;

  @Column({
    type: 'datetime',
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
    comment: '更新时间',
  })
  update_time: Date;

  @OneToMany(
    () => Appointment,
    (appointment) => appointment.disease_category_relation,
  )
  appointments: Appointment[];

  @OneToMany(
    () => RehabGuideApplication,
    (application) => application.disease_category_relation,
  )
  rehab_guide_applications: RehabGuideApplication[];
}
