import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Adminorgs } from './adminorgs.entity';
import { MongoRepository } from 'typeorm';

@Injectable()
export class AdminorgsService {
  constructor(
    @InjectRepository(Adminorgs, 'mongodbConnection')
    private adminorgsRepository: MongoRepository<Adminorgs>,
  ) {}

  async findOne(query: { _id?: string }): Promise<Adminorgs> {
    return await this.adminorgsRepository.findOne({
      where: query,
      select: [
        '_id',
        'cname',
        'code',
        'regAdd',
        'contract',
        'phoneNum',
        'districtRegAdd',
        'adminUserId',
      ],
    });
  }

  async findList(query: {
    pageSize?: number | string;
    curPage?: number | string;
    keyWord?: string;
  }): Promise<{ total: number; list: object[] }> {
    const limit = Number(query.pageSize) || 10;
    const curPage = Number(query.curPage) || 1;

    const where: any = {};
    if (query.keyWord) {
      where['$or'] = [
        { cname: { $regex: query.keyWord, $options: 'i' } },
        { code: { $regex: query.keyWord, $options: 'i' } },
      ];
    }

    const [list, total] = await this.adminorgsRepository.findAndCount({
      where,
      skip: (curPage - 1) * limit,
      take: limit,
    });

    const formattedList = list.map((item) => ({
      _id: item._id,
      cname: item.cname,
      code: item.code,
    }));

    return {
      total,
      list: formattedList,
    };
  }
}
