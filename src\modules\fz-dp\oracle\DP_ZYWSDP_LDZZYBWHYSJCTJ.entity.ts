import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';
@Entity({
  name: 'DP_ZYWSDP_LDZZYBWHYSJCTJ',
  comment: '职业卫生大屏-劳动者职业病危害因素接触统计',
})
export class DP_ZYWSDP_LDZZYBWHYSJCTJ {
  @PrimaryGeneratedColumn()
  ID: number;

  @Column({ type: 'varchar2', length: 255, comment: '地区' })
  DQ: string;

  @Column({ type: 'varchar2', length: 255, comment: '年份' })
  NF: string;

  @Column({ type: 'varchar2', length: 255, comment: '各类粉尘' })
  GLFC: string;

  @Column({ type: 'varchar2', length: 255, comment: '各类化学毒物' })
  GLHXDW: string;

  @Column({ type: 'varchar2', length: 255, comment: '各类物理因素' })
  GLWLYS: string;

  @Column({ type: 'varchar2', length: 255, comment: '年度接触总人数' })
  NDJCZRS: string;

  @Column({ type: 'varchar2', length: 255, comment: '占比' })
  ZB: string;
}
