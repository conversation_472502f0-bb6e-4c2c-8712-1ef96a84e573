import { Modu<PERSON> } from '@nestjs/common';
import { <PERSON>beiController } from './hebei.controller';
import { QykService } from './qyk.service'; // 用人单位统计
import { QyOnlineDeclarationService } from './qyOnlineDeclaration.service'; // 用人单位统计
import { TypeOrmModule } from '@nestjs/typeorm';
import { OdsEntTjQykNew } from './mysql/ods_ent_tj_qyk_new.entity';
import { TjQykGssj } from './mysql/tj_qyk_gssj.entity';
import { DimDistrict } from './mysql/dim_district.entity';
import { TjTechnicalQualityItem } from './mysql/tj_technicalquality_item.entity';
import { UnifiedAccountModule } from '../unified-account/unified-account.module';
import { TjTechnicalqualityItemKzxgPlan } from './mysql/tj_technicalquality_item_kzxg_plan.entity';
import { TjHealthCheckJl } from './mysql/tj_health_check_jl.entity';
import { TjHealthCheck } from './mysql/tj_health_check.entity';
import { wjOccupationalDisease } from './mysql/wj_occupational_disease.entity';
import { ngProduct } from './mysql/ng_product.entity';
import { ngRoster } from './mysql/ng_roster.entity';
import { ngAudit } from './mysql/ng_audit.entity';
import { ngHazardDetect } from './mysql/ng_hazard_detect.entity';
import { ngHazardExam } from './mysql/ng_hazard_exam.entity';
import { ngHazardExposed } from './mysql/ng_hazard_exposed.entity';
import { ngDeclaration } from './mysql/ng_declaration.entity';
import { dimEconomic } from './mysql/dim_economic.entity';
import { dimIndustry } from './mysql/dim_industry.entity';
import { dimEmployerHazard } from './mysql/dim_employer_hazard.entity';
import { dimHazard } from './mysql/dim_hazard.entity';
import { UserService } from './user.service';
import { NgUser } from './mysql/ng_user.entity';
import { NgEmployer } from './mysql/ng_employer.entity';
import { EmployerService } from './employer.service';
import { DimEmployerIndustry } from './mysql/dim_employer_industry.entity';
import { ngMonitor } from './mysql/ng_monitor.entity';
import { ngSupervisor } from './mysql/ng_supervisor.entity';
import { tjHealthCompany } from './mysql/tj_health_company.entity';
import { tjTechnicalqualityCompany } from './mysql/tj_technicalquality_company.entity';
import { PrJgjgProject } from './mysql/pr_jgjg_project.entity';
import { RosterService } from './roster.service';
@Module({
  imports: [
    TypeOrmModule.forFeature(
      [
        DimDistrict,
        wjOccupationalDisease,
        ngProduct,
        ngRoster,
        ngAudit,
        ngHazardDetect,
        ngHazardExam,
        ngHazardExposed,
        ngDeclaration,
        dimEconomic,
        dimIndustry,
        dimEmployerHazard,
        NgUser,
        NgEmployer,
        ngMonitor,
        DimEmployerIndustry,
        dimHazard,
        ngSupervisor,
      ],
      'mysqlConnectionHebei',
    ),
    // TypeOrmModule.forFeature(
    //   [
    //     // DimDistrict,
    //     // wjOccupationalDisease,
    //     // ngProduct,
    //     // ngRoster,
    //     ngAudit,
    //     // ngHazardDetect,
    //     // ngHazardExam,
    //     // ngHazardExposed,
    //     ngDeclaration,
    //     // dimEconomic,
    //     // dimIndustry,
    //     // dimEmployerHazard,
    //     // NgUser,
    //     // NgEmployer,
    //     // ngMonitor,
    //     // DimEmployerIndustry,
    //     // dimHazard,
    //     // ngSupervisor,
    //   ],
    //   'mysqlConnectionXjbt',
    // ),
    TypeOrmModule.forFeature(
      [
        OdsEntTjQykNew,
        TjQykGssj,
        TjTechnicalqualityItemKzxgPlan,
        TjHealthCheckJl,
        TjHealthCheck,
        tjHealthCompany,
        TjTechnicalQualityItem,
        tjTechnicalqualityCompany,
      ],
      'mysqlConnectionHebeiTy',
    ),
    TypeOrmModule.forFeature([PrJgjgProject], 'mysqlConnectionHebeiPr'),
    UnifiedAccountModule,
  ],
  controllers: [HebeiController],
  providers: [
    QykService,
    QyOnlineDeclarationService,
    UserService,
    EmployerService,
    RosterService,
  ],
  exports: [
    QykService,
    QyOnlineDeclarationService,
    UserService,
    EmployerService,
    RosterService,
  ],
})
export class HebeiModule {}
