import {
  Entity,
  ObjectIdColumn,
  Column,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import * as CryptoJS from 'crypto-js';
// import { User } from './users.entity';

class occupationalDisease {
  @Column({ type: 'varchar', comment: '职业病名称' })
  name: string;

  @Column({ type: 'varchar', comment: '职业病编码' })
  code: string;
};

@Entity('users')
// @Index('index_departs', ['departs'])
// @Index('index_name', ['name'])
// @Index('index_phoneNum', ['phoneNum'], { unique: true })
// @Index('index_userName', ['userName'], { unique: true })
export class Users {
  @ObjectIdColumn()
  _id: string;

  @Column({ type: 'varchar', default: '' })
  unitCode: string;

  @Column({ type: 'varchar' })
  idNo: string;

  @Column({ type: 'varchar', default: 'iservice2' })
  source: string;

  @Column({ type: 'varchar' })
  userName: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar' })
  phoneNum: string;

  @Column({ type: 'varchar', default: '86' })
  countryCode: string;

  @Column({ type: 'varchar', default: '1' })
  idType: string;

  @Column({ type: 'varchar' })
  position: string;

  @Column({ type: 'varchar' })
  company: string;

  @Column({ type: 'varchar' })
  employeeId: string;

  @Column()
  companyId: string[];

  @Column({ type: 'int' })
  companyStatus: number;

  @Column({ type: 'date' })
  date: Date;

  @Column({ type: 'date' })
  updateTime: Date;

  @Column({ type: 'varchar' })
  group: string;

  @Column({ type: 'date' })
  birth: Date;

  @Column({ type: 'varchar' })
  gender: string;

  @Column({ type: 'varchar' })
  state: string;

  @Column()
  password: string;

  @Column({ type: 'boolean', default: false, comment: '是否患有职业病' })
  hasOccupationalDisease: boolean;

  @Column({ type: 'varchar', comment: '职业病描述' })
  occupationalDiseaseDescription: string;

  @Column(_type => occupationalDisease)
  occupationalDisease: occupationalDisease[] = [];

  @BeforeInsert()
  @BeforeUpdate()
  async savePassword() {
    if (this.password && this.password.length < 30) {
      this.password = CryptoJS.SHA256(
        this.password + '7a58917b-d493-4940-87d4-ab2b0fa8a3b7',
      ).toString();
    }
  }
}
