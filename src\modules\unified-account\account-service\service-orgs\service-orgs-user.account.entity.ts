import { Entity, Column, CreateDateColumn, ObjectIdColumn } from 'typeorm';
import { IsString, IsEmail, IsNotEmpty, IsArray } from 'class-validator';
import { nanoid } from 'nanoid';

@Entity('serviceUsers')
export class ServiceUser {
  @ObjectIdColumn()
  _id: string;

  @Column({ comment: '用户名' })
  @IsString()
  @IsNotEmpty()
  userName: string;

  @Column({ comment: '姓名' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @Column({ comment: '手机号' })
  @IsString()
  phoneNum: string;

  @Column({ comment: '邮箱' })
  @IsEmail()
  email: string;

  @Column({ comment: '密码' })
  password: string;

  @Column({ comment: '密码有效期', nullable: true })
  passwordExpiresAt: Date;

  @Column({ comment: '登录尝试次数', type: 'int', default: 0 })
  loginAttempts: number;

  @Column({ comment: '登录尝试时间', type: 'json', default: [] })
  loginAttemptsTimestamp: Array<Date>;

  @Column({ comment: '所属机构ID' })
  org_id: string;

  @Column({ comment: '所属机构名' })
  org: string;

  @Column({ comment: '所属部门ID' })
  department_id: string;

  @Column({ comment: '所属部门名称' })
  department: string;

  @Column({ comment: '是否机构VIP用户', default: false })
  isQLC: boolean;

  @Column()
  @IsString()
  group: string;

  @Column({ comment: '国家代码', default: '86' })
  countryCode: string;

  @CreateDateColumn({ comment: '创建时间' })
  ctime: Date;

  @Column({ comment: '头像', default: '/static/upload/images/defaultlogo.png' })
  logo: string;

  @Column({ comment: '在职状态', default: true })
  state: boolean;

  @Column({ comment: '是否启用', default: true })
  enable: boolean;

  @Column({ comment: '备注', nullable: true })
  comments: string;

  @Column({ comment: '已阅读的上一个更新版本的版本ID', nullable: true })
  readVersionId: string;

  @Column()
  @IsArray()
  versions: string[];

  constructor() {
    this._id = this._id || nanoid();
    this.userName = this.userName || '';
    this.name = this.name || '';
    this.phoneNum = this.phoneNum || '';
    this.logo = this.logo || '/static/upload/images/defaultlogo.png';
    this.state = this.state === false ? false : true;
    this.enable = this.enable === false ? false : true;
    this.countryCode = this.countryCode || '86';
    this.loginAttempts = this.loginAttempts || 0;
    this.isQLC = this.isQLC || false;
  }
}
