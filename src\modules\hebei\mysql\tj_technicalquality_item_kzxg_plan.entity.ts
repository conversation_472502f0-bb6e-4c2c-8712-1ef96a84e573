import { Entity, Column, PrimaryColumn } from 'typeorm';

@Entity('tj_technicalquality_item_kzxg_plan')
export class TjTechnicalqualityItemKzxgPlan {
  @PrimaryColumn({ type: 'varchar', length: 64, comment: 'id' })
  id: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '项目名称',
  })
  item_name: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '项目单位',
  })
  project: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '项目单位地址',
  })
  project_address: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '项目联系人',
  })
  contacts: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '项目联系电话',
  })
  contacts_phone: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '项目联系邮箱',
  })
  contacts_email: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '项目类型' })
  project_type: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '所属行业' })
  industry: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '法人' })
  legal_person: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '项目负责人',
  })
  project_leader: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '控效报告单位危害程度',
  })
  kzxg_risk_level: string;

  @Column({ type: 'date', comment: '职业病防护设施验收时间' })
  ys_accept_date: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '报告接受部门',
  })
  receive_dept: string;

  @Column({ type: 'date', comment: '报告日期' })
  report_date: string;
}
