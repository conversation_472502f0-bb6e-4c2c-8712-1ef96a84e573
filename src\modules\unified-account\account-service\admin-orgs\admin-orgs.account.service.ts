import { In, MongoRepository } from 'typeorm';
import { plainToClass } from 'class-transformer';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { AccountService } from '../account-service.interface';
import { AdminOrgsOrg } from './admin-orgs-org.account.entity';
import { AdminOrgsUser } from './admin-orgs-user.account.entity';
import { UNIFIED_ACCOUNT_CREATED } from '../../events/constants';
import { END_POINT } from 'src/constants/endPoint';
import {
  CreateAdminAccountData,
  CreateAdminUserData,
  CreateAdminOrgData,
  QueryAdminOrgData,
} from '../../types';

@Injectable()
export class AdminOrgsAccountService
  implements AccountService<CreateAdminAccountData, AdminOrgsUser>
{
  constructor(
    @InjectRepository(AdminOrgsOrg, 'mongodbConnection')
    private readonly AdminOrgsOrgRepository: MongoRepository<AdminOrgsOrg>,
    @InjectRepository(AdminOrgsUser, 'mongodbConnection')
    private readonly AdminOrgsUserRepository: MongoRepository<AdminOrgsUser>,

    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {}
  private readonly logger = new Logger(AdminOrgsAccountService.name);

  async upsertSubAccounts(
    data: CreateAdminAccountData,
  ): Promise<AdminOrgsUser> {
    const user = await this.upsertUserAccount(data.user);
    const org = await this.upsertOrgAccount(data.org);
    await this.relationOrgManager(org, user);

    this.eventEmitter.emit(UNIFIED_ACCOUNT_CREATED, {
      endPoint: END_POINT.qy,
      org,
      user,
    });

    return user;
  }

  async upsertOrgAccount(data: CreateAdminOrgData): Promise<AdminOrgsOrg> {
    let org = await this.getOrgAccountByCode(data.code);
    if (!org) {
      this.logger.log(`create org account ${JSON.stringify(data)}`);
      org = await this.createOrgAccount(data);
    } else {
      // org = await this.authExistOrgAccount(data);
      this.logger.log(`org 已经存在： ${JSON.stringify(org)}`);
    }
    return org;
  }

  async upsertUserAccount(data: CreateAdminUserData): Promise<AdminOrgsUser> {
    let user = await this.getUserAccountByPhoneNum(data.phoneNum);
    if (!user) {
      this.logger.log(`create admin user account ${JSON.stringify(data)}`);
      user = await this.createUserAccount(data);
    } else {
      this.logger.log(`admin user 已经存在： ${JSON.stringify(user)}`);
      // user = await this.authExistUserAccount(data);
    }

    return user;
  }

  async getAccount(id: string): Promise<AdminOrgsUser> {
    const user = await this.AdminOrgsUserRepository.findOne({
      where: { _id: id },
    });
    return user;
  }

  private async getOrgAccountByCode(code: string): Promise<AdminOrgsOrg> {
    const org = await this.AdminOrgsOrgRepository.findOne({
      where: { code },
    });
    return org;
  }

  async getUserAccountByPhoneNum(phoneNum: string): Promise<AdminOrgsUser> {
    const user = await this.AdminOrgsUserRepository.findOne({
      where: { phoneNum },
    });
    return user;
  }

  async getUserAccountById(id: string): Promise<AdminOrgsUser> {
    const user = await this.AdminOrgsUserRepository.findOne({
      where: { _id: id },
    });
    return user;
  }

  private async getUserAccountListByIds(
    ids: string[],
  ): Promise<AdminOrgsUser[]> {
    const user = await this.AdminOrgsUserRepository.find({
      where: { _id: In(ids) },
    });
    return user;
  }

  private async getUserAccountByOrgAndPhoneNum(
    org: AdminOrgsOrg,
    phoneNum: string,
  ): Promise<AdminOrgsUser> {
    // 需要从 org 的管理员中查找 user，而不是用电话号码查找
    const userIds = [org.adminUserId, ...org.adminArray].filter((id) => id);
    const users = await this.getUserAccountListByIds(userIds);
    const user = users.find((user) => user.phoneNum === phoneNum);
    return user;
  }

  private async queryOrgByAdminUserId(id: string): Promise<AdminOrgsOrg> {
    const org = await this.AdminOrgsOrgRepository.findOne({
      where: { adminUserId: id },
    });
    return org;
  }

  async getAccountByQuery(query: QueryAdminOrgData): Promise<AdminOrgsUser> {
    const org = await this.getOrgAccountByCode(query.code);
    if (!org) {
      return null;
    }
    const user = await this.getUserAccountByOrgAndPhoneNum(org, query.phoneNum);
    return user;
  }

  async createUserAccount(data: CreateAdminUserData): Promise<AdminOrgsUser> {
    const user = await this.AdminOrgsUserRepository.save(
      plainToClass(AdminOrgsUser, {
        group: this.configService.get('groupID').adminGroupID,
        ...data,
      }),
    );
    return user;
  }

  async createOrgAccount(data: CreateAdminOrgData): Promise<AdminOrgsOrg> {
    const org = await this.AdminOrgsOrgRepository.save(
      plainToClass(AdminOrgsOrg, {
        ...data,
      }),
    );
    return org;
  }

  async relationOrgManager(
    org: AdminOrgsOrg,
    user: AdminOrgsUser,
  ): Promise<void> {
    const _set = {};
    if (!org.adminUserId) {
      _set['adminUserId'] = user._id;
    }
    if (!org.adminArray?.includes(user._id)) {
      _set['adminArray'] = [...org.adminArray, user._id];
    }
    await this.AdminOrgsOrgRepository.updateOne(
      {
        _id: org._id,
      },
      {
        $set: _set,
      },
    );
  }

  async updateOrgAccount(
    orgId: string,
    data: Partial<AdminOrgsOrg>,
  ): Promise<AdminOrgsOrg> {
    console.log('开始更新用人单位信息', orgId, data);
    const result = await this.AdminOrgsOrgRepository.updateOne(
      { _id: orgId },
      { $set: data },
      // { upsert: false }  // 如果不存在则不创建新记录
    );
    return plainToClass(AdminOrgsOrg, result);
  }

  async updateUserAccount(
    userId: string,
    data: Partial<AdminOrgsUser>,
  ): Promise<AdminOrgsUser> {
    const result = await this.AdminOrgsUserRepository.updateOne(
      { _id: userId },
      { $set: data },
      // { upsert: false }  // 如果不存在则不创建新记录
    );
    return plainToClass(AdminOrgsUser, result);
  }

  async validateAccount(id: string): Promise<boolean> {
    let validate = false;
    const user = await this.getUserAccountById(id);
    if (user) {
      const org = await this.queryOrgByAdminUserId(id);
      validate = !!org && org.adminArray.includes(id);
    }
    return validate;
  }

  // async fixAdminUser(id: string, phoneNum: string): Promise<string> {
  //   let user = await this.getUserAccountById(id);
  //   if (!user) {
  //     user = await this.getUserAccountByPhoneNum(phoneNum);
  //     if (!user) {
  //       user = await this.createUserAccount({
  //         phoneNum: phoneNum,
  //         name: phoneNum,
  //         userName: phoneNum,
  //       });
  //     }
  //   }
  //   return user._id;
  // }

  private async authExistUserAccount(
    data: CreateAdminUserData,
  ): Promise<AdminOrgsUser> {
    await this.removeUserAccountByPhoneNum(data.phoneNum);
    const user = await this.createUserAccount(data);
    return user;
  }

  // private async authExistOrgAccount(data: CreateAdminOrgData): Promise<AdminOrgsOrg> {
  //   const org = await this.AdminOrgsOrgRepository.save(
  //     plainToClass(AdminOrgsOrg, {
  //       ...data,
  //     }),
  //   );
  //   return org;
  // }

  private async removeUserAccountByPhoneNum(phoneNum: string) {
    const user = await this.getUserAccountByPhoneNum(phoneNum);
    const userId = user?._id;
    if (user) {
      this.logger.log(`删除用户 ${phoneNum} ${userId} 的账号`);
      await this.AdminOrgsUserRepository.deleteOne({
        _id: userId,
      });
      const org = await this.getRelationOrgByUserId(userId);
      if (org) {
        await this.dissolveRelation(org, userId);
      }
    }
  }

  private async getRelationOrgByUserId(userId: string) {
    const org = await this.AdminOrgsOrgRepository.findOne({
      where: {
        $or: [{ adminUserId: userId }, { adminArray: { $in: [userId] } }],
      },
    });
    return org;
  }

  private async dissolveRelation(org, userId) {
    org.adminArray = org.adminArray.filter((id) => id !== userId);
    org.adminUserId = org.adminArray[0] ?? '';
    await this.AdminOrgsOrgRepository.updateOne(
      { _id: org._id },
      { $set: org },
    );
  }

  async deregisterAccount({
    unifiedCode,
  }: {
    unifiedCode: string;
  }): Promise<void> {
    this.logger.log(`删除企业: ${unifiedCode}`);
    await this.AdminOrgsOrgRepository.updateOne(
      { code: unifiedCode },
      {
        $set: {
          adminUserId: '',
          adminArray: [],
          isDelete: true,
          productionStatus: '0',
        },
      },
      // { upsert: false }  // 如果不存在则不创建新记录
    );
  }
}
