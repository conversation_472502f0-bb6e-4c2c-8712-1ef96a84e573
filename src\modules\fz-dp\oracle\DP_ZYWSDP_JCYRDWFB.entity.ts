import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';
@Entity({
  name: 'DP_ZYWSDP_JCYRDWFB', // 没有统计数据
  comment: '职业卫生大屏-监测用人单位分布',
})
export class DP_ZYWSDP_JCYRDWFB {
  @PrimaryGeneratedColumn()
  ID: number;

  @Column({ type: 'varchar2', length: 255, comment: '地区' })
  DQ: string;

  @Column({ type: 'varchar2', length: 255, comment: '行业及规模分布' })
  HYJGMFB: string;

  @Column({ type: 'varchar2', length: 255, comment: '经济类型与规模分布' })
  JJLXYGMFB: string;
}
