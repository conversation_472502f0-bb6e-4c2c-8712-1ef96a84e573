import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Todo } from './mysql/todo.entity';
import { TodoType } from './mysql/todo-type.entity';
import { TodoMeta } from './mysql/todo-meta.entity';
import { CreateTodoDto } from './dto/create-todo.dto';
import { UpdateTodoDto } from './dto/update-todo.dto';
import { FindTodoDto } from './dto/find-todo.dto';
import { DATA_SOURCE_NAME, TodoItem, TodoPagination } from './constants';

@Injectable()
export class TodoService {
  constructor(
    @InjectRepository(Todo, DATA_SOURCE_NAME)
    private readonly todoRepository: Repository<Todo>,
    @InjectRepository(TodoType, DATA_SOURCE_NAME)
    private readonly todoTypeRepository: Repository<TodoType>,
    @InjectRepository(TodoMeta, DATA_SOURCE_NAME)
    private readonly todoMetaRepository: Repository<TodoMeta>,
  ) {}

  async create(createTodoDto: CreateTodoDto, user: { id: string }): Promise<Todo> {
    const { meta, type, ...todoData } = createTodoDto;

    // Check if todo type exists
    const todoType = await this.todoTypeRepository.findOne({
      where: { code: createTodoDto.type },
    });
    if (!todoType) {
      throw new NotFoundException(`Todo type with code ${createTodoDto.type} not found`);
    }

    // Start transaction
    const queryRunner = this.todoRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create todo
      const todo = queryRunner.manager.create(Todo, {
        ...todoData,
        typeCode: todoType.code,
        userId: user.id,
        status: 0,
      });
      const savedTodo = await queryRunner.manager.save(todo);

      // Create meta if provided
      if (meta) {
        const todoMeta = queryRunner.manager.create(TodoMeta, {
          todoId: savedTodo.id,
          metaData: meta,
        });
        await queryRunner.manager.save(todoMeta);
      }

      await queryRunner.commitTransaction();

      return this.findOne(savedTodo.id, user);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async findAll(findTodoDto: FindTodoDto, user: { id: string }): Promise<[TodoItem[], TodoPagination]> {
    const { page = 1, pageSize = 10, ...filters } = findTodoDto;
    const skip = (page - 1) * pageSize;

    const queryBuilder = this.todoRepository
      .createQueryBuilder('todo')
      .leftJoinAndSelect('todo.type', 'type')
      .leftJoinAndSelect('todo.meta', 'meta')
      .where('todo.userId = :userId', { userId: user.id });

    // Apply filters
    if (filters.type) {
      queryBuilder.andWhere('todo.typeCode = :typeCode', { typeCode: filters.type });
    }
    if (filters.title) {
      queryBuilder.andWhere('todo.title LIKE :title', { title: `%${filters.title}%` });
    }
    if (filters.status !== undefined) {
      queryBuilder.andWhere('todo.status = :status', { status: filters.status });
    }

    // Add pagination
    queryBuilder.skip(skip).take(pageSize);

    // Add sorting
    queryBuilder.orderBy('todo.createdAt', 'DESC');

    const [items, total] = await queryBuilder.getManyAndCount();

    return [
      items.map((item) => ({
        id: item.id,
        type: item.typeCode,
        title: item.title,
        status: item.status,
        createdAt: item.createdAt,
        description: item.description,
        meta: item.meta?.metaData
      })),
      {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize),
      }
    ];
  }

  async findOne(id: number, user: { id: string }): Promise<Todo> {
    const todo = await this.todoRepository.findOne({
      where: { 
        id,
        userId: user.id 
      },
      relations: ['type', 'meta'],
    });

    if (!todo) {
      throw new NotFoundException(`Todo with ID ${id} not found or unauthorized`);
    }

    return todo;
  }

  async update(id: number, updateTodoDto: UpdateTodoDto, user: { id: string }): Promise<Todo> {
    const todo = await this.findOne(id, user);
    const { meta, type, ...todoData } = updateTodoDto;

    // Start transaction
    const queryRunner = this.todoRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Update todo
      if (Object.keys(todoData).length > 0 || type) {
        const updateData = { ...todoData };
        if (type) {
          const todoType = await this.todoTypeRepository.findOne({
            where: { code: type },
          });
          if (!todoType) {
            throw new NotFoundException(`Todo type with code ${type} not found`);
          }
          // updateData.typeCode = type;
        }
        await queryRunner.manager.update(Todo, { id, userId: user.id }, updateData);
      }

      // Update meta if provided
      if (meta && todo.meta) {
        await queryRunner.manager.update(TodoMeta, { todoId: id }, { metaData: meta });
      } else if (meta && !todo.meta) {
        const todoMeta = queryRunner.manager.create(TodoMeta, {
          todoId: id,
          metaData: meta,
        });
        await queryRunner.manager.save(todoMeta);
      }

      await queryRunner.commitTransaction();
      return this.findOne(id, user);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async completeOne(id: number, user: { id: string }): Promise<Todo> {
    return this.updateStatus(id, user, 1);
  }

  private async updateStatus(id: number, user: { id: string }, status: number): Promise<Todo> {
    const todo = await this.todoRepository.findOne({
      where: { id, userId: user.id },
    });

    if (!todo) {
      throw new NotFoundException(`Todo with ID ${id} not found or not owned by user`);
    }

    todo.status = status;
    return this.todoRepository.save(todo);
  }

  async remove(id: number, user: { id: string }): Promise<void> {
    const todo = await this.findOne(id, user);

    // Start transaction
    const queryRunner = this.todoRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Delete meta first (if exists)
      if (todo.meta) {
        await queryRunner.manager.delete(TodoMeta, { todoId: id });
      }

      // Delete todo
      await queryRunner.manager.delete(Todo, { id, userId: user.id });

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
}
