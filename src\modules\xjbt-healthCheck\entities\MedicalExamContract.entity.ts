import {
  Entity,
  ObjectIdColumn,
  Column,
  ManyToOne,
  <PERSON>in<PERSON><PERSON><PERSON>n,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

// 假设相关实体已存在
// src/modules/unified-account/account-service/physical-exam/physical-exam-org.account.entity.ts
import { PhysicalExamOrg } from '../../unified-account/account-service/physical-exam/physical-exam-org.account.entity';
import { Adminorgs } from '../../adminorgs/adminorgs.entity';

@Entity('MedicalExamContracts')
export class MedicalExamContract {
  @ObjectIdColumn()
  _id: string;

  @Column({ type: 'timestamp', nullable: true, comment: '体检开始日期' })
  examStartDate?: Date;

  @Column({ type: 'timestamp', nullable: true, comment: '体检结束日期' })
  examEndDate?: Date;

  @Column({ type: 'float', nullable: true, comment: '合同金额' })
  contractAmount?: number;

  @Column({ type: 'int', nullable: true, comment: '体检总人数' })
  contractPeopleNum?: number;

  @Column({
    type: 'enum',
    enum: [0, 1, 2],
    nullable: true,
    comment: '体检类别 0离岗 1岗前 2在岗'
  })
  contractType?: number;

  @Column({ type: 'timestamp', nullable: true, comment: '合同起草日期' })
  contractDraftDate?: Date;

  @Column({ type: 'timestamp', nullable: true, comment: '合同签订日期' })
  contractSignDate?: Date;

  @Column({
    type: 'enum',
    enum: [0, 1, 2],
    nullable: true,
    comment: '审核状态 0待审核 1已拒绝 2已审核'
  })
  auditStatu?: number;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '体检机构联系人' })
  agencyContact?: string;

  @Column({ type: 'varchar', length: 50, nullable: true, comment: '体检机构联系电话' })
  agencyPhone?: string;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '企业联系人' })
  companyContact?: string;

  @Column({ type: 'varchar', length: 50, nullable: true, comment: '企业联系方式' })
  companyPhone?: string;

  @Column('json', { nullable: true, comment: '合同名称和路径' })
  contractFile?: { fileName: string; fileUrl: string }[];

  @ManyToOne(() => PhysicalExamOrg, { nullable: true })
  @JoinColumn({ name: 'physicalOrgId' })
  physicalOrg?: PhysicalExamOrg;

  @ManyToOne(() => Adminorgs, { nullable: true })
  @JoinColumn({ name: 'cId' })
  company?: Adminorgs;

  @Column({
    type: 'enum',
    enum: [0, 1],
    nullable: true,
    comment: '合同发起方 0体检机构发起 1企业发起'
  })
  contractInitiator?: number;

  @Column({ type: 'timestamp', nullable: true, comment: '创建日期' })
  createDate?: Date;

  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date;
}