import { Entity, Column, ObjectIdColumn, Index } from 'typeorm';
import { ObjectId } from 'mongodb';

// 大屏统计数据
@Entity('dpStatistical')
export class DpStatistical {
  @ObjectIdColumn()
  _id: ObjectId;

  @Column({ comment: '当前所统计的行政区划代码 12位' })
  @Index()
  adcode: string;

  @Column({ comment: '大屏数据的表名, 用于区分不同的数据' })
  @Index()
  table: string;

  @Column({ comment: '统计的数据' })
  data: [];

  @Column()
  updatedAt: Date;
}
