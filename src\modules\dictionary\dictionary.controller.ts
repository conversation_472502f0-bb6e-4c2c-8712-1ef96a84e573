import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
} from '@nestjs/common';
import { wrapperResponse } from 'src/utils';
import { DictionaryService } from './dictionary.service';

import { ExpertClassifierService } from './expert_classifier.service';
import { ExpertClassifier } from './mysql/expert_classifier.entity';

@Controller('dictionary')
export class DictionaryController {
  constructor(
    private readonly dictionaryService: DictionaryService,
    private readonly expertClassifierService: ExpertClassifierService,
  ) {}

  // 专家分类管理
  @Get('expert-classifier')
  findAll(): Promise<ExpertClassifier[]> {
    return wrapperResponse(
      this.expertClassifierService.findAll(),
      '查询专家分类列表',
    );
  }

  @Get('expert-classifier:id')
  findOne(@Param('id') id: number): Promise<ExpertClassifier> {
    return wrapperResponse(
      this.expertClassifierService.findOne(id),
      '查询专家分类详情',
    );
  }

  @Post('expert-classifier')
  create(@Body() createDto: ExpertClassifier): Promise<ExpertClassifier> {
    return wrapperResponse(
      this.expertClassifierService.create(createDto),
      '新增专家分类',
    );
  }

  @Put('expert-classifier/:id')
  update(
    @Param('id') id: number,
    @Body() updateDto: ExpertClassifier,
  ): Promise<ExpertClassifier> {
    return wrapperResponse(
      this.expertClassifierService.update(+id, updateDto),
      '更新专家分类',
    );
  }

  @Delete('expert-classifier/:id')
  remove(@Param('id') id: number): Promise<void> {
    return wrapperResponse(
      this.expertClassifierService.remove(id),
      '删除专家分类',
    );
  }

  @Get('area-code')
  getAreaCode(@Query() query: any): Promise<any> {
    return wrapperResponse(
      this.dictionaryService.getAreaCode(query.code),
      '获取区域编码',
    );
  }
}
