import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';
// 职业卫生大屏-用人单位开展三同时统计
@Entity({
  name: 'DP_ZYWSDP_YRDWKZSTSTJ',
  comment: '职业卫生大屏-用人单位开展三同时统计',
})
export class DP_ZYWSDP_YRDWKZSTSTJ {
  @PrimaryGeneratedColumn()
  ID: number;

  @Column({ type: 'varchar2', length: 255, comment: '类型' })
  LX: string;

  @Column({ type: 'varchar2', length: 255, comment: '用人单位数' })
  YRDWS: string;

  @Column({ type: 'varchar2', length: 255, comment: '全部开展数' })
  QBKZS: string;

  @Column({ type: 'varchar2', length: 255, comment: '部分开展数' })
  BFKZS: string;

  @Column({ type: 'varchar2', length: 255, comment: '未开展数' })
  WKZS: string;
}
