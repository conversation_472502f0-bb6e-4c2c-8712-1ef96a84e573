import {
  Column,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Declaration } from './declaration.entity';

@Entity('declaration_basic_condition')
export class DeclarationBasicCondition {
  @PrimaryGeneratedColumn({ name: 'id', type: 'int', comment: '自增ID' })
  id: number;

  @Column({ comment: '申报id', type: 'int' })
  declaration_id: number;

  @Column({ comment: '条件名称', type: 'varchar' })
  name: string;

  @Column({
    type: 'enum',
    enum: ['0', '1'],
    comment: '自评结果 0 不符合 1 符合',
  })
  self_assessment_result: string;

  @Column({
    type: 'enum',
    enum: ['0', '1'],
    comment: '师市级复核结果 0 不符合 1 符合',
  })
  city_assessment_result: string;

  @Column({
    type: 'enum',
    enum: ['0', '1'],
    comment: '兵团级复核结果 0 不符合 1 符合',
  })
  province_assessment_result: string;

  @ManyToOne(
    () => Declaration,
    (declaration) => declaration.declaration_basic_condition,
  )
  @JoinColumn({ name: 'declaration_id' })
  declaration: Declaration;
}
