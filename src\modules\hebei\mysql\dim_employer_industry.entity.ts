import { Entity, Column, PrimaryColumn } from 'typeorm';
// 使用组合主键来唯一标识每一行记录
// 企业行业分类
@Entity('dim_employer_industry')
export class DimEmployerIndustry {
  @PrimaryColumn({ type: 'varchar', length: 12, comment: 'id' })
  id: string;

  @Column({ type: 'varchar', length: 16, comment: '中卫信编码' })
  code: string;

  @Column({ type: 'varchar', length: 16, comment: '码表类型编码' })
  type: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '名称' })
  name: string;

  @Column({ type: 'varchar', length: 64, nullable: true, comment: '层级编码' })
  level: string;

  @Column({ type: 'varchar', length: 12, comment: '父级id' })
  parent_id: string;

  @Column({ type: 'tinyint', nullable: true, comment: '级别' })
  industry_level: number | null;
}
