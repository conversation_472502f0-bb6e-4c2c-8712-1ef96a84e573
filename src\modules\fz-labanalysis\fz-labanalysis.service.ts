import { Injectable, Logger } from '@nestjs/common';
import { <PERSON>ronJob } from 'cron';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, MongoRepository } from 'typeorm';
import { SchedulerRegistry } from '@nestjs/schedule';
import { ConfigService } from '@nestjs/config';
import { GlobalModule } from '../../global.module';
// Oracle
import { LIMS_ZYWS_MREGIST } from './oracle/LIMS_ZYWS_MREGIST.entity';
import { LIMS_ZYWS_SAMPLE } from './oracle/LIMS_ZYWS_SAMPLE.entity';
import { LIMS_ZYWS_SAMPCHECKITEM } from './oracle/LIMS_ZYWS_SAMPCHECKITEM.entity';
import { LIMS_ZYWS_CHKRSLTM } from './oracle/LIMS_ZYWS_CHKRSLTM.entity';
// Mongo
import { JcqlcProject } from './mongo/jcqlc-project.entity';
import { ServiceOrg } from './mongo/service-org.entity';
import { ServiceEmployee } from './mongo/service-employee.entity';
import { SamplePreparation } from './mongo/sample-preparation.entity';
import { temporaryFileRecord } from './mongo/temporary-file-record.entity';

import * as fs from 'fs';
import * as path from 'path';
import * as moment from 'moment';

type mongoRes = {
  WTSNO: string;
  ITEMNAME: string;
  ORGCODE: string;
  ORGNAME: string;
  SENDTIME: Date;
  ADDRESS: Array<any>;
  FLINKMAN: string;
  FLINKTEL: string;
  sample: Array<any>;
};
type saveRes = {
  errMsg?: string;
  success?: boolean;
  path?: string;
  staticName?: string;
};

@Injectable()
export class FzLabanalysisService {
  constructor(
    private readonly schedulerRegistry: SchedulerRegistry,
    private readonly configService: ConfigService,

    @InjectRepository(LIMS_ZYWS_MREGIST, 'labOracleConnection')
    private readonly LIMS_ZYWS_MREGIST_Repository: Repository<LIMS_ZYWS_MREGIST>,

    @InjectRepository(LIMS_ZYWS_SAMPLE, 'labOracleConnection')
    private readonly LIMS_ZYWS_SAMPLE_Repository: Repository<LIMS_ZYWS_SAMPLE>,

    @InjectRepository(LIMS_ZYWS_SAMPCHECKITEM, 'labOracleConnection')
    private readonly LIMS_ZYWS_SAMPCHECKITEM_Repository: Repository<LIMS_ZYWS_SAMPCHECKITEM>,

    @InjectRepository(LIMS_ZYWS_CHKRSLTM, 'labOracleConnection')
    private readonly LIMS_ZYWS_CHKRSLTM_Repository: Repository<LIMS_ZYWS_CHKRSLTM>,

    @InjectRepository(JcqlcProject, 'mongodbConnection')
    private jcqlcProjectRepository: MongoRepository<JcqlcProject>,

    @InjectRepository(ServiceOrg, 'mongodbConnection')
    private serviceOrgRepository: MongoRepository<ServiceOrg>,

    @InjectRepository(ServiceEmployee, 'mongodbConnection')
    private serviceEmployeeRepository: MongoRepository<ServiceEmployee>,

    @InjectRepository(SamplePreparation, 'mongodbConnection')
    private samplePreparationRepository: MongoRepository<SamplePreparation>,

    @InjectRepository(temporaryFileRecord, 'mongodbConnection')
    private temporaryFileRecordRepository: MongoRepository<temporaryFileRecord>,
  ) {}

  private readonly logger = new Logger(FzLabanalysisService.name);
  private readonly basePath = GlobalModule?.config['report_path'] || '';
  private readonly fzOrganization =
    GlobalModule?.config['fzOrganization'] || '123501004880998676';
  private readonly schema = (
    GlobalModule?.config['database_lab_oracle']?.database || 'SYSTEM'
  ).toUpperCase();

  /**
   * 模块初始化
   * @returns {void} 启动定时器，对项目进行定时同步
   */
  async onModuleInit() {
    // ------------ 启动项目自动遍历3个月内的更新并同步，正式项目启动后关闭 --------------
    this.logger.log(
      '===== 启动项目自动遍历数据库3个月内的更新并同步，调试完后关闭 =====',
    );
    await this.subscribeCopyDb(60 * 24 * 90); // 启动项目自动遍历3个月内的更新并同步，正式项目启动后关闭
    // ------------------------------------------------------------------------------

    // 动态创建和启动Cron定时作业
    const interval = this.configService.get('fzlabCornInterval');
    const job = new CronJob(interval, this.handleCron.bind(this));
    this.schedulerRegistry.addCronJob('fzlabTimedTask', job as any);
    job.start();
  }

  /**
   * 模块销毁
   * @returns {void} 停止和销毁Cron定时作业
   */
  async onModuleDestroy() {
    const job = this.schedulerRegistry.getCronJob('fzlabTimedTask');
    job.stop();
    this.schedulerRegistry.deleteCronJob('fzlabTimedTask');
  }

  /**
   * 定时任务
   * @returns {void}
   */
  async handleCron() {
    try {
      this.subscribeCopyDb();
    } catch (e) {
      this.logger.error(e);
    }
  }

  /**
   * 查询一定时间内 Oracle 表的变动数据并同步到 Mongo
   * @param {number} interval - 监听时间间隔
   * @returns {void}
   */
  async subscribeCopyDb(interval: number = 12): Promise<void> {
    try {
      const dateStart = new Date();
      const formattedStart = moment(dateStart).format('YYYY-MM-DD HH:mm:ss');
      this.logger.log('');
      this.logger.log(`---------- 开始同步数据 ${formattedStart} ----------`);
      // 监听 LIMS_ZYWS_SAMPCHECKITEM 表的修改变动，进行数据同步
      await this.syncSampcheckitemToMongo(interval);
      // 监听 LIMS_ZYWS_MREGIST 表的修改变动，进行数据同步 -- 暂时弃用
      // await this.syncMregistToMongo(interval);
      const dateEnd = new Date();
      const formattedDateEnd = moment(dateEnd).format('YYYY-MM-DD HH:mm:ss');
      this.logger.log(`---------- 同步数据结束 ${formattedDateEnd} ----------`);
    } catch (error) {
      this.logger.error(`同步异常：${error}`);
    }
  }

  /**
   * 实验室交样：同步 Mongo 数据到 Oracle
   * @param {string} projectSN - 项目编号
   * @param {number} batch - 批次
   * @returns {Object} 保存结果
   * @returns {boolean} returns.success - 操作是否成功
   * @returns {boolean} returns?.message - 操作信息
   * @returns {string} returns?.errMsg - 错误信息
   */
  async copyMongoToOracle(
    projectSN: string,
    batch: string,
  ): Promise<{ success: boolean; message?: string; errMsg?: string }> {
    try {
      this.logger.log(`-| 交样请求 [projectSN:${projectSN},batch:${batch}]`);

      // 在全流程已经保存好检测方案数据，查询方案单
      const mongoData = await this.findMongoData(projectSN, batch);
      // 存储 Mongo 更新参数，用以交样后更新实验室 lab 字段
      const bulkUpdateProject = [];

      // 遍历该批次检测方案交样数据
      for (const item of mongoData) {
        const {
          WTSNO,
          ORGCODE,
          ORGNAME,
          FLINKMAN,
          FLINKTEL,
          sample,
          ADDRESS,
          SENDTIME,
        } = item;

        const companyAddress =
          ADDRESS && ADDRESS.length > 0 && ADDRESS[0].address
            ? ADDRESS[0].address
            : '';
        const OPERTIME = new Date();
        const serviceEmployeeQuery = {};
        serviceEmployeeQuery['_id'] = {
          $in: [...sample.map((sampling) => sampling.samplingStaff)],
        };
        const serviceEmployee =
          await this.serviceEmployeeRepository.find(serviceEmployeeQuery);
        const DEPTMAN =
          serviceEmployee && serviceEmployee.length
            ? serviceEmployee.map((employee) => employee.name).join(',')
            : '';

        const dbRes = await this.updateOrInsert(
          { WTSNO, REGNO: WTSNO + '-' + batch },
          {
            WTSNO,
            REGNO: WTSNO + '-' + batch,
            ORGCODE,
            ORGNAME,
            SENDTIME,
            SAMPORIG: '现场采样',
            DEPTMAN, // 交样人
            OPERTIME,
            ADDRESS: companyAddress,
            FLINKMAN,
            FLINKTEL,
          },
          'LIMS_ZYWS_MREGIST_Repository',
        );
        const { REGGUID } = dbRes;
        for (const sampleItem of sample) {
          const {
            SAMPCLASSSEQNM,
            harmFactorsNameArray,
            SAMPNAME,
            SAMPNO,
            BEGINTIME, // 采样开始时间
            ENDTIME, // 采样结束时间
            TEMPER,
            pressure,
            HUMIDITY,
            // INSTFLUX,
            GETPOS,
            REMARK,
            SELFNO,
            sampleMode,
            samplingFlow,
            sampleArr,
          } = sampleItem;

          // 跳过 物理/直读 危害因素
          if (!SAMPNO || SAMPNO === '-') {
            continue; // 直读的危害因素编号为 -
          }

          const INSTFLUX = samplingFlow || ''; // 采样流量
          let SAMPVOL = '';
          // 计算采样体积 SAMPVOL
          if (sampleMode === '个体') {
            // 个体采样为分段采样
            if (sampleArr && sampleArr.length > 0) {
              let SAMPVOL_SUB = 0;
              for (const flowItem of sampleArr) {
                let flows = 0;
                let smaplingTime = 0;

                const {
                  smaplingTimeStart,
                  smaplingTimeEnd,
                  samplingFlowStart,
                  samplingFlowEnd,
                } = flowItem;
                samplingFlowEnd && samplingFlowStart
                  ? (flows =
                      (Number(
                        samplingFlowEnd.toString().replace(/[^0-9.]/gi, ''),
                      ) +
                        Number(
                          samplingFlowStart.toString().replace(/[^0-9.]/gi, ''),
                        )) /
                      2)
                  : null;
                smaplingTime = moment(smaplingTimeEnd).diff(
                  moment(smaplingTimeStart),
                  'm',
                  false,
                );
                SAMPVOL_SUB += flows * Math.ceil(smaplingTime);
              }
              SAMPVOL = String(SAMPVOL_SUB.toFixed(2));
            }
          } else {
            const smaplingTime = moment(ENDTIME).diff(
              moment(BEGINTIME),
              'm',
              false,
            );
            samplingFlow
              ? (SAMPVOL = String(
                  (
                    Number(samplingFlow.toString().replace(/[^0-9.]/gi, '')) *
                    Math.ceil(smaplingTime)
                  ).toFixed(2),
                ))
              : (SAMPVOL = '');
          }
          // 当检测现场的温度不满足5-35℃或大气压不在98.9-103.4kPa时需要进行换算
          if (
            SAMPVOL &&
            TEMPER &&
            pressure &&
            (Number(TEMPER) <= 5 ||
              Number(TEMPER) >= 35 ||
              Number(pressure.replace(/[^0-9.]/gi, '')) <= 98.9 ||
              Number(pressure.replace(/[^0-9.]/gi, '')) >= 103.4)
          ) {
            SAMPVOL = (
              (((Number(SAMPVOL) * 293) / (273 + Number(TEMPER))) *
                Number(pressure.replace(/[^0-9.]/gi, ''))) /
              101.3
            ).toFixed(2);
          }

          if (isNaN(Number(SAMPVOL))) {
            SAMPVOL = '';
          }

          await this.updateOrInsert(
            { SAMPNO, REGGUID },
            {
              SAMPNO,
              REGGUID,
              SAMPCLASSSEQNM,
              SAMPNAME,
              REMARK,
              SELFNO,
              BEGINTIME,
              ENDTIME,
              SAMPCOUNNO: WTSNO,
              TEMPER,
              HUMIDITY,
              GETPOS,
              INSTFLUX,
              SAMPVOL,
            },
            'LIMS_ZYWS_SAMPLE_Repository',
          );

          const sampcheckitemData = harmFactorsNameArray.map((labItem) => {
            return {
              SAMPNO,
              ITEMNAME: labItem.name,
              ITEMNO: labItem.harmFactorId,
              REMARK: labItem.sampleListNote,
            };
          });

          // INFO: 和全流程不同，此处先创建 lab,以便后续的修改
          const labComment = [];
          for (const lab of sampcheckitemData) {
            const { ITEMNAME } = lab;
            labComment.push({
              harmFactor: ITEMNAME,
              comment: '',
            });
            await this.updateOrInsert(
              { SAMPNO, ITEMNAME },
              {
                SAMPNO,
                ITEMNAME,
                ITEMNO: lab.ITEMNO,
              },
              'LIMS_ZYWS_SAMPCHECKITEM_Repository',
            );
          }

          bulkUpdateProject.push({
            updateMany: {
              filter: { projectSN },
              update: {
                $set: {
                  'station.$[].harmFactors.$[].sample.$[sampleElem].lab':
                    labComment,
                },
              },
              arrayFilters: [{ 'sampleElem.sampleSN': SAMPNO }],
            },
          });
        }
      }
      if (!bulkUpdateProject.length) {
        this.logger.log(`-| 交样失败 无可更新内容`);
        return {
          success: false,
          message: '无可更新内容',
        };
      }
      await this.jcqlcProjectRepository.bulkWrite([...bulkUpdateProject]);
      this.logger.log(`-| 交样成功 [projectSN:${projectSN},batch:${batch}]`);
      return {
        success: true,
        message: '交样成功',
      };
    } catch (error) {
      this.logger.error(error);
      return {
        success: false,
        message: '交样失败',
      };
    }
  }

  /**
   * 同步 LIMS_ZYWS_SAMPCHECKITEM 表
   * @param {number} interval - 监听间隔
   * @returns {void} 同步
   */
  async syncSampcheckitemToMongo(interval: number = 15): Promise<void> {
    try {
      // 获取福州机构 id
      const serviceOrgQuery = {};
      serviceOrgQuery['organization'] = this.fzOrganization;
      const serviceOrg =
        await this.serviceOrgRepository.findOne(serviceOrgQuery);
      const serviceOrgId = serviceOrg ? serviceOrg._id : null;
      if (!serviceOrgId) {
        this.logger.error('福州机构不存在');
        return;
      }
      // 查询 LIMS_ZYWS_SAMPCHECKITEM 表在 {interval} 分钟内修改的数据
      const sampcheckitemQuery = `SELECT ITEMNO,SAMPNO,ITEMNAME,CHECKRESULT,RESULTSEQ FROM ${this.schema}.LIMS_ZYWS_SAMPCHECKITEM
      WHERE TO_CHAR(OPERTIME, 'YYYY-MM-DD HH24:MI:SS') >= TO_CHAR(SYSDATE - INTERVAL '${interval}' MINUTE, 'YYYY-MM-DD HH24:MI:SS')`;

      const sampcheckitemRes =
        await this.LIMS_ZYWS_SAMPCHECKITEM_Repository.query(sampcheckitemQuery);
      const sampcheckitemObj = {};

      this.logger.log(
        `-| 同步 LIMS_ZYWS_SAMPCHECKITEM 表数据：[Total Length:${sampcheckitemRes.length}]`,
      );

      for (const item of sampcheckitemRes) {
        // 获取项目编号的模糊查询(主项目)
        // const match = item.SAMPNO.match(/^([^-]+)-/);
        // const contentBeforeHyphen = match[1];
        // const projectSNprefix =
        //   contentBeforeHyphen.match(/[a-zA-Z]/g).join('') +
        //   '-' +
        //   contentBeforeHyphen.match(/\d/g).join('');
        const projectSNprefix = this.splitString(item.SAMPNO);
        // 去除只有数字或字母的可能匹配错误的项
        if (projectSNprefix) {
          // console.log('========= projectSNprefix =========\n', projectSNprefix);
          if (sampcheckitemObj[projectSNprefix]) {
            sampcheckitemObj[projectSNprefix].lab.push({
              ...item,
              projectSNprefix,
            });
          } else {
            const sampleSNFix =
              item.SAMPNO.split('-').length == 2
                ? item.SAMPNO.split('-')[0] + '-'
                : item.SAMPNO.split('-')[0] + item.SAMPNO.split('-')[1] + '-';
            // 获取已有结果的样品总数 TODO: 可能会匹配到子项目的内容，需要加 not like
            const LaballSampleNumber =
              await this.LIMS_ZYWS_SAMPCHECKITEM_Repository.createQueryBuilder(
                'SAMPCHECKITEM',
              )
                .where('SAMPCHECKITEM.SAMPNO LIKE :likeStr', {
                  likeStr: `%${sampleSNFix}%`,
                })
                .getCount();
            // .andWhere(
            //   `NOT REGEXP_LIKE(SAMPCHECKITEM.SAMPNO, '${projectSNprefix}-[A-Z]')`,
            // ) // ？？

            const allSampleAnalyseNumber =
              await this.LIMS_ZYWS_SAMPCHECKITEM_Repository.createQueryBuilder(
                'SAMPCHECKITEM',
              )
                .where('SAMPCHECKITEM.SAMPNO LIKE :likeStr', {
                  likeStr: `%${sampleSNFix}%`,
                })
                .andWhere('SAMPCHECKITEM.CHECKRESULT IS NOT NULL')
                .getCount();

            sampcheckitemObj[projectSNprefix] = {
              lab: [{ ...item, projectSNprefix }],
              allSampleAnalyseNumber,
              LaballSampleNumber,
            };
          }
        } else {
          // 编号有误，同步失败
          this.logger.error(
            `LIMS_ZYWS_SAMPCHECKITEM 表编号有误，同步失败 [{SAMPNO:${item.SAMPNO},ITEMNO:${item.ITEMNO}}]`,
          );
        }
      }
      const bulkUpdateProject = [];
      const bulkUpdatePreparation = [];
      const bulkUpdateTemporaryFile = [];

      // const updateinstrument: any = {};
      for (const projectKey of Object.keys(sampcheckitemObj)) {
        const { lab, allSampleAnalyseNumber, LaballSampleNumber } =
          sampcheckitemObj[projectKey];
        const matchObj: any = {
          projectSN: { $regex: projectKey },
        };
        if (projectKey.split('-').length === 2) {
          // 主项目
          matchObj.parentId = '0';
        }
        const jcqlcProjectData = await this.jcqlcProjectRepository
          .aggregate([
            { $match: matchObj },
            {
              $project: {
                allSampleNumber: 1,
                projectSN: 1,
                _id: 1,
              },
            },
          ])
          .toArray();

        // 未查到相关项目
        if (!jcqlcProjectData || !jcqlcProjectData.length) {
          this.logger.error(`projectSN:${projectKey} 项目不存在`);
          continue;
        }

        const {
          allSampleNumber,
          projectSN,
          _id: projectObjectId,
        } = jcqlcProjectData[0];
        const projectId = projectObjectId.toString();
        const RESULTSEQ_LIST = []; // 记录已经同步过的 RESULTSEQ

        // updateinstrument[projectId] = {};

        let temporaryFile;
        temporaryFile = await this.temporaryFileRecordRepository
          .aggregate([
            { $match: { projectId } },
            {
              $project: { temporaryFile: 1 },
            },
          ])
          .toArray();
        if (!temporaryFile || !temporaryFile.length) {
          const saveTemporaryFile =
            await this.temporaryFileRecordRepository.create({
              projectSN,
              projectId,
            });

          await this.temporaryFileRecordRepository.save(saveTemporaryFile);
          temporaryFile = [];
        }
        const temporaryFileList = [];
        const year = await this.getProjectYear(projectSN);
        if (!year) {
          this.logger.error(`projectSN:${projectSN} 获取项目年份有误`);
          continue;
        }
        // 同步实验检测结果
        for (const labItem of lab) {
          const { SAMPNO, ITEMNAME, CHECKRESULT, RESULTSEQ } = labItem;
          // 游离二氧化硅的空气中浓度字段不同
          if (ITEMNAME === '游离二氧化硅') {
            bulkUpdateProject.push({
              updateMany: {
                filter: { projectSN },
                update: {
                  $set: {
                    'station.$[].harmFactors.$[].sample.$[sampleElem].lab.$[harmName].SiO2_concentration':
                      CHECKRESULT,
                  },
                },
                arrayFilters: [
                  { 'sampleElem.sampleSN': SAMPNO },
                  { 'harmName.harmFactor': ITEMNAME },
                ],
              },
            });
          } else {
            bulkUpdateProject.push({
              updateMany: {
                filter: { projectSN },
                update: {
                  $set: {
                    'station.$[].harmFactors.$[].sample.$[sampleElem].lab.$[harmName].air_concentration':
                      CHECKRESULT,
                  },
                },
                arrayFilters: [
                  { 'sampleElem.sampleSN': SAMPNO },
                  { 'harmName.harmFactor': ITEMNAME },
                ],
              },
            });
          }
          if (!RESULTSEQ || RESULTSEQ_LIST.includes(RESULTSEQ)) {
            continue;
          }
          RESULTSEQ_LIST.push(RESULTSEQ);

          // 同步实验室原始记录单
          // this.logger.log(`  |=> 同步项目${projectSN} ${SAMPNO}原始记录单`);

          const fileBufferQuery = `SELECT FCONT,FINSTLXS,FINSTNMS,FINSTNOS,FINSTXHS FROM ${this.schema}.LIMS_ZYWS_CHKRSLTM
          WHERE RESULTSEQ=:resultSeq`;

          const fileBufferString =
            await this.LIMS_ZYWS_CHKRSLTM_Repository.query(fileBufferQuery, [
              Number(RESULTSEQ),
            ]);
          if (!fileBufferString || !fileBufferString.length) {
            continue;
          }
          const { FCONT, FINSTLXS, FINSTNMS, FINSTNOS, FINSTXHS } =
            fileBufferString[0];

          // TODO: 不知道这个表的更新时机，如果不是同步更新操作时间则需要再写一个额外针对这个表的查询
          if (!FCONT) {
            continue;
          }
          // 获取旧文件信息
          const temporaryFileData = temporaryFile.length
            ? JSON.parse(JSON.stringify(temporaryFile))
            : [];

          const chemistryWordList = temporaryFileData.length
            ? temporaryFileData[0].temporaryFile
            : null;

          const chemistryWordFileName =
            chemistryWordList && chemistryWordList.length
              ? chemistryWordList.find((item) => item.resultseq === RESULTSEQ)
              : null;

          const oldChemistryWordFileUrl =
            chemistryWordFileName && chemistryWordFileName.url
              ? chemistryWordFileName.url
              : '';

          const saveRepfile = await this.saveBinaryDataToFile(
            FCONT,
            serviceOrgId,
            year,
            projectSN,
            '原始记录单' + RESULTSEQ,
            'pdf',
            oldChemistryWordFileUrl,
          );

          // if (updateinstrument[projectId][SAMPNO]) {
          //   updateinstrument[projectId][SAMPNO].push({
          //     instrumentID: FINSTLXS || '',
          //     instrumentName: FINSTNMS || '',
          //     internalNumber: FINSTNOS || '',
          //     instrumentModel: FINSTXHS || '',
          //   });
          // } else {
          //   updateinstrument[projectId][SAMPNO] = [
          //     {
          //       instrumentID: FINSTLXS || '',
          //       instrumentName: FINSTNMS || '',
          //       internalNumber: FINSTNOS || '',
          //       instrumentModel: FINSTXHS || '',
          //     },
          //   ];
          // }
          const newWordFileName = {
            resultseq: RESULTSEQ,
            sampleSN: SAMPNO,
            url: '',
            name: '',
            FINSTLXS: FINSTLXS || '',
            FINSTNMS: FINSTNMS || '',
            FINSTNOS: FINSTNOS || '',
            FINSTXHS: FINSTXHS || '',
          };
          if (saveRepfile.errMsg) {
            this.logger.error(
              `${projectSN}项目文件保存失败：${saveRepfile.errMsg}`,
            );
            continue;
          } else {
            newWordFileName.url = saveRepfile.path;
            newWordFileName.name = saveRepfile.staticName;
            this.logger.log(`---------|| 文件保存成功：${saveRepfile.path}`);
          }

          if (!newWordFileName.url || !newWordFileName.name) {
            continue;
          }
          temporaryFileList.push(newWordFileName);

          // 插入
        }
        temporaryFileList.length &&
          bulkUpdateTemporaryFile.push({
            updateMany: {
              filter: { projectId },
              update: {
                $set: {
                  temporaryFile: temporaryFileList,
                },
              },
            },
          });
        if (
          !(Number(allSampleNumber) > allSampleAnalyseNumber) &&
          +allSampleNumber > 0
        ) {
          bulkUpdateProject.push({
            updateMany: {
              filter: { projectSN },
              update: {
                $set: {
                  'progress.labSampleList': {
                    status: 2,
                    completedTime: new Date(),
                  },
                  'progress.labRecord': {
                    status: 2,
                    completedTime: new Date(),
                  },
                  'progress.labTestReport': {
                    status: 2,
                    completedTime: new Date(),
                  },
                  'progress.reportApproved': {
                    status: 2,
                    completedTime: new Date(),
                  },
                  allSampleAnalyseNumber,
                  LaballSampleNumber,
                  // labRecordFile,
                  // chemistryWordFileName,
                },
              },
            },
          });
          bulkUpdatePreparation.push({
            updateMany: {
              filter: { projectSN, 'FormData.projectNumbers': projectSN },
              update: {
                $set: {
                  'FormData.$[e].status': true,
                  status: 2,
                },
              },
              arrayFilters: [{ 'e.projectNumbers': projectSN }],
            },
          });
        } else {
          bulkUpdateProject.push({
            updateMany: {
              filter: { projectSN },
              update: {
                $set: {
                  allSampleAnalyseNumber,
                  LaballSampleNumber,
                  // labRecordFile,
                  // chemistryWordFileName,
                },
              },
            },
          });
        }
      }

      // const updatepProjectIds = Object.keys(updateinstrument);
      // if (updateinstrument && updatepProjectIds.length) {
      //   for (const instrProjectId of updatepProjectIds) {
      //     for (const instrSampleSN of Object.keys(
      //       updateinstrument[instrProjectId],
      //     )) {
      //       bulkUpdateProject.push({
      //         updateMany: {
      //           filter: { _id: instrProjectId },
      //           update: {
      //             $set: {
      //               'station.$[].harmFactors.$[].sample.$[el].instrument':
      //                 updateinstrument[instrProjectId][instrSampleSN],
      //             },
      //           },
      //           arrayFilters: [{ 'el.sampleSN': instrSampleSN }],
      //         },
      //       });
      //     }
      //   }
      // }
      if (bulkUpdateTemporaryFile.length) {
        await this.temporaryFileRecordRepository.bulkWrite([
          ...bulkUpdateTemporaryFile,
        ]);
      }
      if (bulkUpdateProject.length) {
        await this.jcqlcProjectRepository.bulkWrite([...bulkUpdateProject]);
      }
      if (bulkUpdatePreparation.length) {
        await this.samplePreparationRepository.bulkWrite([
          ...bulkUpdatePreparation,
        ]);
      }

      this.logger.log(`-| 完成同步 LIMS_ZYWS_SAMPCHECKITEM 表`);
      this.logger.log(``);
    } catch (error) {
      this.logger.error(`-| ${error}`);
      console.error(error);
    }
  }

  /**
   * 同步 LIMS_ZYWS_MREGIST 表  -- 暂时弃用，原始记录单已经修改为不由这里来
   * @param {number} interval 时间间隔
   * @returns {void} 同步
   */
  async syncMregistToMongo(interval: number = 15): Promise<void> {
    // 获取福州机构 id
    const serviceOrgQuery = {};
    serviceOrgQuery['organization'] = this.fzOrganization;
    const serviceOrg = await this.serviceOrgRepository.findOne(serviceOrgQuery);
    const serviceOrgId = serviceOrg ? serviceOrg._id : null;
    if (!serviceOrgId) {
      this.logger.error('福州机构不存在');
      return;
    }
    // 查询最近修改的记录
    const mregistQuery = `SELECT REGGUID FROM ${this.schema}.LIMS_ZYWS_MREGIST
      WHERE TO_CHAR(OPERTIME, 'YYYY-MM-DD HH24:MI:SS') >= TO_CHAR(SYSDATE - INTERVAL '${interval}' MINUTE, 'YYYY-MM-DD HH24:MI:SS') AND ORIGNAL IS NOT NULL`;

    const mregistData =
      await this.LIMS_ZYWS_MREGIST_Repository.query(mregistQuery);

    this.logger.log(
      `-| 同步 LIMS_ZYWS_MREGIST 表数据：[Total Length:${mregistData.length}]`,
    );
    // for (const mregist of mregistData) {
    //   const projectData = await this.LIMS_ZYWS_MREGIST_Repository.findOne({
    //     select: ['WTSNO', 'ORIGNAL'],
    //     where: {
    //       REGGUID: mregist.REGGUID,
    //     },
    //   });
    //   const projectSN = projectData.WTSNO;
    //   const ORIGNAL = projectData.ORIGNAL;
    //   const jcqlcProjectData = await this.jcqlcProjectRepository
    //     .aggregate([
    //       { $match: { projectSN } },
    //       {
    //         $project: { chemistryWordFileName: 1 },
    //       },
    //     ])
    //     .toArray();
    //   if (!jcqlcProjectData || !jcqlcProjectData.length) {
    //     this.logger.error(`${projectSN}项目不存在`);
    //     continue;
    //   }
    //   const chemistryWordFileName = jcqlcProjectData[0].chemistryWordFileName;

    //   const year = await this.getProjectYear(projectSN);
    //   const oldChemistryWordFileUrl = chemistryWordFileName?.url || '';
    //   // 约定好存储的文件类型： pdf
    //   const saveRepfile = await this.saveBinaryDataToFile(
    //     ORIGNAL,
    //     serviceOrgId,
    //     year,
    //     projectSN,
    //     '检测报告',
    //     'pdf',
    //     oldChemistryWordFileUrl,
    //   );

    //   let newWordFileName = {};
    //   if (saveRepfile.errMsg) {
    //     this.logger.error(
    //       `${projectSN}项目文件保存失败：${saveRepfile.errMsg}`,
    //     );
    //   } else {
    //     newWordFileName = {
    //       url: saveRepfile.path,
    //       name: saveRepfile.staticName,
    //     };
    //   }
    //   await this.jcqlcProjectRepository.bulkWrite([
    //     {
    //       updateMany: {
    //         filter: { projectSN },
    //         update: {
    //           $set: {
    //             // labRecordFile,
    //             chemistryWordFileName: newWordFileName,
    //           },
    //         },
    //       },
    //     },
    //   ]);
    // }
    this.logger.log(`-| 完成同步 LIMS_ZYWS_MREGIST 表`);
  }
  /**
   * 查询项目信息
   * @param {string} projectSN - 项目编号
   * @param {string} batch - 批次
   * @returns {Array} 查询项目信息
   */
  async findMongoData(projectSN: string, batch: string): Promise<mongoRes[]> {
    // mongo 查询
    const res = await this.jcqlcProjectRepository
      .aggregate([
        { $match: { projectSN } },
        // 展开station数组
        { $unwind: '$station' },
        { $unwind: '$station.harmFactors' },
        // 重新构造文档
        {
          $project: {
            WTSNO: '$projectSN',
            ITEMNAME: '$projectName',
            ORGCODE: '$companyID',
            ORGNAME: '$EnterpriseName',
            ADDRESS: '$companyAddress',
            FLINKMAN: '$companyContact',
            FLINKTEL: '$companyContactPhoneNumber',
            SAMPCLASSSEQNM: '$station.harmFactors.category',
            SAMPNAME: '$station.harmFactors.harmFactorsName',
            harmFactorsNameArray: '$station.harmFactors.harmFactorsNameArray',
            AMOUNT: '$station.harmFactors.sampleNumber',
            sample: '$station.harmFactors.sample',
            sampleMode: '$station.harmFactors.sampleMode', // 采样方式
          },
        },
        { $unwind: '$sample' },
        {
          $match: {
            'sample.batch': Number(batch), // 当前交样批次
            'sample.isCompleteConfirm': true, // 交样状态
          },
        },
        {
          $group: {
            _id: '$_id',
            WTSNO: { $first: '$WTSNO' },
            ITEMNAME: { $first: '$ITEMNAME' },
            ORGCODE: { $first: '$ORGCODE' },
            ORGNAME: { $first: '$ORGNAME' },
            ADDRESS: { $first: '$ADDRESS' },
            FLINKMAN: { $first: '$FLINKMAN' },
            FLINKTEL: { $first: '$FLINKTEL' },
            SENDTIME: { $max: '$sample.smaplingTimeEnd' },
            sampleData: {
              $push: {
                sampleMode: '$sampleMode',
                samplingStaff: '$sample.samplingStaff',
                SAMPCLASSSEQNM: '$SAMPCLASSSEQNM',
                SAMPNAME: '$SAMPNAME',
                AMOUNT: '$AMOUNT',
                harmFactorsNameArray: '$harmFactorsNameArray',
                GETPOS: '$sample.samplingPlace',
                SAMPNO: '$sample.sampleSN',
                BEGINTIME: '$sample.smaplingTimeStart', // 采样开始时间
                ENDTIME: '$sample.smaplingTimeEnd', // 采样结束时间
                TEMPER: '$sample.temperature',
                pressure: '$sample.pressure',
                HUMIDITY: '$sample.HUMIDITY',
                INSTFLUX: '$sample.samplingFlow',
                REMARK: '$sample.remarks', //备注
                SELFNO: '$sample.selfNumber', //自编号
                samplingFlow: '$sample.samplingFlow', // 定点采样流量
                // smaplingTimeStart: '$sample.smaplingTimeStart', // 采样开始时间
                // smaplingTimeEnd: '$sample.smaplingTimeEnd', // 采样结束时间
                sampleArr: '$sample.samplingArr', // 个体采样流量-分段
              },
            },
          },
        },
        // 重新构造结果
        {
          $project: {
            _id: 0,
            WTSNO: 1,
            ITEMNAME: 1,
            SENDTIME: 1,
            ORGCODE: 1,
            ORGNAME: 1,
            ADDRESS: 1,
            FLINKMAN: 1,
            FLINKTEL: 1,
            sample: '$sampleData',
          },
        },
      ])
      .toArray();
    const mongoResults: mongoRes[] = res as unknown as mongoRes[]; // TS类型校验
    return mongoResults;
  }

  /**
   * 保存二进制数据到文件
   * @param {Buffer} binaryData - 二进制数据
   * @param {string} serviceOrgId - 服务机构ID
   * @param {string} year - 年份
   * @param {string} projectSN - 项目编号
   * @param {string} name - 文件名称
   * @param {string} fileType - 文件类型
   * @param {string} oldPath - 旧文件路径
   * @returns {Promise<saveRes>} 保存结果
   */
  async saveBinaryDataToFile(
    hexString,
    serviceOrgId,
    year,
    projectSN,
    name,
    fileType = 'pdf',
    oldPath = '',
  ): Promise<saveRes> {
    try {
      const filePath = path.normalize(
        `${this.basePath}/${serviceOrgId}/${year}/${projectSN}/lab`,
      ); // report 后接机构编号
      const oldFilePath = oldPath
        ? path.normalize(`${this.basePath}/${oldPath}`)
        : '';
      if (!fs.existsSync(filePath)) {
        fs.mkdirSync(filePath, { recursive: true });
      }
      if (oldFilePath && fs.existsSync(oldFilePath)) {
        fs.unlinkSync(oldFilePath);
      }

      const timestamp = new Date().getTime();
      const fileName = `${projectSN}${name}_${timestamp}.${fileType}`;

      // 注意：Blob可能是一个Buffer或一个Stream
      this.logger.log(`-| PDF文件正在保存`);
      fs.writeFileSync(path.join(filePath, fileName), hexString);
      // if (hexString instanceof Buffer) {
      //   fs.writeFileSync(path.join(filePath, fileName), hexString);
      //   // this.logger.log(`-| PDF文件已成功保存`);
      // } else {
      //   // 如果Blob不是Buffer，则需要额外的处理，但 oracledb 通常返回 Buffer
      //   this.logger.log(`-| 该文件类型有误`);
      // }
      // const buffer = Buffer.from(hexString);
      // // 将二进制数据写入文件
      // fs.writeFileSync(path.join(filePath, fileName), buffer);
      return {
        success: true,
        path: `${serviceOrgId}/${year}/${projectSN}/lab/${fileName}`,
        staticName: fileName,
      };
      // oss
    } catch (error) {
      return { errMsg: '文件同步失败' + error };
    }
  }

  /**
   * 根据输入的字符串，按照特定的拆分规则进行拆分并返回结果。
   * @param {string} input - 需要处理的输入字符串。
   * @returns {string} 根据规则拆分后的字符串。
   *
   * 拆分规则：
   * 1. 如果输入字符串符合XX232149-XY123-107004或XX232149-XY123-#107004的格式，则返回XX-232149-XY123。（子项目）
   * 2. 如果输入字符串符合XX232149-107004或XX232149-#107004的格式，则返回XX-232149。（主项目）
   */
  splitString(input: string): string {
    // 使用正则表达式匹配不同的情况
    const regex1 = /^([A-Z]{2}\d+)-([A-Z]+\d+)-(#?\d+)$/;
    const regex2 = /^([A-Z]{2}\d+)-(#?\d+)$/;

    if (regex1.test(input)) {
      const matches = input.match(regex1);
      return `${matches[1].slice(0, 2)}-${matches[1].slice(2)}-${matches[2]}`;
    } else if (regex2.test(input)) {
      const matches = input.match(regex2);
      return `${matches[1].slice(0, 2)}-${matches[1].slice(2)}`;
    } else {
      // 如果输入不符合任何一个正则表达式的情况，返回原始输入
      return '';
    }
  }
  /**
   * 获取项目年份
   * @param {string} projectSN - 项目编号
   * @returns {string} - 项目年份
   */
  async getProjectYear(projectSN: string): Promise<string> {
    try {
      // 获取项目创建日期，如果是子项目那么获取主项目的创建日期
      if (!projectSN) {
        // return '2024';
        throw new Error('该项目有误');
      }
      const projectSN_array = projectSN.split('-');
      let creatProject = projectSN;
      if (projectSN_array.length === 3) {
        creatProject = projectSN_array[0] + '-' + projectSN_array[1];
      }
      const parentProject = await this.jcqlcProjectRepository
        .aggregate([
          { $match: { projectSN: creatProject } },
          {
            $project: {
              progress: 1,
            },
          },
        ])
        .toArray();

      const parentProjectObj =
        parentProject && parentProject.length
          ? JSON.parse(JSON.stringify(parentProject))
          : [];
      this.logger.log(`-| 获取项目年份中`);
      const createProject_time = parentProjectObj.length
        ? parentProjectObj[0].progress?.createProject_time?.completedTime
        : '';

      if (createProject_time) {
        this.logger.log(`-| 获取项目年份：${createProject_time}`);
        return new Date(createProject_time).getFullYear() + '';
      } else {
        // throw new Error('该项目创建时间有误');
        return '';
      }
    } catch (error) {
      console.log(error); // ?
      this.logger.error(`-| 获取项目年份失败 :${error}`); // ?
      return ''; // TODO: 后续需要修改此返回
    }
  }

  /**
   * 修改或新增 Oracle 数据库
   * @param {object} where - where条件
   * @param {object} data - 数据
   * @param {object} dbRepositoryKey - 数据库实体
   * @returns {object} 更新结果
   */
  async updateOrInsert(where, data, dbRepositoryKey): Promise<any> {
    const dbRepository = this[dbRepositoryKey];
    const existingRecord = await dbRepository.findOne({ where });
    if (existingRecord) {
      // 如果存在，则更新记录
      const parmas = { ...existingRecord, ...data };
      return await dbRepository.save(parmas);
    } else {
      // 如果不存在，则新增记录
      const newRecord = dbRepository.create(data); // 创建新实体
      return await dbRepository.save(newRecord);
    }
  }
}
