import { Module } from '@nestjs/common';
import { HealthCheckAppointmentController } from './health-check-appointment.controller';
import { HealthCheckAppointmentService } from './health-check-appointment.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HealthCheckAppointment } from './health-check-appointment.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([HealthCheckAppointment], 'mongodbConnection'),
  ],
  controllers: [HealthCheckAppointmentController],
  providers: [HealthCheckAppointmentService],
  exports: [HealthCheckAppointmentService],
})
export class HealthCheckAppointmentModule {}
