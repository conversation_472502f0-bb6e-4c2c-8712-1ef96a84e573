CREATE TABLE todo (
  `id` INT PRIMARY KEY AUTO_INCREMENT, -- 待办事项ID
  `type_code` VARCHAR(100) NOT NULL,  -- 分类编码
  `title` VARCHAR(255) NOT NULL, -- 待办事项标题
  `description` text, -- 待办事项描述
  `status` tinyint(4) DEFAULT NULL COMMENT '状态：0-未读，1-已读',
  `user_id` varchar(255) NOT NULL, -- 负责人ID
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)