import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CronJob } from 'cron';
import { SchedulerRegistry } from '@nestjs/schedule';
import { ConfigService } from '@nestjs/config';
import { DpStatisticsService } from '../dp-statistics/dp-statistics.service';
import { DP_ZYWSDP_GDQZYWSFGTJ_LB } from './oracle/DP_ZYWSDP_GDQZYWSFGTJ_LB.entity';
import { DP_ZYWSDP_JCYRDWSLTJ } from './oracle/DP_ZYWSDP_JCYRDWSLTJ.entity';
import { DP_ZYWSDP_ZYWSKSGZL_LB } from './oracle/DP_ZYWSDP_ZYWSKSGZL_LB.entity';
import { DP_ZYWSDP_ZYJKJCTJ } from './oracle/DP_ZYWSDP_ZYJKJCTJ.entity';
import { DP_ZYWSDP_ZDWHYSJCTJ } from './oracle/DP_ZYWSDP_ZDWHYSJCTJ.entity';
import { DP_ZYWSDP_JCYRDWZYJKPXTJ } from './oracle/DP_ZYWSDP_JCYRDWZYJKPXTJ.entity';
import { DP_ZYWSDP_YRDWKZSTSTJ } from './oracle/DP_ZYWSDP_YRDWKZSTSTJ.entity';
import { DP_ZYWSDP_LDZZYBWHYSJCTJ } from './oracle/DP_ZYWSDP_LDZZYBWHYSJCTJ.entity';
// import { DP_ZYWSDP_JCYRDWFB } from './oracle/DP_ZYWSDP_JCYRDWFB.entity';
// import { DP_ZYWSDP_ZYWHYSFHTJ } from './oracle/DP_ZYWSDP_ZYWHYSFHTJ.entity';
// import { DP_X_ZYWSDP_ZYWSJCTJ } from './oracle/DP_X_ZYWSDP_ZYWSJCTJ.entity';
import { DP_ZYWSDP_YRDWZYWHYSSBTJ } from './oracle/DP_ZYWSDP_YRDWZYWHYSSBTJ.entity';
import { DP_ZYWSDP_JCYRDWHYTJ } from './oracle/DP_ZYWSDP_JCYRDWHYTJ.entity';
import { DP_ZYWSDP_JCYRDWGMTJ } from './oracle/DP_ZYWSDP_JCYRDWGMTJ.entity';

@Injectable()
export class FzDpService {
  constructor(
    private schedulerRegistry: SchedulerRegistry,
    private configService: ConfigService,
    @InjectRepository(DP_ZYWSDP_GDQZYWSFGTJ_LB, 'oracleConnection')
    private DP_ZYWSDP_GDQZYWSFGTJ_LB_Repository: Repository<DP_ZYWSDP_GDQZYWSFGTJ_LB>,
    private dpStatisticsService: DpStatisticsService,
    @InjectRepository(DP_ZYWSDP_JCYRDWSLTJ, 'oracleConnection')
    private DP_ZYWSDP_JCYRDWSLTJ_Repository: Repository<DP_ZYWSDP_JCYRDWSLTJ>,
    @InjectRepository(DP_ZYWSDP_ZYWSKSGZL_LB, 'oracleConnection')
    private DP_ZYWSDP_ZYWSKSGZL_LB_Repository: Repository<DP_ZYWSDP_ZYWSKSGZL_LB>,
    @InjectRepository(DP_ZYWSDP_ZYJKJCTJ, 'oracleConnection')
    private DP_ZYWSDP_ZYJKJCTJ_Repository: Repository<DP_ZYWSDP_ZYJKJCTJ>,
    @InjectRepository(DP_ZYWSDP_ZDWHYSJCTJ, 'oracleConnection')
    private DP_ZYWSDP_ZDWHYSJCTJ_Repository: Repository<DP_ZYWSDP_ZDWHYSJCTJ>,
    @InjectRepository(DP_ZYWSDP_JCYRDWZYJKPXTJ, 'oracleConnection')
    private DP_ZYWSDP_JCYRDWZYJKPXTJ_Repository: Repository<DP_ZYWSDP_JCYRDWZYJKPXTJ>,
    @InjectRepository(DP_ZYWSDP_YRDWKZSTSTJ, 'oracleConnection')
    private DP_ZYWSDP_YRDWKZSTSTJ_Repository: Repository<DP_ZYWSDP_YRDWKZSTSTJ>,
    @InjectRepository(DP_ZYWSDP_LDZZYBWHYSJCTJ, 'oracleConnection')
    private DP_ZYWSDP_LDZZYBWHYSJCTJ_Repository: Repository<DP_ZYWSDP_LDZZYBWHYSJCTJ>,
    // @InjectRepository(DP_ZYWSDP_JCYRDWFB, 'oracleConnection')
    // private DP_ZYWSDP_JCYRDWFB_Repository: Repository<DP_ZYWSDP_JCYRDWFB>,
    // @InjectRepository(DP_ZYWSDP_ZYWHYSFHTJ, 'oracleConnection')
    // private DP_ZYWSDP_ZYWHYSFHTJ_Repository: Repository<DP_ZYWSDP_ZYWHYSFHTJ>,
    // @InjectRepository(DP_X_ZYWSDP_ZYWSJCTJ, 'oracleConnection')
    // private DP_X_ZYWSDP_ZYWSJCTJ_Repository: Repository<DP_X_ZYWSDP_ZYWSJCTJ>,
    @InjectRepository(DP_ZYWSDP_YRDWZYWHYSSBTJ, 'oracleConnection')
    private DP_ZYWSDP_YRDWZYWHYSSBTJ_Repository: Repository<DP_ZYWSDP_YRDWZYWHYSSBTJ>,
    @InjectRepository(DP_ZYWSDP_JCYRDWHYTJ, 'oracleConnection')
    private DP_ZYWSDP_JCYRDWHYTJ_Repository: Repository<DP_ZYWSDP_JCYRDWHYTJ>,
    @InjectRepository(DP_ZYWSDP_JCYRDWGMTJ, 'oracleConnection')
    private DP_ZYWSDP_JCYRDWGMTJ_Repository: Repository<DP_ZYWSDP_JCYRDWGMTJ>,
  ) {}
  private readonly logger = new Logger(FzDpService.name);

  async onModuleInit() {
    // 动态创建和启动Cron定时作业
    const interval = this.configService.get('fzdpCornInterval');
    const job = new CronJob(interval, this.handleCron.bind(this));
    this.schedulerRegistry.addCronJob('fzdpTimedTask', job as any);
    job.start();
  }

  async onModuleDestroy() {
    // 停止和销毁Cron定时作业
    const job = this.schedulerRegistry.getCronJob('fzdpTimedTask');
    job.stop();
    this.schedulerRegistry.deleteCronJob('fzdpTimedTask');
  }

  // 福州大屏定时任务
  async handleCron() {
    try {
      const dpStatistics = await this.dpStatisticsService.findAll({
        adcode: '350100000000', // 福州市
      });
      for (const dpStatistical of dpStatistics) {
        const dpRepository = this[dpStatistical.table + '_Repository'];
        if (dpRepository) {
          await dpRepository.clear();
          for (const item of dpStatistical.data) {
            try {
              await dpRepository.insert(this.stringObjVal(item));
            } catch (e) {
              this.logger.error(e);
            }
          }
        }
      }
      // this.logger.log('定时任务执行完成！！！');
    } catch (e) {
      this.logger.error(e);
    }
  }

  // 把一个对象的值都转成字符串
  stringObjVal(obj: any) {
    return Object.entries(obj).reduce((newItem, [key, value]) => {
      newItem[key] = value ? value.toString() : '0';
      return newItem;
    }, {});
  }

  // 查询某一个表的全部数据
  async find(query: { table: string }) {
    const dpRepository = this[query.table + '_Repository'];
    if (dpRepository) {
      return dpRepository.find();
    }
    return [];
  }
}
