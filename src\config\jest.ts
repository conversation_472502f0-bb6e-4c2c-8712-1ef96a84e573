export default {
  database_mongodb: {
    type: 'mongodb',
    host: 'mongodb://mdb:27017/frameData',
    useNewUrlParser: true,
    useUnifiedTopology: true,
    useCreateIndex: true,
    keepAlive: 3000,
  },
  database_redis: {
    name: 'redisConnection',
    type: 'redis',
    host: '************',
    port: 31379,
  },
  database_mysql: {
    name: 'mysqlConnection',
    type: 'mysql',
    host: '************',
    port: 30654,
    username: 'root',
    password: 'QYcbPrgyZVUIZXPE+tFhJA$',
    database: 'wj',
    logging: 'info',
    synchronize: false,
    timezone: '-08:00', // 设置时区
  },
  database_mysql_iservice: {
    name: 'mysqlConnectionIservice',
    type: 'mysql',
    host: '**************',
    port: 31526,
    username: 'root',
    password: 'QYcbPrgyZVUIZXPE+tFhJA$',
    database: 'iservice',
    logging: 'info',
    synchronize: true,
  },
};
