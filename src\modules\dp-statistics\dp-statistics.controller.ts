import { Controller, Get, Query } from '@nestjs/common';
import { DpStatisticsService } from './dp-statistics.service';
import { wrapperResponse } from 'src/utils';

@Controller('dp-statistics')
export class DpStatisticsController {
  constructor(private readonly dpStatisticsService: DpStatisticsService) {}

  @Get() // http://127.0.0.1:3000/dp-statistics?table=DP_ZYWSDP_GDQZYWSFGTJ_LB
  async find(@Query() query: { table: string; adcode?: string }) {
    return wrapperResponse(
      this.dpStatisticsService.findOne(query),
      '获取mongodb大屏统计数据',
    );
  }
}
