FROM registry.duopu.cn/zyws/docker/oraclelinux:7-slim

RUN  yum -y install oracle-release-el7 oracle-nodejs-release-el7 && \
     yum-config-manager --disable ol7_developer_EPEL && \
     yum -y install oracle-instantclient19.3-basiclite nodejs


# FROM node:18-alpine

LABEL author="<EMAIL>"

ENV PORT=3000 NODE_ENV=production

WORKDIR /app
COPY package.json README.md version /app/
COPY ./dist/ dist/

# RUN npm config set proxy http://192.168.0.3:7890
# RUN npm config set https-proxy http://192.168.0.3:7890
RUN npm config set registry https://registry.npmmirror.com
RUN npm cache clean --force
RUN npm install -g @nestjs/cli
RUN npm install --force

EXPOSE ${PORT}

ENTRYPOINT [ "npm", "run" ]
CMD [ "start:prod" ]
