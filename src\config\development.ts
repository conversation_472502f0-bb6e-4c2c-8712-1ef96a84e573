export default () => ({
  database_mongodb: {
    name: 'mongodbConnection',
    type: 'mongodb',
    // host: process.env.mdbHost || '**************',
    host: process.env.mdbHost || 'mdb.duopu.cn',
    port: 25000,
    username: process.env.mdbUser || 'dbadmin',
    password: process.env.mdbPass || 'DpPass6789',
    database: process.env.mdbName || 'zyws-xjbt0414',
    authSource: 'admin',
    logging: false,
    // synchronize: true, // 生产环境不要使用
  },
  // database_mongodb: {
  //   name: 'mongodbConnection',
  //   type: 'mongodb',
  //   host: process.env.mdbHost || '127.0.0.1',
  //   port: 27017,
  //   database: process.env.mdbName || 'zyws',
  //   authSource: 'admin',
  // },
  // database_oracle: {
  //   // CONNECT SYSTEM/Zyws666#$@************:1521/free;
  //   name: 'oracleConnection',
  //   type: 'oracle',
  //   host: '************',
  //   port: 1521,
  //   username: 'SYSTEM',
  //   password: 'Zyws666#$',
  //   database: 'SYSTEM',
  //   logging: ['error', 'schema', 'warn', 'info', 'log', 'migration'],
  //   queueMax: 1000,
  //   thickMode: false,
  //   synchronize: false, // 生产环境不要使用
  // },
  // database_lab_oracle: {
  //   // CONNECT SYSTEM/Zyws666#$@************:1521/free;
  //   name: 'labOracleConnection',
  //   type: 'oracle',
  //   host: '************',
  //   port: 1521,
  //   username: 'SYSTEM',
  //   password: 'Zyws666#$',
  //   database: 'SYSTEM',
  //   serviceName: 'FREE',
  //   logging: ['error', 'schema', 'warn', 'info', 'log', 'migration'],
  //   queueMax: 1000,
  //   thickMode: false,
  //   synchronize: false, // 生产环境不要使用
  // },
  // database_sqlserver: {
  //   name: 'sqlserverConnection',
  //   type: 'mssql',
  //   host: '************',
  //   port: 30520,
  //   username: 'sa',
  //   password: 'zwySql^%$678',
  //   database: 'wkzwy',
  //   synchronize: true, // 生产环境不要使用
  //   logging: false,
  //   options: {
  //     trustServerCertificate: true, // 信任服务器的自签名证书
  //   },
  // },
  // database_sqlserver: {
  //   name: 'sqlserverConnection',
  //   type: 'mssql',
  //   host: '*************',
  //   port: 1433,
  //   username: 'sa',
  //   password: 'yy123456!!!',
  //   database: 'WZYQHF35',
  //   // database: 'ReportServer$SQL2008',
  //   logging: false,
  //   options: {
  //     encrypt: false,
  //   },
  // },
  // database_sqlserver2: {
  //   name: 'sqlserverConnection2',
  //   type: 'mssql',
  //   host: '*************',
  //   port: 32271,
  //   username: 'sa',
  //   password: 'zwySql^%$678',
  //   database: 'master',
  //   logging: 'all',
  //   options: {
  //     trustServerCertificate: true, // 信任服务器的自签名证书
  //   },
  //   synchronize: false,
  // },
  // database_redis: {
  //   name: 'redisConnection',
  //   type: 'redis',
  //   host: '**************',
  //   port: 31379,
  // },
  database_redis: {
    name: 'redisConnection',
    type: 'redis',
    port: 62719,
    host: 'vanbox.beasts.wang',
    password: 'Tc666888.',
    db: 0,
  },
  // database_mysql: {
  //   name: 'mysqlConnection',
  //   type: 'mysql',
  //   host: '************',
  //   port: 30654,
  //   username: 'root',
  //   password: 'QYcbPrgyZVUIZXPE+tFhJA$',
  //   database: 'wj',
  //   logging: 'info',
  //   synchronize: false,
  //   timezone: '-08:00', // 设置时区
  // },

  mysql_data_sources_hb: {
    mysqlConnectionHebei: {
      ...hbMysqlDataSourceConfig,
      name: 'mysqlConnectionHebei',
      database: 'hbwjw',
    },
    mysqlConnectionHebeiTy: {
      ...hbMysqlDataSourceConfig,
      name: 'mysqlConnectionHebeiTy',
      database: 'hbty',
    },
    mysqlConnectionHebeiPr: {
      ...hbMysqlDataSourceConfig,
      name: 'mysqlConnectionHebeiPr',
      database: 'hbpr',
    },
    mysqlConnectionHebeiWx: {
      ...hbMysqlDataSourceConfig,
      name: 'mysqlConnectionHebeiWx',
      database: 'hbwx',
    },
  },

  database_mysql_iservice: {
    name: 'mysqlConnectionIservice',
    type: 'mysql',
    host: '**************', // 局域网
    // host: 'cmcc.duopu.cn',
    port: 31526,
    username: 'root',
    password: 'QYcbPrgyZVUIZXPE+tFhJA$',
    // host: 'localhost',
    // port: 3306,
    // username: 'root',
    // password: '12345678',
    database: 'iservice',
    logging: 'info',
    synchronize: false,
  },
  database_mysql_xjbt: {
    name: 'mysqlConnectionXjbt',
    type: 'mysql',
    host: '**************', // 局域网
    // host: 'cmcc.duopu.cn',
    port: 31526,
    username: 'root',
    password: 'QYcbPrgyZVUIZXPE+tFhJA$',
    // host: 'localhost',
    // port: 3306,
    // username: 'root',
    // password: '12345678',
    database: 'pn',
    logging: 'info',
    synchronize: false,
  },
  database_mysql_xjbt_jkqy: {
    name: 'mysqlConnectionXjbtJkqy',
    type: 'mysql',
    host: '**************', // 局域网
    // host: 'cmcc.duopu.cn',
    port: 31526,
    username: 'root',
    password: 'QYcbPrgyZVUIZXPE+tFhJA$',
    // host: 'localhost',
    // port: 3306,
    // username: 'root',
    // password: '12345678',
    database: 'jkqy',
    logging: 'info',
    synchronize: false,
  },

  // 存放生成的报告的文件系统目录，代码拼接：report_path + /EnterpriseID/ + 文件名
  report_path: process.cwd() + '/app/public/report',
  fzlabCornInterval: '*/30 * * * *', // 福州数据库同步定时任务
  jwt: {
    secret: process.env.JWT_SECRET || '243d477d-d6bb-4ab1-b0a9-a1b1c9419562',
    expiresIn: process.env.JWT_EXPIRES_IN || '1day',
  },
  domainNames: {
    wj: 'http://**************:7007',
  },
  rabbitmq: {
    uri: 'amqp://user:123456@localhost:5672',
    // uri: 'amqp://su:<EMAIL>:5672',
    exchanges: {
      todos: 'todos.events',
    },
    queues: {
      todoCompleted: 'todo.completed',
      todoCreated: 'todo.created',
    },
  },
  hb: {
    secretKey: process.env.SECRET_KEY,
    hbSyncQyInfoFromOB:
      process.env.hbSyncQyInfoFromOB === 'false' ? false : true,
    enableRequestTimeExpired: process.env.hbEnableRequestTimeExpired === 'true', // 是否禁用 requestTime 字段的超时检测
    requestTimeExpired: +process.env.hbRequestTimeExpired || 5 * 60 * 1000, // requestTime 字段超时时间, 单位 ms
  },
});

const hbMysqlDataSourceConfig = {
  type: 'mysql',
  // host: 'mdb.duopu.cn',
  host: '**************',
  port: 31526,
  username: 'root', // 'root@hbwjw'
  password: 'QYcbPrgyZVUIZXPE+tFhJA$', // 'zwyOB^%678'
  logging: 'info',
  synchronize: false,
  autoLoadEntities: true,
}
