import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

// 专家工作任务安排
@Entity('expert_work')
export class ExpertWork {
  @PrimaryGeneratedColumn({ comment: '自增ID' })
  id: number;

  @Column({ comment: '专家ID', type: 'int', nullable: false })
  expert_id: number;

  @Column({ comment: '发布人ID', type: 'varchar', length: 64 })
  publisher_id?: string;

  @Column({ comment: '发布人姓名', type: 'varchar', length: 64 })
  publisher_name: string;

  @Column({ comment: '管理要素', type: 'varchar', length: 64, nullable: false })
  management_element: string;

  @Column({
    comment: '工作事项',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  work_item: string;

  @Column({ comment: '事项要求', type: 'varchar', length: 255, nullable: true })
  item_requirements?: string;

  @Column({ comment: '计划完成时间', type: 'datetime', nullable: true })
  completion_time?: Date;

  @Column({
    comment:
      '任务状态 0: 未开始, 1: 进行中 2: 已完成 3: 已取消 4: 已拒绝 5: 已暂停',
    type: 'enum',
    enum: [0, 1, 2, 3, 4, 5],
    nullable: true,
    default: 0,
  })
  status: number;

  @Column({ comment: '工作汇报', type: 'text', nullable: true })
  work_report?: string;

  @Column({ comment: '汇报时间', type: 'datetime', nullable: true })
  report_time?: Date;

  @CreateDateColumn({ comment: '发布/创建时间', type: 'datetime' })
  created_at: Date;

  @UpdateDateColumn({ comment: '更新时间', type: 'datetime' })
  updated_at: Date;
}
