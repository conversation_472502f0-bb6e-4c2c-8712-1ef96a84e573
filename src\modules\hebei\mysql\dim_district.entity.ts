import { Entity, Column, PrimaryColumn } from 'typeorm';

@Entity('dim_district')
export class DimDistrict {
  @PrimaryColumn({ type: 'bigint', comment: '区划代码' })
  id: number;

  @Column({ type: 'varchar', length: 128, default: '', comment: '名称' })
  name: string;

  @Column({ type: 'varchar', length: 100, default: '', comment: '完整名称' })
  full_name: string;

  @Column({ type: 'tinyint', comment: '级别1-5,省市县镇村' })
  level: number;

  @Column({
    type: 'bigint',
    unsigned: true,
    nullable: true,
    comment: '父级区划代码',
  })
  parent_id: number;
}
