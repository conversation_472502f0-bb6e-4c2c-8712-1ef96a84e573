import { Entity, Column, ObjectIdColumn, Index } from 'typeorm';
import { ObjectId } from 'mongodb';

// 体检预约单
@Entity('healthCheckAppointment')
export class HealthCheckAppointment {
  @ObjectIdColumn()
  _id: ObjectId;

  @Column({ comment: '用工单位id' })
  EnterpriseID: string;

  @Column({
    type: 'varchar',
    length: 18,
    comment: '用工单位统一社会信用代码',
    transformer: {
      to: (value: string) => value && value.toUpperCase(),
      from: (value: string) => value,
    },
  })
  @Index()
  EnterpriseCode: string;

  @Column({ comment: '体检机构id' })
  physicalExamOrgId: string;

  @Column({
    comment:
      '体检类型 01是上岗前 02是在岗 03是离岗时 04应急 05离岗后 06普通体检',
    enum: ['01', '02', '03', '04', '05', '06'],
  })
  checkType: string;

  @Column({ comment: '是否复查 0否 1是', default: '0', enum: ['0', '1'] })
  isReview: string;

  @Column({ comment: '预估/预约体检人数' })
  peopleNum: number;

  @Column()
  startTime: Date;

  @Column()
  endTime: Date;

  @Column({ comment: '体检项目id' })
  healthcheckId?: string;

  @Column({ comment: '实际体检人员名单' })
  employeeIds: string[];

  @Column({
    comment:
      '预约单当前状态 1 提交申请 2 已确认体检日期 3 已修改体检日期 4 已确认名单 5 体检中 6 已完成 7 已取消',
    default: 1,
    enum: [1, 2, 3, 4, 5, 6, 7],
  })
  status: number;

  @Column({ comment: '预约单相关文件' })
  files?: {
    authorization: string; // 合同委托书
    enterpriseInfo: string; // 用人单位基本信息表以及职业健康检查劳动者信息表
    healthExamRecord: string; // 职业健康检查登记表
  };

  @Column({ comment: '预约单创建时间' })
  createdAt: Date;

  @Column({ comment: '预约单最近的更新时间' })
  updatedAt: Date;
}
