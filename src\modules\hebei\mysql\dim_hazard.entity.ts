import { Entity, Column, PrimaryColumn } from 'typeorm';

// 用人单位的危害因素
@Entity('dim_hazard')
export class dimHazard {
  @Column({
    type: 'int',
    nullable: true,
    comment: '危害因素代码',
  })
  hazard_code: number | null;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '危害因素名称',
  })
  hazard_name: string | null;

  @Column({ type: 'int', nullable: true, comment: '企业申报危害因素编码' })
  hazard_code_employer: number | null;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '企业申报危害因素名称',
  })
  hazard_name_employer: string | null;

  @Column({
    type: 'varchar',
    length: 160,
    nullable: true,
    comment: '危害因素CS号',
  })
  hazard_code_employer_cs: string | null;

  @Column({
    type: 'tinyint',
    nullable: true,
    comment: '危害因素类别1粉尘2化学3物理4放射5生物6其他',
  })
  hazard_type: number | null;

  @Column({
    type: 'tinyint',
    nullable: true,
    comment: '申报用危害类型1粉尘2化学3物理4放射5生物6其他',
  })
  hazard_dec_type: number | null;

  @Column({ type: 'tinyint', nullable: true, comment: '是否监管要求' })
  is_required: number | null;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '别名',
  })
  hazard_alias: string | null;

  @PrimaryColumn({
    type: 'int',
    comment: 'key值',
  })
  key: number;
}
