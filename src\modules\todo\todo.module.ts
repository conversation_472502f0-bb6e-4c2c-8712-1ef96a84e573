import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TodoService } from './todo.service';
import { TodoController } from './todo.controller';
import { Todo } from './mysql/todo.entity';
import { TodoType } from './mysql/todo-type.entity';
import { TodoMeta } from './mysql/todo-meta.entity';
import { DATA_SOURCE_NAME } from './constants';
import { UserContextMiddleware } from 'src/common/middlewares/user-context.middleware';

@Module({
  imports: [
    TypeOrmModule.forFeature(
      [Todo, TodoType, TodoMeta],
      DATA_SOURCE_NAME,
    ),
  ],
  controllers: [TodoController],
  providers: [TodoService],
  exports: [TodoService],
})
export class TodoModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(UserContextMiddleware)
      .forRoutes(
        { path: 'v*/todos', method: RequestMethod.ALL },
        { path: 'v*/todos/*', method: RequestMethod.ALL },
      );
  }
}
