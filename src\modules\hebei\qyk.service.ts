import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { Repository, Like, Between, In, LessThan, MoreThan } from 'typeorm';
import { OdsEntTjQykNew } from './mysql/ods_ent_tj_qyk_new.entity'; // 企业危害申报表/纳管企业库
import { TjQykGssj } from './mysql/tj_qyk_gssj.entity'; // 工商企业库
import { TjTechnicalqualityItemKzxgPlan } from './mysql/tj_technicalquality_item_kzxg_plan.entity';
import { QueryQykDto } from './dto/qyk.dto';
import { DimDistrict } from './mysql/dim_district.entity'; // 区域字典
import { TjTechnicalQualityItem } from './mysql/tj_technicalquality_item.entity'; // 定期检测、现状评价
import { QueryOrgInfoDto } from './dto/queryOrgInfoDto.dto';
import { TjHealthCheckJl } from './mysql/tj_health_check_jl.entity';
import { TjHealthCheck } from './mysql/tj_health_check.entity';
import { wjOccupationalDisease } from './mysql/wj_occupational_disease.entity';
import { PrJgjgProject } from './mysql/pr_jgjg_project.entity';

@Injectable()
export class QykService {
  constructor(
    @InjectRepository(OdsEntTjQykNew, 'mysqlConnectionHebeiTy')
    private readonly odsEntTjQyNewRepository: Repository<OdsEntTjQykNew>,
    @InjectRepository(TjTechnicalqualityItemKzxgPlan, 'mysqlConnectionHebeiTy')
    private readonly TjTechnicalqualityItemKzxgPlanRepository: Repository<TjTechnicalqualityItemKzxgPlan>,
    @InjectRepository(TjHealthCheckJl, 'mysqlConnectionHebeiTy')
    private readonly TjHealthCheckJlRepository: Repository<TjHealthCheckJl>,
    @InjectRepository(wjOccupationalDisease, 'mysqlConnectionHebei')
    private readonly wjOccupationalDiseaseRepository: Repository<wjOccupationalDisease>,
    @InjectRepository(TjHealthCheck, 'mysqlConnectionHebeiTy')
    private readonly TjHealthCheckRepository: Repository<TjHealthCheck>,
    @InjectRepository(DimDistrict, 'mysqlConnectionHebei')
    private readonly districtDictRepository: Repository<DimDistrict>,
    @InjectRepository(TjTechnicalQualityItem, 'mysqlConnectionHebeiTy')
    private readonly tjTechnicalQualityItemRepository: Repository<TjTechnicalQualityItem>,
    @InjectRepository(TjQykGssj, 'mysqlConnectionHebeiTy')
    private readonly tjQykGssjRepository: Repository<TjQykGssj>,
    private readonly configService: ConfigService,
    @InjectRepository(PrJgjgProject, 'mysqlConnectionHebeiPr')
    private readonly PrJgjgProjectRepository: Repository<PrJgjgProject>,
  ) {}
  private readonly logger = new Logger(QykService.name);

  // 根据区域名称查询所有子区域名称
  async getSubDistrictsByName(name: string): Promise<string[]> {
    const district = await this.districtDictRepository.findOne({
      where: { name },
    });
    if (!district) {
      throw new Error(`未找到该区域: ${name}`);
    }
    const children = await this.districtDictRepository.find({
      where: { parent_id: district.id },
    });
    return children.map((item) => item.name);
  }

  // 根据区域Id查询所有子区域名称
  async getSubDistrictsById(id: number): Promise<string[]> {
    const district = await this.districtDictRepository.findOne({
      where: { id },
    });
    if (!district) {
      throw new Error(`未找到该区域: ${id}`);
    }
    const children = await this.districtDictRepository.find({
      where: { parent_id: district.id },
    });
    return children.map((item) => item.name);
  }

  // 根据区域name查询所有id和name
  async getSubDistrictInfosById(
    name: string,
  ): Promise<{ id: number; name: string }[]> {
    const district = await this.districtDictRepository.findOne({
      where: { name },
    });
    if (!district) {
      throw new Error(`未找到该区域: ${name}`);
    }
    const children = await this.districtDictRepository.find({
      where: { parent_id: district.id },
    });
    // 返回包含 id 和 name 的对象数组
    return children.map((item) => ({
      id: item.id,
      name: item.name,
    }));
  }

  // 危害申报 - 1、累积已申报用人单位数；2、新增申报用人单位数；
  async qyHasSbCount(query: QueryQykDto): Promise<object[]> {
    const { district_id = 130000000000 } = query;
    const districts = await this.getSubDistrictsById(district_id);
    if (!districts || districts.length === 0) {
      throw new Error(`未找到该区域的子区域: ${district_id}`);
    }
    const result = await Promise.all(
      districts.map((item) =>
        this.getQyHasSbCount({
          ...query,
          districtName: item,
        }),
      ),
    );
    return result;
  }

  private async getQyHasSbCount(query: QueryQykDto): Promise<object> {
    const { year, districtName, sblb, hylb, jjlx, qygm, startTime, endTime } =
      query;
    const where: any = {};
    if (year) where.sbnf = year;
    if (districtName) where.ssdqmc = Like(`%${districtName}%`);
    if (sblb) where.sblx = sblb;
    if (hylb) where.hylb = Like(`%${hylb}%`);
    if (jjlx) where.jjlx = jjlx;
    if (qygm) where.qygm = qygm;
    if (startTime && endTime) where.sbrq = Between(startTime, endTime);
    // const list = await this.odsEntTjQyNewRepository.find({
    //   where,
    //   select: ['name', 'sblx', 'sbnf', 'sbrq'],
    // });
    const count = await this.odsEntTjQyNewRepository.count({ where });
    return { districtName, count };
  }

  // 危害申报 - 1、已检测未申报用人单位数
  async qyNotSbCount(query: QueryQykDto) {
    const { districtName = '河北省' } = query;
    const districts = await this.getSubDistrictsByName(districtName);
    if (!districts || districts.length === 0) {
      throw new Error(`未找到该区域的子区域: ${districtName}`);
    }
    const result = await Promise.all(
      districts.map((item) =>
        this.getQyNotSbCount({
          ...query,
          districtName: item,
        }),
      ),
    );
    return result;
  }
  private async getQyNotSbCount(query: QueryQykDto): Promise<object> {
    const {
      year,
      districtName,
      hylb,
      startTime,
      endTime,
      item_type,
      company_report_status,
      employer_report_status,
      jjlx,
      qygm,
    } = query;
    // 1、筛选企业危害申报表
    const qyQuery: any = {};
    if (jjlx) qyQuery.jjlx = jjlx; // 经济类型
    if (qygm) qyQuery.qygm = qygm; // 企业规模
    qyQuery.ssdqmc = Like(`%${districtName}%`); // 区域名称
    if (hylb) qyQuery.hylb = Like(`%${hylb}%`); // 行业类别
    const qyList = await this.odsEntTjQyNewRepository.find({
      where: qyQuery,
      select: ['name'],
    });
    const qyNames = qyList.map((item) => item.name);
    // 2、筛选技术服务机构检测项目表
    const where: any = {};
    if (qyNames.length > 0) where.item_name = In(qyNames); // 项目名称
    if (year) where.report_year = year; // 报告年度
    if (item_type) where.item_type = item_type; // 检测项目类型
    if (company_report_status)
      where.company_report_status = +company_report_status;
    if (employer_report_status)
      where.employer_report_status = +employer_report_status;
    if (startTime && endTime) where.report_date = Between(startTime, endTime); // 报告出具日期
    const count = await this.tjTechnicalQualityItemRepository.count({ where });
    return { districtName, count };
  }

  async QueryOrgInfoFromTjQykGssj(query: QueryOrgInfoDto): Promise<TjQykGssj> {
    this.logger.log('开始在 OB TjQykGssj 中查询用人单位信息', query);

    const record = await this.tjQykGssjRepository
      .createQueryBuilder('tj')
      .where('tj.unified_code = :unifiedCode', {
        unifiedCode: query.unifiedCode,
      })
      .getOne();
    this.logger.log(
      'OB TjQykGssj 中查询用人单位信息的查询结果',
      JSON.stringify(record),
    );
    return record;
  }

  async QueryOrgInfoFromTJQykNew(
    query: QueryOrgInfoDto,
  ): Promise<OdsEntTjQykNew> {
    this.logger.log('开始在 OB OdsEntTjQykNew 中查询用人单位信息', query);

    const record = await this.odsEntTjQyNewRepository
      .createQueryBuilder('tj')
      .where('tj.unified_code = :unifiedCode', {
        unifiedCode: query.unifiedCode,
      })
      .getOne();
    this.logger.log(
      'OB OdsEntTjQykNew 中查询用人单位信息的查询结果',
      JSON.stringify(record),
    );
    return record as unknown as OdsEntTjQykNew;
  }

  async QueryOrgInfo(query: QueryOrgInfoDto): Promise<TjQykGssj> {
    const recordFromTjQykGssj = await this.QueryOrgInfoFromTjQykGssj(query);
    return recordFromTjQykGssj;
  }

  async qyStsProjects(query) {
    const queryData: any = {};
    if (query.name) {
      queryData.item_name = Like(`%${query.name}%`);
    }
    queryData.project = query.cname;
    const docs = await this.TjTechnicalqualityItemKzxgPlanRepository.find({
      where: queryData,
    });
    return docs;
  }

  async qyHealthCheckList(query) {
    console.log(query);
    const docs = await this.TjHealthCheckRepository.createQueryBuilder(
      'tj_health_check',
    )
      .where("tj_health_check.yrdwshxydm LIKE '91130827MA0FGWN1XU'")
      .leftJoinAndSelect(
        TjHealthCheckJl,
        'tj_health_check_jl',
        'tj_health_check.id = tj_health_check_jl.reg_id',
      )
      .getRawMany();
    return docs;
  }

  async qySuspects(query) {
    console.log(3213, query);
    const docs = await this.TjHealthCheckRepository.createQueryBuilder(
      'tj_health_check',
    )
      .where("tj_health_check.yrdwshxydm LIKE '91130827MA0FGWN1XU'")
      .leftJoinAndSelect(
        TjHealthCheckJl,
        'tj_health_check_jl',
        'tj_health_check.id = tj_health_check_jl.reg_id',
      )
      .getRawMany();

    return docs;
  }

  async qyOccupationalDisease() {
    const docs = await this.wjOccupationalDiseaseRepository
      .createQueryBuilder('wj_occupational_disease')
      .select([
        'report_card_code',
        'diagnosis_date',
        'appraisal_date',
        'diagnosis_unit',
      ])
      .select('YEAR(wj_occupational_disease.diagnosis_date)', 'year')
      .addSelect('COUNT(*)', 'count')
      .groupBy('YEAR(wj_occupational_disease.diagnosis_date)')
      .orderBy('year', 'DESC')
      .getRawMany();
    console.log(333, docs);
  }

  async getoccupationalDiseasetList(query) {
    const { page = 1, size = 10 } = query;
    const result1 = await this.wjOccupationalDiseaseRepository
      .createQueryBuilder('w')
      .select("DATE_FORMAT(w.diagnosis_date, '%Y')", 'year')
      .addSelect('COUNT(w.id)', 'diagnosisNum')
      .addSelect(
        "COUNT(CASE WHEN w.appraisal_disease_name != '' THEN 1 END)",
        'diagnosisTrueNum' // 不为空的数量
      )
      .andWhere('w.employer_unified_code = :unifiedCode',{ unifiedCode: query.unifiedCode })
      // .andWhere('w.disease_type != :diseaseType', { diseaseType: '无职业病' })
      .groupBy("DATE_FORMAT(w.diagnosis_date, '%Y')")
      .orderBy("DATE_FORMAT(w.diagnosis_date, '%Y')", 'DESC')
      .getRawMany()

    /* const result2 = await this.wjOccupationalDiseaseRepository
      .createQueryBuilder('w')
      .select("DATE_FORMAT(w.appraisal_report_date, '%Y')", 'year')
      .addSelect('COUNT(w.id)', 'appraisalProvinceNum')
      .addSelect('COUNT(w.id)', 'appraisalCityNum') 
      .groupBy("DATE_FORMAT(w.appraisal_report_date, '%Y')")
      .orderBy("DATE_FORMAT(w.appraisal_report_date, '%Y')", 'DESC')
      .getRawMany()   */
    
    // 处理数据
    let result = [];
    result = result1;
    /* result2.forEach((item) => {
      let index = result.findIndex((element) => element.year === item.year);
      if ( index === -1 ) {
        for (let i=0 ;i < result.length; i++){
          if (item.year > result[i].year){
            result.splice(i,0,item)
            break;
          }
        }
      }else{
        result[index] = {...result[index],appraisalProvinceNum: item.appraisalProvinceNum,appraisalCityNum: item.appraisalCityNum}
      }
    }) */
    return {
      list: result.slice((page - 1) * size, page * size),
      pagination: {
        page: page,
        size: size,
        total: result.length,
      },
    };
  }

  async getDiagnosisList(query) {
    const list = await this.wjOccupationalDiseaseRepository
    .createQueryBuilder('disease')
    .select([
      'disease.patient_name',
      'disease.disease_name',
      'disease.gender',
      'disease.date_of_birth',
      'disease.certificate_number',
      'disease.worker_contact_info',
      'disease.diagnosis_unit',
      'disease.diagnosis_date',
      'disease.reporting_date',
      'disease.disease_type',
    ])
    .where('YEAR(disease.diagnosis_date) = :year', { year: query.year })
    .andWhere('disease.employer_unified_code = :unifiedCode',{ unifiedCode: query.unifiedCode })
    .getMany();

    // 处理数据
    let result = []
    list.forEach((item) => {
      let currentYear = new Date().getFullYear();
      let personYear = new Date(item.date_of_birth).getFullYear();
      result.push({...item, age: currentYear - personYear + 1})
    })

    console.log(result);
    
    return {
      list: result,
    };
      
  }

  async getDiagnosisTrueList(query) {
    const list = await this.wjOccupationalDiseaseRepository
    .createQueryBuilder('disease')
    .select([
      'disease.patient_name',
      'disease.disease_name',
      'disease.gender',
      'disease.date_of_birth',
      'disease.certificate_number',
      'disease.worker_contact_info',
      'disease.diagnosis_unit',
      'disease.diagnosis_date',
      'disease.reporting_date',
      'disease.disease_type',
    ])
    .where('YEAR(disease.diagnosis_date) = :year', { year: query.year })
    .andWhere('disease.disease_type != :diseaseType', { diseaseType: '无职业病' })
    .andWhere('disease.employer_unified_code = :unifiedCode',{ unifiedCode: query.unifiedCode })
    .getMany();

    // 处理数据
    let result = []
    list.forEach((item) => {
      let currentYear = new Date().getFullYear();
      let personYear = new Date(item.date_of_birth).getFullYear();
      result.push({...item, age: currentYear - personYear + 1})
    })
    
    return {
      list: result,
    };
  }

  async getAppraisalCityList(query) {   
    /* const list = await this.wjOccupationalDiseaseRepository
    .createQueryBuilder('disease')
    .select([
      'disease.patient_name',
      'disease.disease_name',
      'disease.gender',
      'disease.date_of_birth',
      'disease.certificate_number',
      'disease.worker_contact_info',
      'disease.reporting_unit',
      'disease.appraisal_date',
      'disease.reporting_date',
    ])
    .where('YEAR(disease.appraisal_report_date) = :year', { year: query.year })
    .getMany();

    // 处理数据
    let result = []
    list.forEach((item) => {
      let currentYear = new Date().getFullYear();
      let personYear = new Date(item.date_of_birth).getFullYear();
      result.push({...item, age: currentYear - personYear + 1})
    })
    
    return {
      list: result,
    }; */
  }

  async getAppraisalProvinceList(query) {   
    /* const list = await this.wjOccupationalDiseaseRepository
    .createQueryBuilder('disease')
    .select([
      'disease.patient_name',
      'disease.disease_name',
      'disease.gender',
      'disease.date_of_birth',
      'disease.certificate_number',
      'disease.worker_contact_info',
      'disease.reporting_unit',
      'disease.appraisal_date',
      'disease.reporting_date',
    ])
    .where('YEAR(disease.appraisal_report_date) = :year', { year: query.year })
    .getMany();

    // 处理数据
    let result = []
    list.forEach((item) => {
      let currentYear = new Date().getFullYear();
      let personYear = new Date(item.date_of_birth).getFullYear();
      result.push({...item, age: currentYear - personYear + 1})
    })
    
    return {
      list: result,
    }; */
  }

  async qyOnlineDeclaration() {}

  async getPeriodicTestList(searchQuery) {
    const { page = 1, size = 10 } = searchQuery;
    const query: any = {
      SERVICE_STATUS: '3'
    };
    query.SERVICE_TYPE = '1';
    if (searchQuery.cname) {
      query.NAME = searchQuery.cname;
    }
    if (searchQuery.code) {
      query.CREDITCODE = searchQuery.code;
    }
    
    const result = await this.PrJgjgProjectRepository.find({
      where: { ...query },
      select: [
        'NAME',
        'REPORT_NUMBER',
        'SERVICE_OPERNAME',
        'REPORT_DATE',
        'JCJG_EXCEED',
      ],
      skip: (page - 1) * size,
      take: size,
    });
    const countResult = await this.PrJgjgProjectRepository.count({ where: query });
    return {
      list: result,
      pagination: {
        page: page,
        size: size,
        total: countResult || 0,
      },
    };
  }

  async getcurrentEvaluationList(searchQuery) {   
    const { page = 1, size = 10 } = searchQuery;
    const query: any = {
      SERVICE_STATUS: '3'
    };
    query.SERVICE_TYPE = '2';
    if (searchQuery.cname) {
      query.NAME = searchQuery.cname;
    }
    if (searchQuery.code) {
      query.CREDITCODE = searchQuery.code;
    }
    const result = await this.PrJgjgProjectRepository.find({
      where: { ...query },
      select: [
        'NAME',
        'REPORT_NUMBER',
        'SERVICE_OPERNAME',
        'REPORT_DATE',
        'JCJG_EXCEED',
      ],
      skip: (page - 1) * size,
      take: size,
    });
    const countResult = await this.PrJgjgProjectRepository.count({ where: query });
    return {
      list: result,
      pagination: {
        page: page,
        size: size,
        total: countResult || 0,
      },
    };
  }
}
