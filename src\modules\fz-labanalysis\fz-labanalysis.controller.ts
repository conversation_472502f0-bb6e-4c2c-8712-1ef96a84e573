import { Controller, Post, Body } from '@nestjs/common';
import { FzLabanalysisService } from './fz-labanalysis.service';
import { wrapperResponse, error } from 'src/utils';

@Controller('fz-labanalysis')
export class FzLabanalysisController {
  constructor(private readonly fzLabanalysisService: FzLabanalysisService) {}

  @Post('/parameters') // http://127.0.0.1:3000/fz-labanalysis/parameters
  async parameters(@Body() body) {
    const { projectSN, batch } = body;
    if (!projectSN || !batch) {
      return error('请求参数有误', 401);
    }
    return wrapperResponse(
      this.fzLabanalysisService.copyMongoToOracle(projectSN, batch),
      '导入完成',
    );
  }
}
