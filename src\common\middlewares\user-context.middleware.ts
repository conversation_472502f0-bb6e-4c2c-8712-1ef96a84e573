import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class UserContextMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    const userId = req.headers['x-user-id'];
    const userOpenId = req.headers['x-user-openid-id'];
    const userOrgId = req.headers['x-user-org-id'];
    const username = req.headers['x-username'];

    req['user'] = {
      id: userId,
      openId: userOpenId,
      orgId: userOrgId,
      username,
    };

    next();
  }
}
