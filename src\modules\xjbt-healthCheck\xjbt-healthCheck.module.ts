import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { XjbtHealthCheckController } from './xjbt-healthCheck.controller';
import { XjbtHealthCheckService } from './xjbt-healthCheck.service';
import { EmployeeBasicInfo } from '../dak/entities/employeeBasicInfo.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature(
      [EmployeeBasicInfo],
      'mongodbConnection',
    ),
  ],
  controllers: [XjbtHealthCheckController],
  providers: [XjbtHealthCheckService],
  exports: [XjbtHealthCheckService],
})
export class XjbtHealthCheckModule {} 