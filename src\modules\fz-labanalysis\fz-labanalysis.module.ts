import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { FzLabanalysisController } from './fz-labanalysis.controller';
import { FzLabanalysisService } from './fz-labanalysis.service';

// Oracle
import { LIMS_ZYWS_MREGIST } from './oracle/LIMS_ZYWS_MREGIST.entity';
import { LIMS_ZYWS_SAMPLE } from './oracle/LIMS_ZYWS_SAMPLE.entity';
import { LIMS_ZYWS_SAMPCHECKITEM } from './oracle/LIMS_ZYWS_SAMPCHECKITEM.entity';
import { LIMS_ZYWS_CHKRSLTM } from './oracle/LIMS_ZYWS_CHKRSLTM.entity';
// Mongo
import { JcqlcProject } from './mongo/jcqlc-project.entity';
import { ServiceOrg } from './mongo/service-org.entity';
import { ServiceEmployee } from './mongo/service-employee.entity';
import { SamplePreparation } from './mongo/sample-preparation.entity';
import { temporaryFileRecord } from './mongo/temporary-file-record.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature(
      [
        LIMS_ZYWS_MREGIST,
        LIMS_ZYWS_SAMPLE,
        LIMS_ZYWS_SAMPCHECKITEM,
        LIMS_ZYWS_CHKRSLTM,
      ],
      'labOracleConnection',
    ),
    TypeOrmModule.forFeature(
      [
        JcqlcProject,
        ServiceOrg,
        ServiceEmployee,
        SamplePreparation,
        temporaryFileRecord,
      ],
      'mongodbConnection',
    ),
    ScheduleModule.forRoot(), // 定时任务
  ],
  controllers: [FzLabanalysisController],
  providers: [FzLabanalysisService],
  exports: [FzLabanalysisService], // 导出服务，供其他模块使用
})
export class FzLabanalysisModule {}
