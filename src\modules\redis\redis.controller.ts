import { Controller, Get, Post, Query, Body } from '@nestjs/common';
import { RedisService } from './redis.service';
import { wrapperResponse } from 'src/utils';

@Controller('redis')
export class RedisController {
  constructor(private readonly redisService: RedisService) {}

  @Get() // http://localhost:3000/redis?key=hello
  async getRedis(@Query('key') key: string): Promise<string> {
    if (!key) {
      return wrapperResponse(
        this.redisService.getAllKeysAndValues(),
        '获取redis所有缓存的keys',
      );
    }
    return wrapperResponse(
      this.redisService.get(key),
      '获取redis缓存, key: ' + key,
    );
  }

  @Post() // http://localhost:3000/redis
  async setRedis(
    @Body('key') key: string,
    @Body('value') value: any,
    @Body('ttl') ttl: number, // 过期时间,单位秒
  ): Promise<string> {
    return wrapperResponse(
      this.redisService.set(key, value, ttl),
      `设置redis缓存数据: ${key} - ${value} - ${ttl ? ttl + 's' : '永不过期'}`,
    );
  }
}
