import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { MongoRepository } from 'typeorm';
import { SystemConfigs } from './system-configs.entity';

@Injectable()
export class SystemConfigsService {
  constructor(
    @InjectRepository(SystemConfigs, 'mongodbConnection')
    private readonly systemConfigsRepository: MongoRepository<SystemConfigs>,
  ) {}

  async findOne(select: (keyof SystemConfigs)[]): Promise<SystemConfigs> {
    return await this.systemConfigsRepository.findOne({
      where: {},
      select,
    });
  }

  async update(params = {}): Promise<any> {
    return await this.systemConfigsRepository.updateOne({}, { $set: params });
  }

  async create(systemConfigs: SystemConfigs): Promise<SystemConfigs> {
    return await this.systemConfigsRepository.save(systemConfigs);
  }
}
