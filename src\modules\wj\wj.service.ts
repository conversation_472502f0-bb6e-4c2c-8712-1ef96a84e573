import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TUser } from './mysql/t_user.entity';
import { TAccount } from './mysql/t_account.entity';
import { TUserRole } from './mysql/t_user_role.entity';
import { TRole } from './mysql/t_role.entity';
import { TDept } from './mysql/t_dept.entity';
import { UserDto } from './wj.dto';

@Injectable()
export class WjService {
  constructor(
    @InjectRepository(TUser, 'mysqlConnection')
    private TUserRepository: Repository<TUser>,

    @InjectRepository(TAccount, 'mysqlConnection')
    private TAccountRepository: Repository<TAccount>,

    @InjectRepository(TUserRole, 'mysqlConnection')
    private TUserRoleRepository: Repository<TUserRole>,

    @InjectRepository(TRole, 'mysqlConnection')
    private TRoleRepository: Repository<TRole>,

    @InjectRepository(TDept, 'mysqlConnection')
    private TDeptRepository: Repository<TDept>,
  ) {}
  private readonly logger = new Logger(WjService.name);

  // 校验用户信息
  async checkAccount(data: UserDto): Promise<any> {
    // 1、判断是否存在用户
    let account = await this.findOneAccount({ id: data.id });
    const userData: {
      is_deleted: number;
      status: number;
      name?: string;
      phone?: string;
      gender?: string;
    } = {
      is_deleted: 0,
      status: 1,
    };
    userData.name = data.name || data.auth_account || data.phone;
    if (data.phone) userData.phone = data.phone;
    let gender = '';
    if (data.gender) {
      if (data.gender === '0') {
        gender = 'M';
      } else if (data.gender === '1') {
        gender = 'F';
      }
    }
    if (gender) userData.gender = gender;
    if (account) {
      // 2、更新用户信息
      if (account.is_deleted === 1 || account.status === 0) {
        await this.updateAccount(account.id, { is_deleted: 0, status: 1 });
      }
      await this.updateUser(account.user_id, userData);
      this.logger.log(`更新t_user: ${JSON.stringify(userData)}`);
    } else {
      // 3、获取机构信息
      console.log(3333, data);
      const dept = await this.checkDept(data);
      // 4、创建用户
      let user = await this.findOneUser({
        dept_id: dept.id,
        phone: data.phone,
        name: userData.name,
      });
      if (!user) {
        user = await this.createUser({
          ...userData,
          id: this.createId(),
          dept_id: dept.id,
        });
        this.logger.log(`创建t_user: ${JSON.stringify(user)}`);
      }
      account = await this.createAccount({
        id: data.id,
        user_id: user.id,
        auth_account: data.auth_account,
        auth_secret: data.auth_secret || '',
        secret_salt: data.secret_salt || '',
      });
      this.logger.log(`创建t_account: ${JSON.stringify(account)}`);
      // 5、给用户绑定角色
      await this.bindRole(user.id);
    }
    return account;
  }

  // 给用户绑定角色
  async bindRole(user_id: string) {
    let role = await this.TRoleRepository.findOneBy({
      code: 'user',
    });
    if (!role) {
      role = await this.TRoleRepository.save({
        id: this.createId(),
        name: '普通用户',
        code: 'user',
        status: 1,
        is_deleted: 0,
        authority:
          'answer,answer:list,answer:detail,answer:upload,answer:export,project,project:list,project:detail,project:report,repo,repo:book,repo:detail,user,user:update,answer:create,answer:update,answer:delete,project:create,project:update,project:delete,repo:create,repo:update,repo:delete,repo:list,template,template:update,template:delete,template:list,template:create',
      });
      this.logger.log(`创建t_role: ${JSON.stringify(role)}`);
    }
    const role_id = role.id;
    const userRole = await this.TUserRoleRepository.findOneBy({
      user_id,
      role_id,
    });
    if (!userRole) {
      const newUserRole = await this.TUserRoleRepository.save({
        id: this.createId(),
        user_id,
        role_id,
      });
      this.logger.log(`创建t_user_role: ${JSON.stringify(newUserRole)}`);
    }
  }

  // 新建/查询机构
  async checkDept(data: UserDto): Promise<any> {
    let dept = await this.TDeptRepository.findOneBy({ id: data.org_id });
    if (dept) {
      if (
        dept.name !== data.org_name ||
        dept.short_name !== data.org_short_name ||
        dept.is_deleted === 1 ||
        dept.code !== data.org_code
      ) {
        await this.TDeptRepository.update(data.org_id, {
          name: data.org_name,
          short_name: data.org_short_name || '',
          is_deleted: 0,
          code: data.org_code || data.org_id,
        });
      }
    } else {
      dept = await this.TDeptRepository.save({
        id: data.org_id,
        name: data.org_name,
        short_name: data.org_short_name || data.org_name,
        code: data.org_code || data.org_id,
        parent_id: '1',
      });
      this.logger.log(`创建t_dept: ${JSON.stringify(dept)}`);
    }
    return dept;
  }

  createId(): string {
    return Date.now().toString() + Math.floor(Math.random() * 1000).toString();
  }

  async findOneUser(query = {}): Promise<TUser> {
    return await this.TUserRepository.findOne({
      where: query,
    });
  }

  async createUser(user: object): Promise<TUser> {
    return await this.TUserRepository.save(user);
  }

  async updateUser(id: string, data = {}): Promise<object> {
    return await this.TUserRepository.update(id, data);
  }

  async findOneAccount(query = {}): Promise<TAccount> {
    return await this.TAccountRepository.findOne({
      where: query,
    });
  }

  async createAccount(account: object): Promise<TAccount> {
    return await this.TAccountRepository.save(account);
  }

  async updateAccount(id: string, data = {}): Promise<object> {
    return await this.TAccountRepository.update(id, data);
  }
}
