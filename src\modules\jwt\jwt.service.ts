import { Injectable } from '@nestjs/common';
import { JwtService as NestJwtService } from '@nestjs/jwt';

@Injectable()
export class JwtService {
  constructor(private readonly jwtService: NestJwtService) {}

  // 生成token
  async generateToken(payload: any): Promise<string> {
    return this.jwtService.sign(payload);
  }

  // 验证token
  async verifyToken(token: string): Promise<any> {
    return this.jwtService.verify(token);
  }
}
