export default () => ({
  database_mongodb: {
    name: 'mongodbConnection',
    type: 'mongodb',
    hostReplicaSet: process.env.mdbHostRs,
    port: +process.env.mdbPort || 27017,
    replicaSet: process.env.mdbRs,
    readPreference: 'nearest',
    ssl: false,
    useNewUrlParser: true,
    useUnifiedTopology: true,
    authSource: 'admin',
    synchronize: false,
    username: process.env.mdbUser,
    password: process.env.mdbPass,
    database: process.env.mdbName,
    logging: 'all',
    autoLoadEntities: false,
  },
  database_oracle: {
    name: 'oracleConnection',
    type: 'oracle',
    thickMode: true,
    host: process.env.oracleHost,
    port: +process.env.oraclePort,
    username: process.env.oracleUser,
    password: process.env.oraclePass,
    // database: process.env.oracleDb || 'SYSTEM',
    serviceName: process.env.oracleServiceName,
    queueMax: 1000,
    // logging: 'all',
    logging: ['error', 'schema', 'warn', 'info', 'log', 'migration'],
    connectString: `${process.env.oracleHost}:${process.env.oraclePort}/${process.env.oracleServiceName}`,
    synchronize: false,
  },

  database_lab_oracle: {
    name: 'labOracleConnection',
    type: 'oracle',
    thickMode: true,
    host: process.env.oracleLabHost,
    port: +process.env.oracleLabPort,
    username: process.env.oracleLabUser,
    password: process.env.oracleLabPass,
    database: process.env.oracleLabDb || 'datachange',
    serviceName: process.env.oracleLabServiceName,
    queueMax: 1000,
    // logging: 'all',
    logging: ['error', 'schema', 'warn', 'info', 'log', 'migration'],
    // connectString: `${process.env.oracleLabHost}:${process.env.oracleLabPort}/${process.env.oracleLabServiceName}`,
    synchronize: false,
  },
  fzdpCornInterval: '*/10 * * * *', // 福州大屏定时任务
  fzlabCornInterval: '*/10 * * * *', // 福州数据库同步定时任务
  fzOrganization: '123501004880998676', //福州在数据库内编号 => 查机构id
});
