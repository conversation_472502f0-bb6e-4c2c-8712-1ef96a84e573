import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

// 职业健康专家
@Entity('expert')
export class Expert {
  @PrimaryGeneratedColumn({ comment: '自增ID' })
  id: number;

  @Column({
    comment: '门户用户ID',
    type: 'varchar',
    length: 64,
    nullable: true,
  })
  user_id?: string;

  @Column({ comment: '姓名', type: 'varchar', length: 64 })
  name: string;

  @Column({
    comment: '性别',
    type: 'enum',
    enum: ['F', 'M'],
  })
  gender: string;

  @Column({ comment: '出生日期', type: 'datetime', nullable: true })
  birthday?: Date;

  @Column({
    comment: '证件类型，1是身份证',
    type: 'int',
    default: 1,
    nullable: true,
  })
  id_type: number;

  @Column({ comment: '证件编号', type: 'varchar', length: 64 })
  id_number: string;

  @Column({ comment: '联系电话', type: 'varchar', length: 11 })
  phone: string;

  @Column({ comment: '专家类别id', type: 'int', nullable: true })
  category_id?: number;

  @Column({
    comment: '专家级别 1、兵团级；2、师市级；3、团镇级',
    type: 'enum',
    enum: [1, 2, 3],
  })
  level: number;

  @Column({ comment: '所学专业', type: 'varchar', length: 64, nullable: true })
  major?: string;

  @Column({ comment: '学历', type: 'int', nullable: true })
  education?: number;

  @Column({ comment: '工作单位', type: 'varchar', length: 64, nullable: true })
  work_unit?: string;

  @Column({ comment: '职称', type: 'varchar', length: 64, nullable: true })
  title?: string;

  @Column({ comment: '职务', type: 'varchar', length: 64, nullable: true })
  position?: string;

  @Column({ comment: '受聘时间', type: 'datetime', nullable: true })
  employment_date?: Date;

  @Column({ comment: '解聘时间', type: 'datetime', nullable: true })
  dismissal_date?: Date;

  @Column({
    comment: '相关工作年限',
    type: 'int',
    nullable: true,
  })
  work_years?: number;

  @Column({ comment: '专家简介', type: 'varchar', length: 255, nullable: true })
  introduction?: string;

  @Column({ comment: '专家头像', type: 'varchar', length: 64, nullable: true })
  avatar?: string;

  @Column({
    comment: '所在区域编码',
    type: 'varchar',
    length: 9,
    nullable: true,
  })
  area_code?: string;

  @Column({ comment: '住址', type: 'varchar', length: 255, nullable: true })
  address?: string;

  @Column({ comment: '电子邮箱', type: 'varchar', length: 64, nullable: true })
  email?: string;

  @Column({ comment: '开户行', type: 'varchar', length: 64, nullable: true })
  bank?: string;

  @Column({ comment: '银行卡号', type: 'varchar', length: 64, nullable: true })
  bank_card?: string;

  @Column({
    comment: '是否为第一类专家，0否 1是',
    type: 'enum',
    enum: [0, 1],
    nullable: true,
  })
  first_type?: number;

  @Column({
    comment: '状态, 0已注销 1正常 2出差 3休假 4已解聘 5解聘中 6专家推荐',
    type: 'enum',
    enum: [0, 1, 2, 3, 4, 5, 6],
    default: 1,
  })
  state: number;

  @Column({
    comment: '专家申报状态: 0: 未申报, 1: 审核通过, 2: 待审核 3: 审核未通过',
    type: 'enum',
    enum: [0, 1, 2, 3],
    nullable: true,
    default: 0,
  })
  declaration_status?: number;

  @Column({
    comment: '健康企业评审-企业申报id集合',
    type: 'simple-json',
    nullable: true,
  })
  declaration_ids?: string[];

  @CreateDateColumn({ comment: '创建时间', type: 'datetime' })
  created_at: Date;

  @UpdateDateColumn({ comment: '更新时间', type: 'datetime' })
  updated_at: Date;
}
