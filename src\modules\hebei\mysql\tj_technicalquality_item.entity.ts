import { Entity, Column, PrimaryColumn } from 'typeorm';

@Entity('tj_technicalquality_item')
export class TjTechnicalQualityItem {
  @PrimaryColumn({ type: 'varchar', length: 64, comment: '主键' })
  id: string;

  @Column({
    type: 'varchar',
    length: 64,
    nullable: true,
    comment: '用人单位id',
  })
  e_id: string;

  @Column({
    type: 'varchar',
    length: 64,
    nullable: true,
    comment: '技术服务机构id',
  })
  tq_id: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '项目名称' })
  item_name: string;

  @Column({ type: 'varchar', length: 128, nullable: true, comment: '合同编号' })
  contract_code: string;

  @Column({ type: 'date', nullable: true, comment: '合同签订日期' })
  contract_date: string;

  @Column({ type: 'text', nullable: true, comment: '合同文件地址' })
  contract_url: string;

  @Column({ type: 'date', nullable: true, comment: '报告出具日期' })
  report_date: string;

  @Column({ type: 'varchar', length: 6, nullable: true, comment: '报告年度' })
  report_year: string;

  @Column({
    type: 'varchar',
    length: 2,
    nullable: true,
    comment: '职业病危害风险分类',
  })
  hazard_level: string;

  @Column({ type: 'smallint', nullable: true, comment: '状态' })
  status: number;

  @Column({
    type: 'smallint',
    nullable: true,
    comment: '项目类型 1定期检测 2现状评价 3预评价 4控制效果评价',
  })
  item_type: number;

  @Column({
    type: 'tinyint',
    nullable: true,
    comment: '技术服务机构上报0未上报1已上报',
  })
  company_report_status: number;

  @Column({
    type: 'tinyint',
    nullable: true,
    comment: '用人单位上报0未上报1已上报',
  })
  employer_report_status: number;

  @Column({ type: 'varchar', length: 128, nullable: true, comment: '所属行业' })
  industry: string;

  @Column({ type: 'tinyint', nullable: true, comment: '0-存在 1-删除' })
  del_flag: number;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '创建时间' })
  create_by: string;

  @Column({ type: 'datetime', nullable: true, comment: '创建人' })
  create_time: Date;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '修改人' })
  update_by: string;

  @Column({ type: 'datetime', nullable: true, comment: '修改时间' })
  update_time: Date;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '备注' })
  remark: string;

  @Column({ type: 'datetime', nullable: true, comment: '上传时间' })
  report_time: Date;

  @Column({
    type: 'tinyint',
    nullable: true,
    comment: '建设项目性质 1新建 2改建 3扩建',
  })
  item_nature: number;

  @Column({ type: 'datetime', nullable: true, comment: '办结时间' })
  end_date: Date;

  @Column({
    type: 'varchar',
    length: 2,
    nullable: true,
    comment: '当前环节 1办结 2审核中',
  })
  link: string;

  @Column({
    type: 'int',
    nullable: true,
    comment: '治理情况0-超标 1未超标 2符合 3基本符合 4不符合',
  })
  zlqk: number;

  @Column({
    type: 'int',
    nullable: true,
    comment: '评价情况-0符合项1基本符合项2不符合项',
  })
  pjqk: number;
}
