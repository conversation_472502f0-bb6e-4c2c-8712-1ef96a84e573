import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Dak<PERSON>ontroller } from './dak.controller';
import { DakService } from './dak.service';
import { DiagnosticRecord } from './entities/diagnosticRecord.entity';
import { DeterminationRecord } from './entities/determinationRecord.entity';
import { Users } from '../sxccduijie/entities/users.entity';
import { Employees } from '../sxccduijie/entities/employees.entity';
import { HealthCheckRegister } from './entities/HealthCheckRegister.entity';
import { HealthSurvArchives } from './entities/HealthSurvArchives.entity';
import { EmployeeBasicInfo } from './entities/employeeBasicInfo.entity';


@Module({
  imports: [
    TypeOrmModule.forFeature(
      [DiagnosticRecord, DeterminationRecord, Users, HealthCheckRegister, HealthSurvArchives, EmployeeBasicInfo],
      'mongodbConnection',
    ),
  ],
  controllers: [DakController],
  providers: [DakService],
  exports: [DakService],
})
export class DakModule { }
