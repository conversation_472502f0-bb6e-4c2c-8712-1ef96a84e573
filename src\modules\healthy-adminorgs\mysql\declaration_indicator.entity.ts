import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('declaration_indicator')
export class DeclarationIndicator {
  @PrimaryGeneratedColumn({ name: 'id', type: 'int', comment: '自增ID' })
  id: number;

  @Column({ comment: '申报id', type: 'int' })
  declaration_id: number;

  @Column({ comment: '父级id', type: 'int' })
  parent_id: number;

  @Column({ comment: '指标名称', type: 'varchar' })
  name: string;

  @Column({ comment: '分值', type: 'int' })
  score: number;

  @Column({
    type: 'json',
    comment: '复核方式: 资料审查 现场勘察 访谈',
  })
  review_mode: object;

  @Column({ comment: '赋分标准', type: 'text' })
  score_criteria: string;

  @Column({
    type: 'enum',
    enum: [
      '普通指标',
      '存在职业危害因素企业的特有指标',
      '企业内部设置食堂或餐厅',
    ],
    comment: '指标类别',
  })
  indicator_type: string;

  @Column({ comment: '自评分数', type: 'int' })
  self_score: number;

  @Column({
    type: 'enum',
    enum: ['0', '1'],
    comment: '自评分值合理缺项: 0 不缺项 1 缺项',
  })
  self_missing: string;

  @Column({ comment: '师市级分数', type: 'int' })
  city_score: number;

  @Column({
    type: 'enum',
    enum: ['0', '1'],
    comment: '师市级分值合理缺项: 0 不缺项 1 缺项',
  })
  city_missing: string;

  @Column({ comment: '兵团级分数', type: 'int' })
  province_score: number;

  @Column({
    type: 'enum',
    enum: ['0', '1'],
    comment: '兵团级分值合理缺项: 0 不缺项 1 缺项',
  })
  province_missing: string;

  children: DeclarationIndicator[];
}
