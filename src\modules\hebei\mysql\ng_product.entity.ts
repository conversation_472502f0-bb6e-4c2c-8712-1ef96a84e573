import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

// 用人单位产品
@Entity('ng_product')
export class ngProduct {
  @PrimaryGeneratedColumn({ type: 'int', comment: 'product_id' })
  id: number;

  @Column({ type: 'int', nullable: true, comment: '申报表id' })
  declaration_id: number | null;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '产品名称' })
  product_name: string | null;

  @Column({ type: 'int', nullable: true, comment: '年产量' })
  annual_production: number | null;

  @Column({ type: 'varchar', length: 20, nullable: true, comment: '单位' })
  unit: string | null;
}
