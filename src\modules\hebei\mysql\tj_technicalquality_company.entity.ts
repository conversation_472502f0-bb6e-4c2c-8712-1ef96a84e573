import { Entity, Column } from 'typeorm';

@Entity('tj_technicalquality_company')
export class tjTechnicalqualityCompany {
  @Column({ type: 'varchar', length: 64, comment: '主键', primary: true, })
  id: string;

  @Column({ type: 'varchar', length: 64, nullable: true, comment: '第三方id' })
  third_id: string;

  @Column({ type: 'varchar', length: 128, nullable: true, comment: '机构名称' })
  company_name: string;

  @Column({ type: 'varchar', length: 12, nullable: true, comment: '区划' })
  district_id: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '机构负责人' })
  head_person: string;

  @Column({ type: 'varchar', length: 18, nullable: true, comment: '社会统一信用代码' })
  unified_code: string;

  @Column({ type: 'varchar', length: 128, nullable: true, comment: '机构网址' })
  web_url: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '办公地址' })
  address: string;

  @Column({ type: 'varchar', length: 8, nullable: true, comment: '办公场所产权性质' })
  real_estate_type: string;

  @Column({ type: 'varchar', length: 12, nullable: true, comment: '邮编' })
  postal_code: string;

  @Column({ type: 'varchar', length: 128, nullable: true, comment: '实验室地址' })
  lab_address: string;

  @Column({ type: 'varchar', length: 8, nullable: true, comment: '实验室产权性质' })
  lab_real_estate_type: string;

  @Column({ type: 'varchar', length: 20, nullable: true, comment: '传真' })
  fax: string;

  @Column({ type: 'int', nullable: true, comment: '机构总人数' })
  people_num: number;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '固定资产（万元）' })
  fixed_assets: string;

  @Column({ type: 'date', nullable: true, comment: '成立日期' })
  setup_date: Date;

  @Column({ type: 'varchar', length: 12, nullable: true, comment: '机构性质' })
  company_type: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '工作场所建筑面积' })
  area: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '实验室使用面积' })
  lab_area: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '档案室使用面积' })
  archives_area: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '法人' })
  legal_person: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '机构负责人手机号' })
  head_person_phone: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '机构负责人职务' })
  head_person_post: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '业务联系人' })
  business_person: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '业务联系人手机号' })
  business_person_phone: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '业务联系人固话' })
  business_person_fixed_phone: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '业务联系人邮箱' })
  business_person_email: string;

  @Column({ type: 'int', nullable: true, comment: '专业技术人员数量' })
  tech_people_num: number;

  @Column({ type: 'int', nullable: true, comment: '评价方向人员数量' })
  comment_people_num: number;

  @Column({ type: 'int', nullable: true, comment: '检测方向人员数量' })
  detection_people_num: number;

  @Column({ type: 'int', nullable: true, comment: '放射方向人员数量' })
  radiate_people_num: number;

  @Column({ type: 'int', nullable: true, comment: '职业卫生专家数量' })
  zywjzj_people_num: number;

  @Column({ type: 'int', nullable: true, comment: '国家级专家数量' })
  country_people_num: number;

  @Column({ type: 'int', nullable: true, comment: '省级专家数量' })
  province_people_num: number;

  @Column({ type: 'int', nullable: true, comment: '市级专家数量' })
  city_people_num: number;

  @Column({ type: 'int', nullable: true, comment: '中高级职称数量' })
  zhonggao_people_num: number;

  @Column({ type: 'int', nullable: true, comment: '高级职称数量' })
  zhenggao_people_num: number;

  @Column({ type: 'int', nullable: true, comment: '副高级职称数量' })
  fugao_people_num: number;

  @Column({ type: 'int', nullable: true, comment: '中级职称数量' })
  zhong_people_num: number;

  @Column({ type: 'tinyint', nullable: true, comment: '删除标志 0-存在 1删除' })
  del_flag: number;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '数据创建人' })
  create_by: string;

  @Column({ type: 'datetime', comment: '数据创建日期' })
  create_time: Date;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '数据修改人' })
  update_by: string;

  @Column({ type: 'datetime', comment: '数据修改时间-数据修改时不可为空' })
  update_time: Date;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '备注' })
  remark: string;

  @Column({ type: 'varchar', length: 3, nullable: true, comment: '机构类型' })
  org_type: string;

  @Column({ type: 'datetime', nullable: true, comment: '数据上传时间' })
  report_time: Date;
}
