import { AssessmentModel } from './assessment-model.entity';
import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';

// 考核指标表 mysql
// 该表用于存储考核指标的相关信息
@Entity('basic_conditions')
export class BasicConditions {
  @PrimaryGeneratedColumn({ name: 'id', type: 'int', comment: '自增ID' })
  id: number;

  @Column({ comment: '评估模型id', type: 'int', default: null, nullable: true })
  assessment_model_id: number;

  @Column({ comment: '条件名称', type: 'varchar' })
  name: string;

  @ManyToOne(() => AssessmentModel, assessmentModel => assessmentModel.basic_conditions)
  @JoinColumn({ name: 'assessment_model_id' })
  assessmentModel: AssessmentModel

}
