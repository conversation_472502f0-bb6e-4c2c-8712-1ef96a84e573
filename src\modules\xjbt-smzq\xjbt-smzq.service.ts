import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateAppointmentDto } from './dto/create-appointment.dto';
import { CreateRehabGuideApplicationDto } from './dto/create-rehab-guide-application.dto';
import { Appointment } from './mysql/appointment.entity';
import { RehabGuideApplication } from './mysql/rehab-guide-application.entity';

@Injectable()
export class XjbtSmzqService {
  constructor(
    @InjectRepository(Appointment, 'mysqlConnectionXjbt')
    private appointmentRepository: Repository<Appointment>,
    @InjectRepository(RehabGuideApplication, 'mysqlConnectionXjbt')
    private rehabGuideApplicationRepository: Repository<RehabGuideApplication>,
  ) {}

  async createAppointment(createAppointmentDto: CreateAppointmentDto) {
    const appointment = this.appointmentRepository.create({
      ...createAppointmentDto,
      status: 0, // 待审核
      appt_date: new Date(createAppointmentDto.appt_date),
    });
    return await this.appointmentRepository.save(appointment);
  }

  async createRehabGuideApplication(
    createRehabGuideApplicationDto: CreateRehabGuideApplicationDto,
  ) {
    const rehabGuideApplication = this.rehabGuideApplicationRepository.create({
      ...createRehabGuideApplicationDto,
      status: 0, // 待处理
      create_date: new Date(createRehabGuideApplicationDto.create_date),
    });
    return await this.rehabGuideApplicationRepository.save(
      rehabGuideApplication,
    );
  }
}
