import { Entity, Column, ObjectIdColumn } from 'typeorm';
import {
  IsString,
  IsArray,
  IsBoolean,
  IsNumber,
  IsDate,
  IsNotEmpty,
} from 'class-validator';
import { nanoid } from 'nanoid';

@Entity('serviceOrg')
export class ServiceOrg {
  @ObjectIdColumn()
  _id: string;

  @Column({ comment: '机构名称' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @Column({ comment: '统一社会信用代码' })
  @IsNotEmpty()
  @IsString()
  organization: string;

  @Column('array', { default: [] })
  @IsArray()
  regAddr: string[];

  @Column({ comment: '注册详细地址' })
  @IsString()
  address: string;

  @Column({ comment: '法人代表' })
  @IsString()
  corp: string;

  @Column('array', { comment: '管理人员id集合', default: [] })
  @IsArray()
  managers: string[];

  @Column('array', { comment: '管理人员id与技术服务区域集合' })
  @IsArray()
  managersAndArea: string[];

  @Column({ comment: '业务范围' })
  @IsString()
  lineOfBusiness: string;

  @Column({ comment: '组成形式' })
  @IsString()
  regType: string;

  @Column({ comment: '创建/更改时间' })
  @IsDate()
  ctime: Date;

  @Column({ comment: '营业执照' })
  @IsString()
  img: string;

  @Column('array', { comment: '资质证书id集合' })
  @IsArray()
  qualifies: string[];

  @Column({ comment: '审核结果' })
  @IsString()
  message: string;

  @Column({ comment: '状态' }) // [ 0, 1, 2, 3, 4, 5 ], // 0未注册；1已上传营业执照；2已上传资质证书；3审核通过；4审核不通过；5注销
  @IsNumber()
  status: number;

  @Column({ comment: '黑科技' })
  @IsBoolean()
  blackTechnology: boolean;

  @Column({ comment: '禁用修改审批 - true 禁用； false 不禁用' })
  @IsBoolean()
  modifyApproval: boolean;

  @Column({ comment: '启用修改记录功能， true 启用，false 禁用' })
  @IsBoolean()
  modifyRecord: boolean;

  @Column({ comment: '出入库严格模式， true 启用，false 禁用' })
  @IsBoolean()
  rigorousInventory: boolean;

  @Column({ comment: '启用合同评审严格模式， true 启用，false 禁用' })
  @IsBoolean()
  comtractApproval: boolean;

  @Column({ comment: '座机' })
  @IsString()
  landline: string;

  @Column({ comment: '超级管理员，第一个用户，也是绑定了统一社会信用代码的人' })
  @IsString()
  administrator: string;

  @Column({ comment: '哪个端创建的' })
  @IsString()
  sourse: string;

  constructor() {
    this._id = this._id || nanoid();
    this.name = this.name || '';
    this.organization = this.organization || '';
    this.regAddr = this.regAddr || [];
    this.address = this.address || '';
    this.corp = this.corp || '';
    this.managers = this.managers || [];
    this.managersAndArea = this.managersAndArea || [];
    this.lineOfBusiness = this.lineOfBusiness || '';
    this.regType = this.regType || '';
    this.status = this.status || 0;
  }
}
