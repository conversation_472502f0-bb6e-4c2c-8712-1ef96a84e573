import { IsString, IsEnum, IsDate, IsOptional, IsBoolean, IsArray } from 'class-validator';

/**
 * 创建鉴定记录DTO
 */
export class CreateDeterminationRecordDto {
  @IsEnum(['1', '2'], { message: '鉴定类别必须是 1(首次鉴定) 或 2(再鉴定)' })
  determinationCategory: string;

  @IsString()
  employerName: string;

  @IsOptional()
  @IsString()
  employerCreditCode?: string;

  @IsOptional()
  @IsString()
  laborEmployerName?: string;

  @IsOptional()
  @IsString()
  laborEmployerCreditCode?: string;

  @IsOptional()
  @IsString()
  applicationReason?: string;

  @IsOptional()
  @IsString()
  determinationBasis?: string;

  @IsBoolean()
  hasOccupationalDisease: boolean;

  @IsOptional()
  @IsString()
  determinationConclusionDescription?: string;

  @IsOptional()
  @IsString()
  determinationConclusion?: string;

  @IsOptional()
  @IsArray()
  occupationalDisease?: { name: string; code: string }[] = [];

  @IsOptional()
  @IsString()
  determinationCommittee?: string;

  @IsDate()
  determinationDate: Date;

  @IsString()
  workerName: string;

  @IsOptional()
  @IsString()
  phone?: string;

  @IsOptional()
  @IsEnum(['1', '2'], { message: '性别必须是 1(男) 或 2(女)' })
  gender?: string;

  @IsString()
  idNumber: string;

  @IsOptional()
  @IsString()
  diagnosisNumber?: string;

  @IsString()
  determinationNumber: string;

  @IsOptional()
  @IsString()
  firstDeterminationNumber?: string;

  @IsOptional()
  @IsArray()
  fileList?: { name: string; url: string }[] = [];

  @IsOptional()
  @IsDate()
  createdAt?: Date = new Date();

  @IsOptional()
  @IsDate()
  updatedAt?: Date = new Date();
}
