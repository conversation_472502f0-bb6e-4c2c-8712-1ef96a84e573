import { Body, Controller, Get, Param, Post, Query } from '@nestjs/common';
import { MODULE_NAME } from './constants';
import { UnifiedAccountService } from './unified-account.service';
import { success as wrapperResponseSuccess } from 'src/utils';
import { END_POINT_TYPE } from 'src/constants/endPoint';

@Controller(MODULE_NAME)
export class UnifiedAccountController {
  constructor(private readonly unifiedAccountService: UnifiedAccountService) {}

  @Post('create')
  async createAccount(
    @Body() data: any,
    @Query('endPoint') endPoint: END_POINT_TYPE,
  ) {
    const orgAccount = await this.unifiedAccountService.upsertSubAccounts(
      endPoint,
      data,
    );
    return wrapperResponseSuccess(
      {
        endPoint,
        orgAccount,
      },
      '成功',
    );
  }

  @Get('user/:endPoint/:id')
  async getAccountById(
    @Param('endPoint') endPoint: END_POINT_TYPE,
    @Param('id') id: string,
  ) {
    const userAccount = await this.unifiedAccountService.getAccountById(
      endPoint,
      id,
    );
    return wrapperResponseSuccess(
      {
        userAccount,
      },
      '成功',
    );
  }

  // @Get('org/:endPoint/:id')
  // async getOrgAccountById(
  //   @Param('endPoint') endPoint: END_POINT_TYPE,
  //   @Param('id') id: string,
  // ) {
  //   const orgAccount = await this.unifiedAccountService.getOrgAccountById(
  //     endPoint,
  //     id,
  //   );
  //   return wrapperResponseSuccess(
  //     {
  //       orgAccount,
  //     },
  //     '成功',
  //   );
  // }
}
