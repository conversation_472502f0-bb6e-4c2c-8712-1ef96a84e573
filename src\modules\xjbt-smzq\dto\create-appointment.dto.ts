import {
  IsString,
  <PERSON>N<PERSON>ber,
  IsOptional,
  IsDateString,
  Length,
  IsNotEmpty,
} from 'class-validator';

export class CreateAppointmentDto {
  @IsString()
  @Length(18, 18)
  @IsNotEmpty()
  id_card: string;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsNumber()
  @IsNotEmpty()
  inst_id: number;

  @IsNumber()
  @IsOptional()
  doctor_id?: number;

  @IsDateString()
  @IsNotEmpty()
  appt_date: string;

  @IsString()
  @IsOptional()
  @Length(7, 7)
  disease_category?: string;

  @IsString()
  @IsOptional()
  service_type?: string;

  @IsString()
  @IsOptional()
  requirement?: string;
}
