import { Controller, Get, Param } from '@nestjs/common';
import { HealthCheckAppointmentService } from './health-check-appointment.service';
import { wrapperResponse } from 'src/utils';

@Controller('health-check-appointment')
export class HealthCheckAppointmentController {
  constructor(
    private readonly healthCheckAppointmentService: HealthCheckAppointmentService,
  ) {}

  @Get('/:id') // http://127.0.0.1:3000/health-check-appointment/Vb4-NPjrz
  async findOne(@Param('id') id: string) {
    return wrapperResponse(
      this.healthCheckAppointmentService.findOne({ _id: id }),
      '获取mongodb中预约单的单个数据',
    );
  }
}
