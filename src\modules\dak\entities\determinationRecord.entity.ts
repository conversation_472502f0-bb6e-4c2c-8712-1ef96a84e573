import { Entity, Column, ObjectIdColumn, BeforeInsert } from 'typeorm';
import * as shortid from 'shortid';

/**
 * 鉴定记录实体
 * 包含鉴定类别（首次鉴定、再鉴定）、用人单位名称、用人单位统一社会信用代码、用工单位名称、用工单位统一社会信用代码、
 * 申请鉴定主要理由、鉴定依据、鉴定结论、诊断鉴定委员会、鉴定日期、劳动者名称、身份证、对应的诊断编号
 */
@Entity('determinationRecords')
export class DeterminationRecord {
  @ObjectIdColumn()
  _id: string; // 主键，保持string类型

  @Column()
  // 鉴定类别：1首次鉴定 2再鉴定
  determinationCategory: string;

  @Column()
  // 用人单位名称
  employerName: string;

  @Column({ nullable: true })
  // 用人单位统一社会信用代码
  employerCreditCode: string;

  @Column({ nullable: true })
  // 用工单位名称
  laborEmployerName: string;

  @Column({ nullable: true })
  // 用工单位统一社会信用代码
  laborEmployerCreditCode: string;

  @Column({ nullable: true })
  // 申请鉴定主要理由
  applicationReason: string;

  @Column({ nullable: true })
  // 鉴定依据
  determinationBasis: string;

  @Column()
  // 鉴定结论，false代表不是职业病，true代表是职业病
  hasOccupationalDisease: boolean;

  @Column({ nullable: true })
  // 鉴定结论描述
  determinationConclusionDescription: string;

  @Column({ nullable: true })
  // 职业病数组
  occupationalDisease: { name: string; code: string }[] = [];

  @Column({ nullable: true })
  // 诊断鉴定委员会
  determinationCommittee: string;

  @Column()
  // 鉴定日期
  determinationDate: Date;

  @Column()
  // 劳动者名称
  workerName: string;

  @Column({ nullable: true })
  // 手机号
  phone: string;

  @Column({ nullable: true })
  // 性别 1男 2女
  gender: string;

  @Column()
  // 身份证号码
  idNumber: string;

  @Column({ nullable: true })
  // 对应的诊断编号
  diagnosisNumber: string;

  @Column({ unique: true })
  // 鉴定编号
  determinationNumber: string;

  @Column({ nullable: true })
  // (再鉴定对应的)首次鉴定编号
  firstDeterminationNumber: string;

  @Column({ nullable: true })
  // 文件列表
  fileList: { name: string; url: string }[] = [];

  @Column({ default: () => new Date() })
  // 创建时间
  createdAt: Date;

  @Column({ default: () => new Date() })
  // 更新时间
  updatedAt: Date;

  @BeforeInsert()
  setDefaultId() {
    if (!this._id) {
      this._id = shortid.generate();
    }
  }
}
