import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ExpertClassifier } from './mysql/expert_classifier.entity';
// 专家分类管理
@Injectable()
export class ExpertClassifierService {
  constructor(
    @InjectRepository(ExpertClassifier, 'mysqlConnectionIservice')
    private readonly expertClassifierRepository: Repository<ExpertClassifier>,
  ) {}

  findAll(): Promise<ExpertClassifier[]> {
    return this.expertClassifierRepository.find({
      where: { enable: true },
      order: { id: 'ASC' },
    });
  }

  findOne(id: number): Promise<ExpertClassifier> {
    return this.expertClassifierRepository.findOne({ where: { id } });
  }

  create(createDto: ExpertClassifier): Promise<ExpertClassifier> {
    const expertClassifier = this.expertClassifierRepository.create(createDto);
    return this.expertClassifierRepository.save(expertClassifier);
  }

  async update(
    id: number,
    updateDto: ExpertClassifier,
  ): Promise<ExpertClassifier> {
    const res = await this.expertClassifierRepository.update(id, updateDto);
    if (res.affected > 0) {
      return this.expertClassifierRepository.findOne({ where: { id } });
    }
    throw new Error('更新失败, 请检查id是否正确');
  }

  // 假删
  async remove(id: number): Promise<void> {
    await this.expertClassifierRepository.update(id, { enable: false });
  }
}
