import { Entity, Column, ObjectIdColumn } from 'typeorm';
@Entity('systemconfigs')
export class SystemConfigs {
  @ObjectIdColumn()
  _id: string;

  @Column({ default: new Date() })
  date: Date;

  @Column({ default: '前端开发俱乐部' })
  siteName: string;

  @Column({ default: '' })
  ogTitle: string;

  @Column({ default: 'https://www.html-js.cn' })
  siteDomain: string;

  @Column({ default: '前端开发' })
  siteDiscription: string;

  @Column()
  siteKeywords: string;

  @Column()
  siteAltKeywords: string;

  @Column()
  siteEmailServer: string;

  @Column()
  siteEmail: string;

  @Column()
  siteEmailPwd: string;

  @Column({ default: '' })
  registrationNo: string;

  @Column()
  mongodbInstallPath: string;

  @Column()
  databackForderPath: string;

  @Column({ type: 'boolean', default: false })
  showImgCode: boolean;

  @Column({ type: 'boolean', default: false })
  bakDatabyTime: boolean;

  @Column({ default: '1' })
  bakDataRate: string;

  @Column({ default: '' })
  statisticalCode: string;

  @Column({ comment: '预警配置信息', default: [] })
  warningConfig: [
    {
      level: number;
      enableUploadFiles: boolean;
      releaseWarningBySelf: boolean;
    },
  ];
}
