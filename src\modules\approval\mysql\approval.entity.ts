import { Entity, Column, PrimaryGeneratedColumn, OneToMany } from 'typeorm';
import { ApprovalApprovers } from './approval-approvers.entity';
import { ApprovalTodo } from './approval-todo.entity';

@Entity('approval')
export class Approval {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', nullable: true })
  title: string;

  @Column({ type: 'varchar', nullable: true })
  description: string;

  @Column({ type: 'varchar', nullable: true })
  link: string;

  @Column({ type: 'int', nullable: true })
  status: number;

  @Column({ type: 'datetime', nullable: true })
  start_at: Date;

  @Column({ type: 'datetime', nullable: true })
  end_at: Date;

  @Column({ type: 'varchar', nullable: true })
  created_by: string;

  @Column({ type: 'datetime', nullable: true })
  created_at: Date;

  @Column({ type: 'datetime', nullable: true })
  updated_at: Date;

  // @Column({ type: 'varchar', nullable: true, name: 'related_id' })
  // relatedId: string;

  @OneToMany(() => ApprovalApprovers, approvers => approvers.approval)
  approvers: ApprovalApprovers[];

  @OneToMany(() => ApprovalTodo, todo => todo.approval)
  todos: ApprovalTodo[];
}