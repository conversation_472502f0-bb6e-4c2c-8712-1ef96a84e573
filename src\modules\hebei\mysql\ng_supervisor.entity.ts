import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

// 申报审核记录
@Entity('ng_supervisor')
export class ngSupervisor {
  @PrimaryGeneratedColumn({ type: 'int', comment: 'monitor_id' })
  id: number;
  
  @Column({type:'varchar',comment:'市'})
  city:string | null;

  @Column({type:'varchar',comment:'单位名称'})
  name:string | null;

  @Column({ type: 'varchar', nullable: true, comment: '区域名称' })
  district_name: string | null;

  @Column({ type: 'bigint', nullable: true, comment: '区域编码' })
  district_id: number | null;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '联系人电话' })
  phone: string | null;

  @Column({ type: 'tinyint', nullable: true, comment: '1省2地市3区县4乡镇' })
  level: number | null;

  @Column({ type: 'tinyint', nullable: true, comment: '是否开发区0否1是' })
  is_dev_district: number | null;

  @Column({ type: 'varchar',length: 255, nullable: true, comment: '审核回执落款' })
  sign_name: string | null;
}
