import {
  Entity,
  Column,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
// 角色
@Entity('t_role')
export class TRole {
  @PrimaryColumn({ type: 'varchar', length: 64, comment: 'ID' })
  id: string;

  @Column({ type: 'varchar', length: 50, comment: '名称' })
  name: string;

  @Column({ type: 'varchar', length: 50, comment: '编码' })
  code: string;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '备注' })
  remark: string;

  @Column({
    type: 'varchar',
    length: 3000,
    nullable: true,
    comment: '权限列表',
  })
  authority: string;

  @Column({ type: 'tinyint', default: 1, comment: '1激活 0失活' })
  status: number;

  @Column({ type: 'tinyint', default: 0, comment: '是否删除' })
  is_deleted: number;

  @CreateDateColumn({ type: 'timestamp', comment: '创建时间' })
  create_at: Date;

  @Column({ type: 'varchar', length: 256, nullable: true })
  create_by: string;

  @UpdateDateColumn({ type: 'timestamp', comment: '更新时间' })
  update_at: Date;

  @Column({ type: 'varchar', length: 256, nullable: true })
  update_by: string;
}
