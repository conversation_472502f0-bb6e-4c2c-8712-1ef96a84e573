import { IsBoolean, IsEnum, IsString } from 'class-validator';
import { nanoid } from 'nanoid';
import {
  Entity,
  Column,
  ObjectIdColumn,
  Index,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

// 企业用人单位 信息
@Entity('adminusers')
export class AdminOrgsUser {
  @ObjectIdColumn()
  _id: string;

  @Column({ comment: '用户名' })
  @Index()
  userName: string;

  @Column({ comment: '姓名' })
  @Index()
  name: string;

  @Column({ comment: '联系方式' })
  @Index()
  phoneNum: string;

  @Column({ default: '86' })
  @IsString()
  countryCode: string;

  @Column({ default: true })
  @IsBoolean()
  enable: boolean; // 账号是否受限

  @Column({ default: '1' })
  @IsEnum(['0', '1'])
  state: string; // 账号状态 1正常，0删除

  @CreateDateColumn({ default: () => new Date() })
  createTime: Date;

  @UpdateDateColumn({ default: () => new Date() })
  updateTime: Date;

  constructor() {
    this._id = this._id || nanoid();
    this.enable = this.enable === false ? false : true;
    this.countryCode = '86';
    this.state = '1';
  }
}
