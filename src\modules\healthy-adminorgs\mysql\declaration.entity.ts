import { format } from 'date-fns';
import {
  AfterLoad,
  Column,
  CreateDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { DeclarationBasicCondition } from './declaration_basic_condition.entity';
import { DeclarationIndicator } from './declaration_indicator.entity';
import { Attachment } from './attachment.entity';

@Entity('declaration')
export class Declaration {
  @PrimaryGeneratedColumn({ name: 'id', type: 'int', comment: '自增ID' })
  id: number;

  @Column({ comment: '企业id', type: 'varchar' })
  enterprise_id: string;

  @Column({ comment: '评估模型id', type: 'int' })
  assessment_model_id: number;

  @Column({ comment: '企业名称', type: 'varchar' })
  unit_name: string;

  @Column({ comment: '年度', type: 'varchar' })
  year: string;

  @Column({
    type: 'enum',
    enum: ['0', '1'],
    comment: '自评状态：0 不通过 1通过',
  })
  self_assessment_status: string;

  @CreateDateColumn({ comment: '自评时间', type: 'datetime' })
  self_assessment_time: Date;

  @Column({
    type: 'enum',
    enum: ['0', '1', '2', '3', '4', '5'],
    comment:
      '申报状态：0 待初审 1审核需修改 2初审不通过 3待审核 4审核不通过 5审核通过',
  })
  declaration_status: string;

  @CreateDateColumn({ comment: '申报时间', type: 'datetime' })
  declaration_time: Date;

  @CreateDateColumn({ comment: '初审时间', type: 'datetime' })
  city_review_time: Date;

  @Column({ comment: '分数', type: 'int' })
  score: number;

  @Column({ comment: '师市分数', type: 'int' })
  city_score: number;

  @Column({
    type: 'enum',
    enum: ['0', '1'],
    comment: '师市级评价状态 0 不符合 1 符合',
  })
  city_assessment_status: string;

  @Column({ comment: '兵团评分', type: 'int' })
  province_score: number;

  @Column({
    type: 'enum',
    enum: ['0', '1'],
    comment: '兵团级评审状态 0 不符合 1 符合',
  })
  province_assessment_status: string;

  self_assessment_time_format: string;

  declaration_time_format: string;

  city_review_time_format: string;

  @Column({
    type: 'enum',
    enum: ['0', '1'],
    comment: '是否提交到兵团 0 未提交 1 已提交',
  })
  is_submit: string;

  @Column({ comment: '审核不通过原因', type: 'text' })
  reason: string;

  @AfterLoad()
  formatDate() {
    if (this.self_assessment_time) {
      this.self_assessment_time_format = format(
        this.self_assessment_time,
        'yyyy-MM-dd HH:mm:ss',
      );
    }

    if (this.declaration_time) {
      this.declaration_time_format = format(
        this.declaration_time,
        'yyyy-MM-dd HH:mm:ss',
      );
    }

    if (this.city_review_time) {
      this.city_review_time_format = format(
        this.city_review_time,
        'yyyy-MM-dd HH:mm:ss',
      );
    }
  }

  @OneToMany(
    () => DeclarationBasicCondition,
    (declarationBasicCondition) => declarationBasicCondition.declaration,
  )
  declaration_basic_condition: DeclarationBasicCondition[];

  declaration_indicator: DeclarationIndicator[];

  @OneToMany(() => Attachment, (attachment) => attachment.declaration)
  attachment: Attachment[];

  @Column({ comment: '社会统一信用代码', type: 'varchar' })
  code: string;

  @Column({ comment: '企业规模', type: 'varchar' })
  companyScale: string;

  @Column({ comment: '联系人', type: 'varchar' })
  contract: string;

  @Column({ comment: '联系电话', type: 'varchar' })
  phoneNum: string;

  @Column({ comment: '单位地址', type: 'varchar' })
  regAdd: string;

  @Column({ comment: '单位类型', type: 'varchar' })
  regType: string;
}
