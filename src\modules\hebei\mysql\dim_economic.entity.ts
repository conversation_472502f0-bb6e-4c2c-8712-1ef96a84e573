import { Entity, Column, PrimaryColumn } from 'typeorm';

@Entity('dim_economic')
export class dimEconomic {
  @Column({ type: 'int', nullable: true, comment: 'economic_id' })
  id: number;

  @Column({ type: 'varchar', length: 60, nullable: true, comment: '经济类型' })
  economic_type: string;

  @Column({ type: 'varchar', length: 20, nullable: true, comment: '层级关系' })
  level: string;

  @Column({ type: 'int', unsigned: true, nullable: true, comment: 'economic_id_GBT' })
  id_GBT: number;

  @Column({ type: 'varchar', length: 50, nullable: true, comment: 'GB/T12402-2000国家标准经济类型' })
  economic_type_GBT: string;

  @PrimaryColumn({ type: 'int', comment: '主键' })
  key: number;
}
