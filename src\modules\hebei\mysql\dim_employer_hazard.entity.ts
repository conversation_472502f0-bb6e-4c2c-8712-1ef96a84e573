import { Entity, Column } from 'typeorm';

// 用人单位的危害因素
@Entity('dim_employer_hazard')
export class dimEmployerHazard {
  @Column({
    type: 'int',
    // nullable: true,  // 📌
    comment: '危害因素代码',
    primary: true,
  })
  hazard_code: number | null;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '危害因素名称',
  })
  hazard_name: string | null;

  @Column({
    type: 'tinyint',
    nullable: true,
    comment: '危害因素类别1粉尘2化学3物理4放射5生物6其他',
  })
  hazzrd_type: number | null;

  @Column({
    type: 'varchar',
    length: 160,
    nullable: true,
    comment: '危害因素CS号',
  })
  hazzrd_cs: string | null;

  @Column({ type: 'boolean', nullable: true, comment: '是否监管要求' })
  is_required: boolean | null;
}
