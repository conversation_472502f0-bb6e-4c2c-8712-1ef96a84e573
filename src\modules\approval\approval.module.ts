import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApprovalController } from './approval.controller';
import { ApprovalService } from './approval.service';
import { Approval } from './mysql/approval.entity';
import { ApprovalApprovers } from './mysql/approval-approvers.entity';
import { ApprovalTodo } from './mysql/approval-todo.entity';
import { DATA_SOURCE_NAME } from './constants';
import { UserContextMiddleware } from 'src/common/middlewares/user-context.middleware';
import { TodoModule } from '../todo/todo.module';

@Module({
  imports: [
    TypeOrmModule.forFeature(
      [Approval, ApprovalApprovers, ApprovalTodo],
      DATA_SOURCE_NAME,
    ),
    TodoModule,
  ],
  controllers: [ApprovalController],
  providers: [ApprovalService],
  exports: [ApprovalService],
})
export class ApprovalModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(UserContextMiddleware)
      .forRoutes(
        { path: 'v*/approval', method: RequestMethod.ALL },
        { path: 'v*/approval/*', method: RequestMethod.ALL },
      );
  }
}
