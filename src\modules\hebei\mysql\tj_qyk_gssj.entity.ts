import {
  Entity,
  Column,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('tj_qyk_gssj')
export class TjQykGssj {
  @PrimaryColumn({ type: 'bigint', comment: 'id' })
  id: number;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '所属区划id',
  })
  district_id: string;

  @Column({ type: 'varchar', length: 128, nullable: true, comment: '单位名称' })
  name: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '统一社会信用代码',
  })
  unified_code: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '地址' })
  address: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '法人' })
  fr: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '行业类别代码',
  })
  hylbdm: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '行业类别' })
  hylb: string;

  @Column({
    type: 'varchar',
    length: 2,
    nullable: true,
    comment: '危害程度1-严重0-一般',
  })
  whcd: string;

  @Column({
    type: 'varchar',
    length: 2,
    nullable: true,
    comment: '申报类型0-未申报1-已申报',
  })
  sblx: string;

  @Column({
    type: 'varchar',
    length: 2,
    nullable: true,
    comment: '是否纳入专项治理0-否；1-是',
  })
  nrzxzl: string;

  @Column({
    type: 'varchar',
    length: 2,
    nullable: true,
    comment: '是否停产停业0-否；1-是',
  })
  tcty: string;

  @Column({
    type: 'varchar',
    length: 2,
    nullable: true,
    comment: '是否是帮扶企业0-否；1-是',
  })
  bf: string;

  @Column({
    type: 'varchar',
    length: 2,
    nullable: true,
    comment: '是否注销0-否；1-是',
  })
  zx: string;

  @Column({
    type: 'varchar',
    length: 2,
    nullable: true,
    comment: '数据来源0-系统导入1-自主添加',
  })
  sjly: string;

  @Column({
    type: 'varchar',
    length: 2,
    nullable: true,
    comment: '是否正常纳税0-否；1-是',
  })
  zcns: string;

  @Column({
    type: 'char',
    length: 1,
    nullable: true,
    comment: '删除标志（0代表存在 2代表删除）',
  })
  del_flag: string;

  @Column({ type: 'varchar', length: 64, nullable: true, comment: '创建者' })
  create_by: string;

  @CreateDateColumn({ comment: '创建时间' })
  create_time: Date;

  @Column({ type: 'varchar', length: 64, nullable: true, comment: '更新者' })
  update_by: string;

  @UpdateDateColumn({ comment: '更新时间' })
  update_time: Date;

  @Column({
    type: 'varchar',
    length: 2,
    nullable: true,
    comment: '是否存在危害因素0-否；1-是',
  })
  whys: string;

  @Column({
    type: 'varchar',
    length: 2,
    nullable: true,
    comment: '查无此企业0-否；1-是',
  })
  cw: string;
}
