import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm';

// 体检预约单
@Entity('appointment')
export class Appointment {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ comment: '我方对应的预约单id' })
  @Index()
  appointmentId: string;

  @Column({ comment: '用工单位id' })
  EnterpriseID: string;

  @Column({
    type: 'varchar',
    length: 18,
    comment: '用工单位统一社会信用代码',
  })
  EnterpriseCode: string;

  @Column({ comment: '用工单位名称' })
  EnterpriseName: string;

  @Column({ comment: '用工单位联系人' })
  contact: string;

  @Column({ comment: '用工单位联系人电话' })
  phoneNum: string;

  @Column({ comment: '体检机构id' })
  physicalExamOrgId: string;

  @Column({
    comment:
      '体检类型 01是上岗前 02是在岗 03是离岗时 04应急 05离岗后 06普通体检',
    enum: ['01', '02', '03', '04', '05', '06'],
  })
  checkType: string;

  @Column({ comment: '是否复查 0否 1是', default: '0', enum: ['0', '1'] })
  isReview: string;

  @Column({ comment: '预估/预约体检人数' })
  peopleNum: number;

  @Column({ comment: '体检开始时间' })
  startTime: Date;

  @Column({ comment: '体检结束时间' })
  endTime: Date;

  @Column({ comment: '体检项目id', nullable: true })
  healthcheckId: string;

  @Column({ comment: '实际体检人员名单', type: 'simple-array' })
  employeeIds: string[];

  @Column({
    comment:
      '预约单当前状态 1 提交申请 2 已确认体检日期 3 已修改体检日期 4 已确认名单 5 体检中 6 已完成 7 已取消',
    default: 1,
    enum: [1, 2, 3, 4, 5, 6, 7],
  })
  status: number;

  @Column({ comment: '预约单相关文件-合同委托书', nullable: true })
  files_authorization?: string;

  @Column({
    comment: '预约单相关文件-人单位基本信息表以及职业健康检查劳动者信息表',
    nullable: true,
  })
  files_enterpriseInfo?: string;

  @Column({ comment: '预约单相关文件-职业健康检查登记表', nullable: true })
  files_healthExamRecord?: string;

  @Column({ comment: '预约单创建时间' })
  createdAt: Date;

  @Column({ comment: '预约单最近的更新时间' })
  updatedAt: Date;
}
