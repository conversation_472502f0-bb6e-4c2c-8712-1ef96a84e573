import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';
@Entity({
  name: 'DP_ZYWSDP_ZYWHYSFHTJ', // 没有统计数据
  comment: '职业卫生大屏-职业危害因素防护统计',
})
export class DP_ZYWSDP_ZYWHYSFHTJ {
  @PrimaryGeneratedColumn()
  ID: number;

  @Column({ type: 'varchar2', length: 255, comment: '地区' })
  DQ: string;

  @Column({ type: 'varchar2', length: 255, comment: '年份' })
  NF: string;

  @Column({ type: 'varchar2', length: 255, comment: '类型' })
  LX: string;

  @Column({ type: 'varchar2', length: 255, comment: '有' })
  Y: string;

  @Column({ type: 'varchar2', length: 255, comment: '部分有' })
  BFY: string;

  @Column({ type: 'varchar2', length: 255, comment: '无' })
  W: string;

  @Column({ type: 'varchar2', length: 255, comment: '有效' })
  YX: string;

  @Column({ type: 'varchar2', length: 255, comment: '无效' })
  WX: string;

  @Column({ type: 'varchar2', length: 255, comment: '部分有效' })
  BFYX: string;
}
