import { MongoRepository } from 'typeorm';
import { plainToClass } from 'class-transformer';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { AccountService } from '../account-service.interface';
import { PhysicalExamOrg } from './physical-exam-org.account.entity';
import { PhysicalExamUser } from './physical-exam-user.account.entity';
import {
  CreatePhysicalExamAccountData,
  CreatePhysicalExamUserData,
  CreatePhysicalExamOrgData,
  QueryPhysicalExamOrgData,
} from '../../types';

@Injectable()
export class PhysicalExamAccountService
  implements AccountService<CreatePhysicalExamAccountData, PhysicalExamUser>
{
  constructor(
    @InjectRepository(PhysicalExamOrg, 'mongodbConnection')
    private readonly physicalExamOrgRepository: MongoRepository<PhysicalExamOrg>,
    @InjectRepository(PhysicalExamUser, 'mongodbConnection')
    private readonly physicalExamUserRepository: MongoRepository<PhysicalExamUser>,

    private readonly configService: ConfigService,
  ) {}

  async upsertSubAccounts(
    data: CreatePhysicalExamAccountData,
  ): Promise<PhysicalExamUser> {
    const org = await this.upsertOrgAccount(data.org);
    const user = await this.upsertUserAccount({
      org: org.name,
      org_id: org._id,
      ...data.user,
    });
    await this.relationOrgManager(org._id, user._id);
    return user;
  }

  async upsertOrgAccount(
    data: CreatePhysicalExamOrgData,
  ): Promise<PhysicalExamOrg> {
    let org = await this.getOrgAccountByQuery({
      organization: data.organization,
    });
    if (!org) {
      org = await this.createOrgAccount(data);
    } else {
      org = await this.updateOrgAccount(org._id, data);
    }
    return org;
  }

  async upsertUserAccount(
    data: CreatePhysicalExamUserData,
  ): Promise<PhysicalExamUser> {
    let user = await this.getUserAccountByQuery({
      phoneNum: data.phoneNum,
    });
    if (!user) {
      user = await this.createUserAccount(data);
    } else {
      user = await this.updateUserAccount(user._id, data);
    }
    return user;
  }

  async getAccount(id: string): Promise<PhysicalExamUser> {
    const user = await this.physicalExamUserRepository.findOne({
      where: { _id: id },
    });
    return user;
  }

  async getOrgAccountByQuery(query: QueryPhysicalExamOrgData) {
    const org = await this.physicalExamOrgRepository.findOne({
      where: query,
    });
    return org;
  }

  async getUserAccountByQuery(query: { phoneNum: string }) {
    const user = await this.physicalExamUserRepository.findOne({
      where: query,
    });
    return user;
  }

  async getUserAccountById(id: string): Promise<PhysicalExamUser> {
    const user = await this.physicalExamUserRepository.findOne({
      where: { _id: id },
    });
    return user;
  }

  async getAccountByQuery(
    query: QueryPhysicalExamOrgData,
  ): Promise<PhysicalExamUser> {
    const org = await this.getOrgAccountByQuery(query);
    if (!org?.managers?.[0]) {
      return null;
    }
    const user = await this.getUserAccountById(org.managers[0]);
    return user;
  }

  async createUserAccount(
    data: CreatePhysicalExamUserData,
  ): Promise<PhysicalExamUser> {
    const user = await this.physicalExamUserRepository.save(
      plainToClass(PhysicalExamUser, {
        group: this.configService.get('groupID').physicalExamGroupID,
        ...data,
      }),
    );
    return user;
  }

  async createOrgAccount(
    data: CreatePhysicalExamOrgData,
  ): Promise<PhysicalExamOrg> {
    const org = await this.physicalExamOrgRepository.save(
      plainToClass(PhysicalExamOrg, {
        ...data,
      }),
    );
    return org;
  }

  async getOrgAccount(id: string): Promise<PhysicalExamOrg> {
    const org = await this.physicalExamOrgRepository.findOne({
      where: { _id: id },
    });
    return org;
  }

  async relationOrgManager(orgId: string, userId: string): Promise<void> {
    await this.physicalExamOrgRepository.updateOne(
      {
        _id: orgId,
      },
      {
        $set: {
          managers: [userId],
        },
      },
    );
  }

  async updateOrgAccount(
    orgId: string,
    data: Partial<PhysicalExamOrg>,
  ): Promise<PhysicalExamOrg> {
    const result = await this.physicalExamOrgRepository.updateOne(
      { _id: orgId },
      { $set: data },
      // { upsert: false }  // 如果不存在则不创建新记录
    );
    return plainToClass(PhysicalExamOrg, result);
  }

  async updateUserAccount(
    userId: string,
    data: Partial<PhysicalExamUser>,
  ): Promise<PhysicalExamUser> {
    const result = await this.physicalExamUserRepository.updateOne(
      { _id: userId },
      { $set: data },
      // { upsert: false }  // 如果不存在则不创建新记录
    );
    return plainToClass(PhysicalExamUser, result);
  }
  
  async validateAccount(/*id: string*/): Promise<boolean> {
    return Promise.resolve(true);
  }
}
