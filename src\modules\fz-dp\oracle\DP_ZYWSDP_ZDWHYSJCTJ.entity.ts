import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';
// 职业卫生大屏-重点危害因素监测统计
@Entity({
  name: 'DP_ZYWSDP_ZDWHYSJCTJ',
  comment: '职业卫生大屏-重点危害因素监测统计',
})
export class DP_ZYWSDP_ZDWHYSJCTJ {
  @PrimaryGeneratedColumn()
  ID: number;

  @Column({ type: 'varchar2', length: 255, comment: '类型' })
  LX: string;

  @Column({ type: 'varchar2', length: 255, comment: '用人单位数' })
  YRDWS: string;

  @Column({ type: 'varchar2', length: 255, comment: '岗位/工种超标率（%）' })
  GWGZCBL: string;

  @Column({ type: 'varchar2', length: 255, comment: '检测工作场所超标率（%）' })
  JCGZCSCBL: string;
}
