import { IsString, IsOptional, IsArray, IsDate } from 'class-validator';

class AllergyDto {
  @IsString()
  allergySource: string;

  @IsString()
  allergySymptoms: string;

  @IsString()
  allergyDate: string;

  @IsString()
  treatment: string;

  @IsString()
  effectRecord: string;
}

class PastHistoryDto {
  @IsString()
  diseaseName: string;

  @IsString()
  diagnosisDate: string;

  @IsString()
  institutionName: string;

  @IsString()
  treatmentProcess: string;

  @IsString()
  outcomeCode: string;
}

class FamilyHistoryDto {
  @IsString()
  familyMember: string;

  @IsString()
  diseaseName: string;

  @IsString()
  diagnosisDate: string;

  @IsString()
  institutionName: string;

  @IsString()
  treatmentProcess: string;
}

class EmergencyContactDto {
  @IsString()
  name: string;

  @IsString()
  relationship: string;

  @IsString()
  phoneNum: string;
}

/**
 * 创建员工基本信息DTO
 */
export class CreateEmployeeBasicInfoDto {
  @IsString()
  userId: string;

  @IsString()
  employeeId: string;

  @IsString()
  EnterpriseID: string;

  @IsString()
  name: string;

  @IsString()
  gender: string;

  @IsString()
  IDNum: string;

  @IsString()
  phoneNum: string;

  @IsOptional()
  @IsString()
  contactPhoneNum?: string;

  @IsOptional()
  @IsString()
  nativePlace?: string;

  @IsOptional()
  @IsString()
  nativePlaceAddress?: string;

  @IsOptional()
  @IsString()
  residencePlace?: string;

  @IsOptional()
  @IsString()
  residenceAddress?: string;

  @IsOptional()
  @IsString()
  nation?: string;

  @IsOptional()
  @IsString()
  maritalStatus?: string;

  @IsOptional()
  @IsString()
  education?: string;

  @IsOptional()
  @IsString()
  bloodType?: string;

  @IsOptional()
  @IsArray()
  allergy?: AllergyDto[];

  @IsOptional()
  @IsArray()
  pastHistory?: PastHistoryDto[];

  @IsOptional()
  @IsArray()
  familyHistory?: FamilyHistoryDto[];

  // 健康行为相关字段
  @IsOptional()
  @IsString()
  smokingHistory?: string;

  @IsOptional()
  @IsString()
  smokingAmount?: string;

  @IsOptional()
  @IsString()
  smokingYear?: string;

  @IsOptional()
  @IsString()
  smokingMonth?: string;

  @IsOptional()
  @IsString()
  drinkingHistory?: string;

  @IsOptional()
  @IsString()
  drinkingAmount?: string;

  @IsOptional()
  @IsString()
  drinkingYear?: string;

  // 生育史相关字段
  @IsOptional()
  @IsString()
  currentChildren?: string;

  @IsOptional()
  @IsString()
  abortion?: string;

  @IsOptional()
  @IsString()
  stillbirth?: string;

  @IsOptional()
  @IsString()
  premature?: string;

  @IsOptional()
  @IsString()
  abnormalFetus?: string;

  @IsOptional()
  @IsString()
  childrenHealth?: string;

  // 月经史相关字段
  @IsOptional()
  @IsString()
  menarcheAge?: string;

  @IsOptional()
  @IsString()
  menstruationDays?: string;

  @IsOptional()
  @IsString()
  menstruationCycle?: string;

  @IsOptional()
  @IsString()
  menopauseAge?: string;

  @IsOptional()
  @IsString()
  exerciseHabit?: string;

  @IsOptional()
  @IsArray()
  emergencyContact?: EmergencyContactDto[];

  @IsOptional()
  @IsDate()
  createdAt?: Date;

  @IsOptional()
  @IsDate()
  updatedAt?: Date;
} 