import { Entity, Column, PrimaryColumn } from 'typeorm';

@Entity('tj_health_check_jl')
export class TjHealthCheckJl {
  @PrimaryColumn({ type: 'varchar', length: 64, comment: 'id' })
  id: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '删除标志（0代表存在 2代表删除）',
  })
  del_flag: string;

  @Column({ type: 'varchar', length: 500, nullable: true, comment: '备注' })
  remark: string;

  @Column({
    type: 'varchar',
    length: 32,
    nullable: true,
    comment: '接害人员id',
  })
  reg_id: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '危害因素' })
  whys: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '体检结论' })
  tjjl: string;

  @Column({
    type: 'varchar',
    length: 1000,
    nullable: true,
    comment: '疾病或异常',
  })
  jbhyc: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '检测类型' })
  jclx: string;

  @Column({ type: 'datetime', comment: '报告时间' })
  report_time: Date;
}
