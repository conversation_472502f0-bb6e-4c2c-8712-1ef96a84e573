import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGenerated<PERSON><PERSON>umn,
  OneToOne,
  JoinColumn,
  OneToMany,
  ManyToOne,
} from 'typeorm';
import { ngAudit } from './ng_audit.entity';
import { ngRoster } from './ng_roster.entity';
import { NgEmployer } from './ng_employer.entity';

// 用人单位申报记录
@Entity('ng_declaration')
export class ngDeclaration {
  @PrimaryGeneratedColumn({ type: 'int', comment: 'declaration_id' })
  id: number;

  @Column({ type: 'tinyint', comment: '申报类型(1初次申报2变更申报3年度更新)' })
  declaration_type: number;

  @Column({ type: 'int', comment: '申报年份' })
  declaration_year: number;

  @Column({ type: 'datetime', nullable: true, comment: '申报日期' })
  declaration_date: Date;

  @Column({ type: 'varchar', length: 8, nullable: true, comment: '变更原因' })
  change_reason: string | null;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '变更原因备注（原因选其它时使用）' })
  change_reason_comment: string | null;

  @Column({ type: 'varchar', length: 100, comment: '用人单位名称' })
  employer_name: string;

  @Column({ type: 'varchar', length: 18, comment: '统一社会信用代码' })
  unified_code: string;

  @Column({ type: 'json', nullable: true, comment: '单位注册地址' })
  employer_address: object | null;

  @Column({ type: 'json', nullable: true, comment: '作业场所地址' })
  work_address: object | null;

  @Column({
    type: 'tinyint',
    nullable: true,
    comment: '企业规模(1大2中3小4微)',
  })
  employer_scale: number;

  @Column({ type: 'bigint', nullable: true, comment: '所属地区_id' })
  district_id: number | null;

  @Column({
    type: 'int',
    nullable: true,
    unsigned: true,
    comment: '经济类型id',
  })
  economic_id: number | null;

  @Column({
    type: 'varchar',
    nullable: true,
    length: 12,
    comment: '行业分类id',
  })
  industry_id: string | null;

  @Column({
    type: 'varchar',
    nullable: true,
    length: 10,
    comment: '行业分类国标GBZ编码',
  })
  gbz_industry_id: string | null;

  @Column({
    type: 'tinyint',
    nullable: true,
    comment: '职业病危害风险等级(0一般1严重)',
  })
  risk_level: number;

  @Column({ type: 'varchar', nullable: true, length: 50, comment: '法人姓名' })
  legal_rep: string;

  @Column({
    type: 'varchar',
    nullable: true,
    length: 20,
    comment: '法人联系电话',
  })
  legal_rep_phone: string;

  @Column({
    type: 'varchar',
    nullable: true,
    length: 50,
    comment: '职业卫生管理联系人',
  })
  contactor: string;

  @Column({
    type: 'varchar',
    nullable: true,
    length: 20,
    comment: '管理人联系电话',
  })
  contactor_phone: string;

  @Column({ type: 'int', nullable: true, comment: '在册职工总数' })
  employee_num: number;

  @Column({ type: 'int', nullable: true, comment: '职业病累计人数' })
  patient_num: number;

  @Column({ type: 'int', nullable: true, comment: '外委人员数' })
  outsourced_num: number | null;

  @Column({ type: 'varchar', nullable: true, length: 50, comment: '填报人' })
  filler: string;

  @Column({ type: 'varchar', nullable: true, length: 20, comment: '填报人联系电话' })
  filler_phone: string;

  @Column({ type: 'tinyint', nullable: true, comment: '是否军工涉密产品(1是0否)' })
  is_secure: number;

  @Column({ type: 'int', nullable: true, comment: '接害人员总数' })
  exposed_num: number;

  @Column({ type: 'int', nullable: true, comment: '有无主要负责人培训' })
  has_leader_training: number;

  @Column({ type: 'int', nullable: true, comment: '有无职业卫生管理人员培训' })
  has_manager_training: number;

  @Column({ type: 'int', nullable: true, comment: '接害劳动者培训人数' })
  employee_training_num: number;

  @Column({ type: 'tinyint', nullable: true, comment: '有无粉尘危害(0无1有)' })
  has_dust_hazard: number;

  @Column({ type: 'int', nullable: true, comment: '粉尘危害接害总人数' })
  dust_exposed_num: number | null;

  @Column({ type: 'tinyint', nullable: true, comment: '有无化学物质危害(0无1有)' })
  has_chem_hazard: number;

  @Column({ type: 'int', nullable: true, comment: '化学物质危害接害总人数' })
  chem_exposed_num: number | null;

  @Column({ type: 'tinyint', nullable: true, comment: '有无物理因素危害(0无1有)' })
  has_phys_hazard: number;

  @Column({ type: 'int', nullable: true, comment: '物理因素危害接害总人数' })
  phys_exposed_num: number | null;

  @Column({ type: 'tinyint', nullable: true, comment: '有无放射性因素危害(0无1有)' })
  has_radio_hazard: number;

  @Column({ type: 'int', nullable: true, comment: '放射性因素危害接害总人数' })
  radio_exposed_num: number | null;

  @Column({ type: 'tinyint', nullable: true, comment: '有无生物因素危害(0无1有)' })
  has_bio_hazard: number;

  @Column({ type: 'int', nullable: true, comment: '生物因素危害接害总人数' })
  bio_exposed_num: number | null;

  @Column({ type: 'tinyint', nullable: true, comment: '有无其它因素危害(0无1有)' })
  has_other_hazard: number;

  @Column({ type: 'int', nullable: true, comment: '其它因素危害接害总人数' })
  other_exposed_num: number | null;

  @Column({
    type: 'json',
    nullable: true,
    comment: '职业健康检查机构[{name:名字, report:报告编号, phone:联系方式, unified_code: 统一社会信用代码},...]',
  })
  exam_org: object | null;

  // @Column({ type: 'tinyint', comment: '体检总人数' })
  // exam_employee_num: number;
  
  @Column({
    type: 'json',
    nullable: true,
    comment: '体检表结论{prejob:[s,c,r,o,n],onjob:[s,c,r,o,n],offjob:[s,c,r,o,n]}',
  })
  exam_result: object | null;

  @Column({ type: 'tinyint', nullable: true, comment: '健康监护开展情况(1已开展0未开展)' })
  exam_status: number;

  @Column({ type: 'tinyint', nullable: true, comment: '有无粉尘体检(0无1有)' })
  has_dust_exam: number | null;

  @Column({ type: 'int', nullable: true, comment: '粉尘危害体检总人数' })
  dust_exam_num: number | null;

  @Column({
    type: 'tinyint',
    nullable: true,
    comment: '有无化学物质体检(0无1有)',
  })
  has_chem_exam: number | null;

  @Column({ type: 'int', nullable: true, comment: '化学物质危害体检总人数' })
  chem_exam_num: number | null;

  @Column({
    type: 'tinyint',
    nullable: true,
    comment: '有无物理因素体检(0无1有)',
  })
  has_phys_exam: number | null;

  @Column({ type: 'int', nullable: true, comment: '物理因素危害体检总人数' })
  phys_exam_num: number | null;

  @Column({
    type: 'tinyint',
    nullable: true,
    comment: '有无放射性因素体检(0无1有)',
  })
  has_radio_exam: number | null;

  @Column({ type: 'int', nullable: true, comment: '放射性因素危害体检总人数' })
  radio_exam_num: number | null;

  // @Column({ type: 'int', nullable: true, comment: '体检结果疑似职业病人数' })
  // suspected_num: number | null;

  // @Column({ type: 'int', nullable: true, comment: '体检结果职业禁忌证人数' })
  // prohibitive_num: number | null;

  // @Column({ type: 'int', nullable: true, comment: '体检结果提示复查人数' })
  // more_exam_num: number | null;

  // @Column({
  //   type: 'int',
  //   nullable: true,
  //   comment: '体检结果其它疾病或异常人数',
  // })
  // other_num: number | null;

  // @Column({ type: 'int', nullable: true, comment: '体检结果目前无异常人数' })
  // abnormal_num: number | null;

  @Column({
    type: 'tinyint',
    comment: '年度职业病危害因素检测类型(0未开展,1定期检测,2现状评价)',
    nullable: true,
  })
  detect_type: number;

  @Column({
    type: 'json',
    nullable: true,
    comment: '检测机构[{name:名字, report:报告编号, phone:联系方式, unified_code: 统一社会信用代码},...]',
  })
  detect_org: object | null;

  // @Column({ type: 'date', nullable: true, comment: '报告日期' })
  // detect_report_date: string | null;

  @Column({ type: 'tinyint', nullable: true, comment: '有无粉尘检测(0无1有)' })
  has_dust_detect: number | null;

  @Column({ type: 'int', nullable: true, comment: '粉尘危害检测点数' })
  dust_detect_points: number | null;

  @Column({ type: 'int', nullable: true, comment: '粉尘危害超标点数' })
  dust_overlimit_points: number | null;

  @Column({
    type: 'tinyint',
    nullable: true,
    comment: '有无化学物质检测(0无1有)',
  })
  has_chem_detect: number | null;

  @Column({ type: 'int', nullable: true, comment: '化学物质危害检测点数' })
  chem_detect_points: number | null;

  @Column({ type: 'int', nullable: true, comment: '化学物质危害超标点数' })
  chem_overlimit_points: number | null;

  @Column({
    type: 'tinyint',
    nullable: true,
    comment: '有无物理因素检测(0无1有)',
  })
  has_phys_detect: number | null;

  @Column({ type: 'int', nullable: true, comment: '物理因素危害检测点数' })
  phys_detect_points: number | null;

  @Column({ type: 'int', nullable: true, comment: '物理因素危害超标点数' })
  phys_overlimit_points: number | null;

  @Column({
    type: 'tinyint',
    nullable: true,
    comment: '有无放射性因素检测(0无1有)',
  })
  has_radio_detect: number | null;

  @Column({ type: 'int', nullable: true, comment: '放射性因素危害检测点数' })
  radio_detect_points: number | null;

  @Column({ type: 'int', nullable: true, comment: '放射性因素危害超标点数' })
  radio_overlimit_points: number | null;

  @Column({
    type: 'tinyint',
    nullable: true,
    comment: '有无生物和其它因素检测(0无1有)',
  })
  has_bio_other_detect: number | null;

  @Column({ type: 'int', nullable: true, comment: '生物和其它因素危害检测点数' })
  bio_other_detect_points: number | null;

  @Column({ type: 'int', nullable: true, comment: '生物和其它因素危害超标点数' })
  bio_other_overlimit_points: number | null;

  @Column({ type: 'datetime', comment: '初次填表时间' })
  start_time: Date;

  @Column({ type: 'int', comment: '编辑次数' })
  edit_num: number;

  @Column({ type: 'datetime', comment: '最后更新时间' })
  last_update_time: Date;

  @Column({ type: 'json', nullable: true, comment: '申报表盖章文件' })
  declaration_files: object | null;

  @Column({ type: 'varchar', length: 32, comment: 'uuid' })
  uuid: string;

  @Column({ type: 'int', nullable: true,  })
  last_audit_id: number;

  @Column({ type: 'tinyint', comment: '是否被删除 0否 1是', default: 0 })
  del_flag: number;

  @Column( {type:'tinyint', comment:'数据同步指标(0未完成申报闭环1已上报国家库2国家库下载获得)', default: 0} )
  sync_status:number | null;

  @OneToMany(() => ngAudit, (ngAudit) => ngAudit.declaration)
  ngAudit: ngAudit[];

  @OneToMany(() => ngRoster, (ngRoster) => ngRoster.declaration)
  roster: ngRoster[];

  @OneToOne(() => ngAudit, (ngAudit) => ngAudit.declaration)
  @JoinColumn({ name: 'last_audit_id', referencedColumnName: 'id' })
  latestAudit: ngAudit;

  @ManyToOne(() => NgEmployer)
  @JoinColumn({ name: 'unified_code', referencedColumnName: 'unifiedCode' })
  employer: NgEmployer;
}
