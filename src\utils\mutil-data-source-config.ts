import { DynamicModule } from '@nestjs/common';
import { TypeOrmModule, TypeOrmModuleOptions } from '@nestjs/typeorm';

export type DataSourcesType = {
  [key: string]: TypeOrmModuleOptions;
};

/**
 *  导入多个同构数据源配置
 *  1.  dataSources  Key  TypeOrmModuleOptions
 *  2.  entities  Typeorm  entity  (  Typeorm  entity  )
 *    dataSources  Key  TypeOrmModuleOptions  ,  entities   forRoot
 *    forRoot  TypeOrmModule   DynamicModule
 *    forRoot  TypeOrmModule   DynamicModule[]  ,  Nest  APP  TypeORM
 */
export function generateMultipleDataSourceConfig(
  dataSources: DataSourcesType,
  entities: string[],
): DynamicModule[] {
  const configs = dataSources
    ? Object.values(dataSources).map((dataSourceConfig) => {
        return TypeOrmModule.forRoot({
          ...dataSourceConfig,
          entities,
        });
      })
    : [];
  return configs;
}
