import { Entity, Column, ObjectIdColumn } from 'typeorm';
import { ObjectId } from 'mongodb';
import shortid from 'shortid';

class Station {
  @ObjectIdColumn({ default: () => shortid.generate() })
  _id: ObjectId;

  @Column(() => HarmFactor)
  harmFactors: HarmFactor[];
}

class HarmFactor {
  @ObjectIdColumn({ default: () => shortid.generate() })
  _id: ObjectId;

  @Column()
  sampleMode: string; // 采样方式

  @Column(() => Sample)
  sample: Sample[];
}

class Sample {
  @ObjectIdColumn({ default: () => shortid.generate() })
  _id: ObjectId;

  @Column()
  planSampleDate: Date;

  @Column()
  isCompleteConfirm: boolean;

  @Column()
  batch: number;

  @Column()
  subProjectsId: string;

  @Column()
  sampleSN: string;

  @Column()
  temperature: string; // 温度

  @Column()
  pressure: string; // 气压

  @Column()
  samplingFlow: string; // 采样流量 - 定点

  @Column()
  smaplingTimeStart: Date; // 采样起始时间

  @Column()
  smaplingTimeEnd: Date; // 采样结束时间

  @Column()
  samplingFlowStart: string; // 采样前流量 - 个体 (弃用)

  @Column()
  samplingFlowEnd: string; // 采样后流量 - 个体 (弃用)

  @Column(() => Lab)
  lab: Lab[];

  @Column(() => SampleArr)
  sampleArr: SampleArr[];
}

class SampleArr {
  @ObjectIdColumn({ default: () => shortid.generate() })
  _id: ObjectId;

  @Column()
  smaplingTimeStart: Date; // 采样起始时间

  @Column()
  smaplingTimeEnd: Date; // 采样结束时间

  @Column()
  samplingFlowStart: string; // 采样前流量

  @Column()
  samplingFlowEnd: string; // 采样后流量
}

class Lab {
  @ObjectIdColumn({ default: () => shortid.generate() })
  _id: ObjectId;

  @Column()
  harmFactor: string;

  @Column()
  Abs: string;

  @Column()
  uno_Abs: string;

  @Column()
  sample_content: string;

  @Column()
  sample_volume: string;

  @Column()
  conversion_coefficient: string;

  @Column()
  air_concentration: string;

  @Column()
  duration_of_exposure: string;

  @Column()
  comment: string;

  @Column()
  potential_value: string;

  @Column()
  sample_content_log: string;

  @Column()
  sample_content_ise: string;

  @Column()
  emptyFluorescenceIntensity: string;

  @Column()
  compared_emptyFluorescenceIntensity: string;

  @Column()
  sample_solution: string;

  @Column()
  sample_solution2: string;

  @Column()
  before_volume: string;

  @Column()
  after_volume: string;

  @Column()
  extra_volume: string;

  @Column()
  empty_beaker_weight: string;

  @Column()
  volatilize_sample_weigth: string;

  @Column()
  empty_beaker_weighting: string;

  @Column()
  peakArea_front: string;

  @Column()
  peakArea_back: string;

  @Column()
  peakArea_front2: string;

  @Column()
  peakArea_back2: string;

  @Column()
  subtract_peakArea: string;

  @Column()
  sample_concentration: string;

  @Column()
  sample_weight: string;

  @Column()
  crucible_weight: string;

  @Column()
  ashing_crucible_weight: string;

  @Column()
  acid_crucible_weight: string;

  @Column()
  SiO2_concentration: string;

  @Column()
  filter_concentration1: string;

  @Column()
  filter_concentration2: string;

  @Column()
  analysis_num: string;

  @Column()
  peakArea_IC: string;

  @Column()
  subtract_peakArea_IC: string;

  @Column()
  sample_concentration_IC: string;

  @Column()
  excursion_limit: string;

  @Column()
  logarithm: string;

  @Column()
  constant_volume: string;

  @Column()
  take_sample_volume: string;
}

class CreateProjectTime {
  @Column({ type: 'number' })
  status: number; // 0 未完成 1 进行中 2 已完成

  @Column({ type: 'date' })
  completedTime: Date;
}

class LabSampleList {
  @Column({ type: 'number' })
  status: number; // 0 未完成 1 进行中 2 已完成

  @Column({ type: 'date' })
  completedTime: Date;
}

class LabRecord {
  @Column({ type: 'number' })
  status: number; // 0 未完成 1 进行中 2 已完成

  @Column({ type: 'date' })
  completedTime: Date;
}

class LabTestReport {
  @Column({ type: 'number' })
  status: number; // 0 未完成 1 进行中 2 已完成

  @Column({ type: 'date' })
  completedTime: Date;
}

class ReportApproved {
  @Column({ type: 'number' })
  status: number; // 0 未完成 1 进行中 2 已完成

  @Column({ type: 'date' })
  completedTime: Date;
}

class Progress {
  @Column(() => CreateProjectTime)
  createProject_time: CreateProjectTime;

  @Column(() => LabSampleList)
  labSampleList: LabSampleList;

  @Column(() => LabRecord)
  labRecord: LabRecord;

  @Column(() => LabTestReport)
  labTestReport: LabTestReport;

  @Column(() => ReportApproved)
  reportApproved: ReportApproved;
}

class WordFileName {
  @ObjectIdColumn({ default: () => shortid.generate() })
  _id: ObjectId;

  @Column()
  resultseq: string;

  // TODO:  每条记录都有，暂时没有实际数据无法判断每条是不是都相同
  @Column()
  finstnos: string;

  @Column()
  finstnms: string;

  @Column()
  finstxhs: string;

  @Column()
  finstlxs: string;

  @Column()
  name: string;

  @Column()
  url: string;
}
class LabInstrument {
  @ObjectIdColumn({ default: () => shortid.generate() })
  _id: ObjectId;

  @Column()
  finstnos: string;
  @Column()
  finstnms: string;
  @Column()
  finstxhs: string;
  @Column()
  finstlxs: string;
}

class LabRecordFile {
  @Column()
  staticName: string;

  @Column()
  url: string;
}

@Entity('jcqlcProject')
export class JcqlcProject {
  @ObjectIdColumn({ default: () => shortid.generate() })
  _id: ObjectId;

  @Column()
  projectSN: string;

  @Column()
  allSampleAnalyseNumber: number;

  @Column()
  reportProcessInstanceId: string;

  @Column()
  allSampleNumber: number;

  @Column(() => Station)
  station: Station[];

  @Column(() => Progress)
  progress: Progress;

  @Column(() => LabRecordFile)
  labRecordFile: LabRecordFile;

  @Column(() => LabInstrument)
  chemistryLabInstrument: LabInstrument[]; // 仪器信息

  @Column(() => WordFileName)
  chemistryWordFileName: WordFileName[]; // 原始记录单信息
}
