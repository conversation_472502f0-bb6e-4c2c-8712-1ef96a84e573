import { Entity, Column, CreateDateColumn, UpdateDateColumn, BeforeInsert } from 'typeorm';
import * as shortid from 'shortid';

// 员工信息接口
interface Employee {
  _id: string;
  employeeId: string;
  confirmTransfer: boolean; // 转岗是否经过本人确认
}

// 点位信息接口
interface Station {
  _id: string;
  stationName: string; // 点位名称
  shiftContactTime: number; // 每班接触时间(h) 工作日写实记录
  workDayWeek: string; // 每周接触几天
  weeklyExposureHours: number; // 每周接触时间/h
  harmFactors: string[]; // 危害因素名称 不含总呼
  workWay: string; // 作业方式
  protectiveEquipment: string; // 个人防护用品
  protectiveFacilities: string; // 职业病防护设施
  equipCount: number; // 设备总数
}

/**
 * 企业车间工种点位信息表(新)
 * @Author: xxn
 * @Date: 2024-4-9
 * @LastEditors: xxn
 * @LastEditTime: 2024-4-9
 * @Description: 企业车间工种点位信息表(新)
 */
@Entity('workspace')
export class Workspace {
  @Column()
  _id: string;

  @Column({ comment: '企业id' })
  EnterpriseID: string;

  @Column({ comment: '检测机构导入', nullable: true })
  serviceOrgId?: string;

  @Column({ comment: '体检机构导入', nullable: true })
  physicalExamOrgId?: string;

  @Column({ comment: '是否经过企业确认：0-未确认，1-已确认', default: '0' })
  status: '0' | '1';

  @Column({ comment: '厂房名称', default: '' })
  workshopName: string;

  @Column({ comment: '车间名称' })
  workspaceName: string;

  @Column({ comment: '工种名称' })
  workTypeName: string;

  @Column({ comment: '作业(暴露)人数（手填）', nullable: true })
  exposedPeopleNumber?: number;

  @Column({ comment: '生产班制', nullable: true })
  dailyProduce?: string;

  @Column({ comment: '工种每周接触天数', nullable: true })
  workDays?: number;

  @Column({ comment: '工种每天接触时间/h', nullable: true })
  exposureHours?: number;

  @Column({ comment: '员工信息' })
  employees: Employee[] = [];

  @Column({ comment: '点位信息' })
  stations: Station[] = [];

  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date;

  @BeforeInsert()
  setDefaults() {
    if (!this._id) {
      this._id = shortid.generate();
    }
    const now = new Date();
    this.createdAt = now;
    this.updatedAt = now;
  }
}
