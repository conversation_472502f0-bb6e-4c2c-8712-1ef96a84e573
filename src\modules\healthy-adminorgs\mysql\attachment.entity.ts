import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Declaration } from './declaration.entity';

@Entity('attachment')
export class Attachment {
  @PrimaryGeneratedColumn({ name: 'id', type: 'int', comment: '自增ID' })
  id: number;

  @Column({ comment: '申报id', type: 'int' })
  declaration_id: number;

  @Column({ comment: '文件名', type: 'varchar' })
  file_name: string;

  @Column({ comment: '文件存贮名称', type: 'varchar' })
  static_name: string;

  @Column({
    type: 'enum',
    enum: ['0', '1'],
    comment: '文件类型: 0 承诺书 1 申请表',
  })
  file_type: string;

  @ManyToOne(() => Declaration, (declaration) => declaration.attachment)
  @JoinColumn({ name: 'declaration_id' })
  declaration: Declaration;
}
