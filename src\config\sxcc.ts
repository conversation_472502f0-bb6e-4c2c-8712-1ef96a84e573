export default () => ({
  sxccCornInterval: '0 0 0 * * *', // sxcc焦煤定时任务
  sxccBaseUrl: process.env.sxccBaseUrl || '', // sxcc接口地址
  isGetSxccData: process.env.isGetSxccData || '0', // 是否获取sxcc数据
  isFullUpdateSxcc: process.env.isFullUpdateSxcc || '0', // 是否全量更新
  database_mongodb: {
    name: 'mongodbConnection',
    type: 'mongodb',
    hostReplicaSet: process.env.mdbHostRs,
    port: +process.env.mdbPort || 27017,
    replicaSet: process.env.mdbRs,
    readPreference: 'nearest',
    ssl: false,
    useNewUrlParser: true,
    useUnifiedTopology: true,
    authSource: 'admin',
    synchronize: false,
    username: process.env.mdbUser,
    password: process.env.mdbPass,
    database: process.env.mdbName,
    logging: 'all',
    autoLoadEntities: false,
  },
});
