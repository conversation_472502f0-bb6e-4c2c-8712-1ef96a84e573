import { Entity, Column, ObjectIdColumn, BeforeInsert } from 'typeorm';
// import { ObjectId } from 'mongodb';
import * as shortid from 'shortid';
/**
 * 诊断记录实体
 * 包含用人单位名称、用人单位统一社会信用代码、用工单位名称、用工单位统一社会信用代码、
 * 诊断结论、处理意见、诊断机构、诊断日期、劳动者名称、身份证、诊断编号等信息
 */
@Entity('diagnosticRecords')
export class DiagnosticRecord {
  @ObjectIdColumn()
  _id: string;

  @Column({ type: 'varchar', comment: '用人单位名称' })
  employerName: string;

  @Column({ type: 'varchar', comment: '用人单位统一社会信用代码', nullable: true })
  employerCreditCode: string;

  @Column({ type: 'varchar', comment: '用工单位名称', nullable: true })
  laborEmployerName: string;

  @Column({ type: 'varchar', comment: '用工单位统一社会信用代码', nullable: true })
  laborEmployerCreditCode: string;

  @Column({ type: 'boolean', comment: '诊断结论，false代表不是职业病，true代表是职业病' })
  hasOccupationalDisease: boolean;

  @Column({ type: 'varchar', comment: '诊断结论描述', nullable: true })
  diagnosisConclusionDescription: string;

  @Column({ nullable: true })
  occupationalDisease: { name: string; code: string }[] = [];  // 默认空数组

  @Column({ type: 'varchar', comment: '处理意见', nullable: true })
  treatmentOpinion: string;

  @Column({ type: 'varchar', comment: '诊断机构' })
  diagnosisInstitution: string;

  @Column({ type: 'date', comment: '诊断日期' })
  diagnosisDate: Date;

  @Column({ type: 'varchar', comment: '劳动者名称' })
  workerName: string;

  @Column({ type: 'varchar', comment: '手机号', nullable: true })
  phone: string;

  @Column({
    type: 'enum',
    enum: ['1', '2'],
    comment: '性别 1男 2女',
    nullable: true
  })
  gender: string;

  @Column({ type: 'varchar', comment: '身份证号码' })
  idNumber: string;

  @Column({ type: 'varchar', comment: '诊断编号', unique: true })
  diagnosisNumber: string;

  @Column({ type: 'varchar', comment: '诊断证明书附件地址' })
  certificateUrl: string;

  @Column({ nullable: true })
  fileList: { name: string; url: string }[] = [];

  @Column({ type: 'date', default: () => new Date(), comment: '创建时间' })
  createdAt: Date;

  @Column({ type: 'date', default: () => new Date(), comment: '更新时间' })
  updatedAt: Date;

  @BeforeInsert()
  setDefaultId() {
    if (!this._id) {
      this._id = shortid.generate();
    }
  }
}
