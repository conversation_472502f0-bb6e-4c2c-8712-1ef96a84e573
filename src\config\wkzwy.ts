export default () => ({
  database_mongodb: {
    name: 'mongodbConnection',
    type: 'mongodb',
    hostReplicaSet: process.env.mdbHostRs,
    port: +process.env.mdbPort || 27017,
    replicaSet: process.env.mdbRs,
    readPreference: 'nearest',
    ssl: false,
    useNewUrlParser: true,
    useUnifiedTopology: true,
    authSource: 'admin',
    synchronize: false,
    username: process.env.mdbUser,
    password: process.env.mdbPass,
    database: process.env.mdbName,
    logging: 'all',
    autoLoadEntities: false,
  },
  // 乐清虹丰医院
  database_sqlserver: {
    name: 'sqlserverConnection',
    type: 'mssql',
    host: process.env.hfSqlserverHost,
    port: +process.env.hfSqlserverPort,
    username: process.env.hfSqlserverUser,
    password: process.env.hfSqlserverPass,
    database: process.env.hfSqlserverDb,
    logging: 'all',
    options: {
      encrypt: false,
      // trustServerCertificate: true,
      // cryptoCredentialsDetails: {
      //   minVersion: 'TLSv1.2',
      //   ciphers: 'ECDHE-RSA-AES256-SHA384',
      // },
    },
    synchronize: false,
  },
});
