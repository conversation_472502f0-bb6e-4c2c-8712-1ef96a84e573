export default () => ({
  port: parseInt(process.env.PORT, 10) || 3000,

  database_mongodb: {
    name: 'mongodbConnection',
    type: 'mongodb',
    hostReplicaSet: process.env.mdbHostRs,
    port: +process.env.mdbPort || 27017,
    replicaSet: process.env.mdbRs,
    readPreference: 'nearest',
    ssl: false,
    useNewUrlParser: true,
    useUnifiedTopology: true,
    authSource: 'admin',
    username: process.env.mdbUser,
    password: process.env.mdbPass,
    database: process.env.mdbName,
    logging: 'info',
    synchronize: false,
    autoLoadEntities: false,
  },
  // redis通用数据库配置
  database_redis: {
    name: 'redisConnection',
    type: 'redis',
    host: process.env.redis_host || 'valkey.valkey.svc',
    port: +process.env.redis_port || 6379,
    password: process.env.redis_pass || '',
    db: process.env.redis_db || 0,
    connectTimeout: 10000, // 超时时间: 10s
    retryStrategy: (times: number) => Math.min(times * 50, 2000), // 重试策略
  },

  fzdpCornInterval: '*/10 * * * *', // 福州大屏定时任务
  fzlabCornInterval: '*/10 * * * *', // 福州数据库同步定时任务
  enterprise_http_path: '/enterprise',
  fzOrganization: '123501004880998676', //福州在数据库内编号 => 查机构id
  // 预警配置 enableUploadFiles: 企业是否可自行上传整改资料; releaseWarningBySelf: 企业是否可自行解除预警
  warningConfig: [
    { level: 1, enableUploadFiles: true, releaseWarningBySelf: false },
    { level: 2, enableUploadFiles: true, releaseWarningBySelf: false },
    { level: 3, enableUploadFiles: true, releaseWarningBySelf: false },
    { level: 4, enableUploadFiles: true, releaseWarningBySelf: true },
  ],
  groupID: {
    adminGroupID: 'vGEfBpfsv', // 企业用户角色ID
    userGroupID: 'V7au7L2Rw', // 劳动者用户角色ID
  },
  salt_sha2_key: '7a58917b-d493-4940-87d4-ab2b0fa8a3b7',
  jwt: {
    secret: process.env.JWT_SECRET || '243d477d-d6bb-4ab1-b0a9-a1b1c9419562',
    expiresIn: process.env.JWT_EXPIRES_IN || '60s',
  },
  domainNames: {
    wj: 'https://wj.zyws.cn',
  },
  report_path: '/opt/public/report',
});
