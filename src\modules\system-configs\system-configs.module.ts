import { Module } from '@nestjs/common';
import { SystemConfigsService } from './system-configs.service';
import { SystemConfigsController } from './system-configs.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SystemConfigs } from './system-configs.entity';

@Module({
  imports: [TypeOrmModule.forFeature([SystemConfigs], 'mongodbConnection')],
  providers: [SystemConfigsService],
  controllers: [SystemConfigsController],
  exports: [SystemConfigsService],
})
export class SystemConfigsModule {}
