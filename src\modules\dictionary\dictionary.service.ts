import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DictArea } from './mysql/dict_area.entity';

@Injectable()
export class DictionaryService {
  constructor(
    @InjectRepository(DictArea, 'mysqlConnectionIservice')
    private readonly dictAreaRepository: Repository<DictArea>,
  ) {}
  // 学历字典
  async education() {
    return [
      // { id: 1, name: '小学' },
      // { id: 2, name: '初中' },
      { id: 3, name: '高中（含中专）及以下' },
      { id: 4, name: '专科（大专）' },
      { id: 5, name: '本科' },
      { id: 6, name: '硕士研究生' },
      { id: 7, name: '博士研究生' },
    ];
  }

  async getIDtype() {
    return [
      { id: 1, name: '身份证' },
      { id: 2, name: '护照' },
      { id: 3, name: '驾驶证' },
      { id: 4, name: '军官证' },
      { id: 5, name: '港澳通行证' },
      { id: 6, name: '台胞证' },
      { id: 7, name: '户口簿' },
      { id: 8, name: '居住证' },
      { id: 9, name: '其他' },
    ];
  }

  // 获取子集地区编码
  async getAreaCode(parentCode: string = '0') {
    const areas = await this.dictAreaRepository.find({
      where: { parent_code: parentCode },
    });
    return areas.map((area) => ({
      code: area.code,
      name: area.abbr_name,
    }));
  }
  // 根据code获取完整的地区名称，如：['广东省', '深圳市', '南山区']
  async getAreaInfo(code: string) {
    const area = await this.dictAreaRepository.findOne({ where: { code } });
    if (!area) {
      return [];
    }
    const areaInfo = [area.abbr_name];
    let parentCode = area.parent_code;
    while (parentCode !== '0') {
      const parentArea = await this.dictAreaRepository.findOne({
        where: { code: parentCode },
      });
      if (!parentArea) {
        break;
      }
      areaInfo.unshift(parentArea.abbr_name);
      parentCode = parentArea.parent_code;
    }
    return areaInfo;
  }
}
